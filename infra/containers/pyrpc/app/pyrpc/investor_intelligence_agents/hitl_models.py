"""
Human-in-the-Loop Models

Data models for human approval workflows in the Intelligence Agent system.
"""
from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field


class ApprovalStatus(str, Enum):
    """Status of human approval requests."""
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    MODIFIED = "MODIFIED"
    TIMEOUT = "TIMEOUT"


class ApprovalType(str, Enum):
    """Types of approval requests."""
    BRIEF_GENERATION = "BRIEF_GENERATION"
    DATA_EXTRACTION = "DATA_EXTRACTION"
    MARKET_ANALYSIS = "MARKET_ANALYSIS"
    FINAL_BRIEF = "FINAL_BRIEF"
    SENSITIVE_DATA = "SENSITIVE_DATA"


class ApprovalRequest(BaseModel):
    """Request for human approval."""
    id: str = Field(description="Unique approval request ID")
    type: ApprovalType = Field(description="Type of approval being requested")
    title: str = Field(description="Human-readable title for the approval")
    description: str = Field(
        description="Detailed description of what needs approval")
    data: Dict[str, Any] = Field(description="Data that needs approval")
    context: Dict[str, Any] = Field(
        description="Additional context for the approval")
    requester: str = Field(description="Agent or system requesting approval")
    created_at: datetime = Field(default_factory=datetime.now)
    expires_at: Optional[datetime] = Field(
        description="When this approval expires")
    priority: str = Field(
        default="NORMAL", description="Priority level: LOW, NORMAL, HIGH, CRITICAL")


class ApprovalResponse(BaseModel):
    """Response from human approver."""
    request_id: str = Field(description="ID of the approval request")
    status: ApprovalStatus = Field(description="Approval decision")
    feedback: Optional[str] = Field(
        default=None, description="Human feedback or comments")
    modifications: Optional[Dict[str, Any]] = Field(
        default=None, description="Requested modifications to the data")
    approver: str = Field(description="ID or name of the approver")
    approved_at: datetime = Field(default_factory=datetime.now)
    notes: Optional[str] = Field(
        default=None, description="Additional notes from approver")


class HITLCheckpoint(BaseModel):
    """Configuration for a human-in-the-loop checkpoint."""
    name: str = Field(description="Name of the checkpoint")
    description: str = Field(
        description="Description of what this checkpoint validates")
    approval_type: ApprovalType = Field(
        description="Type of approval required")
    required: bool = Field(
        default=True, description="Whether approval is mandatory")
    timeout_seconds: int = Field(
        default=3600, description="Timeout for approval in seconds")
    auto_approve_conditions: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Conditions under which auto-approval is allowed"
    )


class HITLWorkflowConfig(BaseModel):
    """Configuration for HITL workflows."""
    enabled: bool = Field(default=True, description="Whether HITL is enabled")
    checkpoints: List[HITLCheckpoint] = Field(
        description="List of approval checkpoints")
    default_timeout: int = Field(
        default=3600, description="Default timeout in seconds")
    auto_approve_low_risk: bool = Field(
        default=False, description="Auto-approve low-risk operations")
    require_dual_approval: bool = Field(
        default=False, description="Require two approvers for critical operations")


class HITLSession(BaseModel):
    """Session tracking for HITL workflows."""
    session_id: str = Field(description="Unique session ID")
    workflow_type: str = Field(
        description="Type of workflow (e.g., intelligence_brief)")
    state_snapshot: Dict[str, Any] = Field(
        description="Snapshot of system state")
    pending_approvals: List[str] = Field(
        description="List of pending approval request IDs")
    completed_approvals: List[str] = Field(
        description="List of completed approval request IDs")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional session metadata")
