"""
Intelligence Agent Main Entry Point

Main interface for the Intelligence Agent system. Provides the primary
function for generating investor intelligence briefs with optional
Human-in-the-Loop approval workflows.
"""
import logging
from typing import AsyncGenerator, Dict, List, Optional
from pyrpc.rag_response.new_llm import ModelHyperparameters
from pyrpc.models import RAGResponseWithCitations
from .intelligence_graph import IntelligenceGraph
from .hitl_intelligence_graph import HITLIntelligenceGraph
from .hitl_models import HITLWorkflowConfig


async def generate_intelligence_brief(
    user_message: str,
    org_info: Dict,
    investor_name: Optional[str] = None,
    investor_id: Optional[str] = None,
    model_hyperparams: ModelHyperparameters = None,
    enable_hitl: bool = False,
    hitl_config: Optional[HITLWorkflowConfig] = None,
    **kwargs
) -> AsyncGenerator[RAGResponseWithCitations, None]:
    """
    Generate an intelligence brief for a specific investor.
    
    This is the main entry point for the Intelligence Agent system. It orchestrates
    the multi-agent workflow to produce comprehensive investor intelligence briefs
    with optional Human-in-the-Loop approval workflows.
    
    Args:
        user_message: The user's request (e.g., "Generate brief for CalPERS")
        org_info: Organization information including institutionName and orgId
        investor_name: Optional specific investor name to analyze
        investor_id: Optional specific investor ID to analyze
        model_hyperparams: LLM model configuration parameters
        enable_hitl: Whether to enable Human-in-the-Loop approval workflows
        hitl_config: Optional HITL workflow configuration
        **kwargs: Additional parameters including trace_id, session_id, etc.
    
    Yields:
        RAGResponseWithCitations: Streaming responses with status updates and final brief
    
    Example:
        ```python
        # Standard intelligence brief
        async for response in generate_intelligence_brief(
            user_message="Generate brief for CalPERS",
            org_info={"institutionName": "Example Fund", "orgId": "org123"},
            trace_id="trace123"
        ):
            print(f"Status: {response.status}, Answer: {response.answer}")
        
        # Intelligence brief with human approval workflows
        async for response in generate_intelligence_brief(
            user_message="Generate brief for CalPERS",
            org_info={"institutionName": "Example Fund", "orgId": "org123"},
            enable_hitl=True,
            trace_id="trace123"
        ):
            print(f"Status: {response.status}, Answer: {response.answer}")
        ```
    """
    
    # Initialize model hyperparameters if not provided
    if model_hyperparams is None:
        model_hyperparams = ModelHyperparameters()
    
    # Extract investor information from user message if not explicitly provided
    if not investor_name and "brief for" in user_message.lower():
        # Simple extraction - in production would use more sophisticated NER
        try:
            investor_name = user_message.lower().split("brief for")[-1].strip()
            # Clean up common suffixes
            investor_name = investor_name.replace("?", "").replace(".", "").strip()
        except Exception as e:
            logging.warning(f"Could not extract investor name from message: {e}")
    
    # Choose the appropriate graph based on HITL requirements
    if enable_hitl:
        # Use HITL-enabled intelligence graph
        intelligence_graph = HITLIntelligenceGraph(model_hyperparams, hitl_config)
        
        async for response in intelligence_graph.generate_intelligence_brief_with_hitl(
            user_message=user_message,
            investor_name=investor_name,
            investor_id=investor_id,
            org_info=org_info,
            hitl_config=hitl_config,
            **kwargs
        ):
            yield response
    else:
        # Use standard intelligence graph
        intelligence_graph = IntelligenceGraph(model_hyperparams)
        
        async for response in intelligence_graph.generate_intelligence_brief(
            user_message=user_message,
            investor_name=investor_name,
            investor_id=investor_id,
            org_info=org_info,
            **kwargs
        ):
            yield response


# Convenience function for testing and direct usage
async def test_intelligence_brief(investor_name: str = "CalPERS") -> None:
    """
    Test function for the Intelligence Agent system.
    
    Args:
        investor_name: Name of investor to generate brief for
    """
    
    test_org_info = {
        "institutionName": "Test Investment Fund",
        "orgId": "test_org_123"
    }
    
    user_message = f"Generate intelligence brief for {investor_name}"
    
    print(f"Generating intelligence brief for {investor_name}...")
    print("=" * 60)
    
    async for response in generate_intelligence_brief(
        user_message=user_message,
        org_info=test_org_info,
        investor_name=investor_name,
        trace_id="test_trace_123"
    ):
        print(f"[{response.status}] {response.answer[:100]}...")
        
        if response.status == "READY":
            print("\n" + "=" * 60)
            print("FINAL BRIEF:")
            print("=" * 60)
            print(response.answer)
            break


if __name__ == "__main__":
    import asyncio
    
    # Run test
    asyncio.run(test_intelligence_brief("Ontario Teachers"))