"""
HITL Intelligence Graph

Intelligence Agent Graph with Human-in-the-Loop approval workflows integrated
throughout the multi-agent system.
"""
import logging
from typing import AsyncGenerator, Dict, List, Optional
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langgraph.constants import END, START
from langgraph.graph import StateGraph
from pyrpc.rag_response.new_llm import ModelHyperparameters
from pyrpc.models import RAGResponseWithCitations, ChatMessageStatus, Citation
from .state import IntelligenceState
from .supervisor import SupervisorAgent
from .hitl_knowledge_agent import HITLKnowledgeAgent
from .market_agent import MarketAgent
from .hitl_reporting_agent import HITLReportingAgent
from .hitl_manager import HITLManager
from .hitl_models import HITLWorkflowConfig


class HITLIntelligenceGraph:
    """
    HITL Intelligence Graph
    
    Orchestrates the multi-agent intelligence system with human-in-the-loop
    approval workflows using LangGraph.
    """
    
    def __init__(self, model_hyperparams: ModelHyperparameters = None, hitl_config: HITLWorkflowConfig = None):
        self.model_hyperparams = model_hyperparams or ModelHyperparameters()
        
        # Initialize HITL manager
        self.hitl_manager = HITLManager(hitl_config)
        
        # Initialize agents with HITL support
        self.supervisor = SupervisorAgent(self.model_hyperparams)
        self.knowledge_agent = HITLKnowledgeAgent(self.model_hyperparams, self.hitl_manager)
        self.market_agent = MarketAgent(self.model_hyperparams)  # Market agent doesn't need HITL for now
        self.reporting_agent = HITLReportingAgent(self.model_hyperparams, self.hitl_manager)
        
        # Build the graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow with HITL checkpoints."""
        
        # Create the state graph
        workflow = StateGraph(IntelligenceState)
        
        # Add nodes for each agent
        workflow.add_node("supervisor", self._supervisor_node)
        workflow.add_node("knowledge_agent", self._hitl_knowledge_node)
        workflow.add_node("market_agent", self._market_node)
        workflow.add_node("reporting_agent", self._hitl_reporting_node)
        workflow.add_node("hitl_checkpoint", self._hitl_checkpoint_node)
        
        # Define the workflow edges
        workflow.add_edge(START, "supervisor")
        
        # Conditional edges from supervisor to expert agents
        workflow.add_conditional_edges(
            "supervisor",
            self._route_from_supervisor,
            {
                "knowledge_agent": "knowledge_agent",
                "market_agent": "market_agent", 
                "reporting_agent": "reporting_agent",
                "hitl_checkpoint": "hitl_checkpoint",
                "__end__": END
            }
        )
        
        # HITL agents return to supervisor for next routing decision
        workflow.add_edge("knowledge_agent", "supervisor")
        workflow.add_edge("market_agent", "supervisor")
        workflow.add_edge("reporting_agent", END)  # Final step
        workflow.add_edge("hitl_checkpoint", "supervisor")
        
        return workflow.compile()
    
    async def _supervisor_node(self, state: IntelligenceState) -> IntelligenceState:
        """Supervisor node with HITL session management."""
        try:
            # Create HITL session if not exists
            if not state.get("hitl_session_id"):
                session = await self.hitl_manager.create_session(
                    workflow_type="intelligence_brief",
                    initial_state=dict(state)
                )
                state = {**state, "hitl_session_id": session.session_id}
            
            # Update session with current state
            await self.hitl_manager.update_session(
                state["hitl_session_id"], 
                dict(state)
            )
            
            return await self.supervisor.process(state)
            
        except Exception as e:
            logging.error(f"HITL Supervisor node error: {e}")
            return {
                **state,
                "classification": "error",
                "confidence": 0.0
            }
    
    async def _hitl_knowledge_node(self, state: IntelligenceState) -> IntelligenceState:
        """HITL Knowledge agent node with approval workflows."""
        try:
            return await self.knowledge_agent.process(state)
        except Exception as e:
            logging.error(f"HITL Knowledge agent node error: {e}")
            return state
    
    async def _market_node(self, state: IntelligenceState) -> IntelligenceState:
        """Market agent node (no HITL for now)."""
        try:
            return await self.market_agent.process(state)
        except Exception as e:
            logging.error(f"Market agent node error: {e}")
            return state
    
    async def _hitl_reporting_node(self, state: IntelligenceState) -> IntelligenceState:
        """HITL Reporting agent node with approval workflows."""
        try:
            return await self.reporting_agent.process(state)
        except Exception as e:
            logging.error(f"HITL Reporting agent node error: {e}")
            return state
    
    async def _hitl_checkpoint_node(self, state: IntelligenceState) -> IntelligenceState:
        """Generic HITL checkpoint node for additional approvals."""
        try:
            # This node can handle additional approval workflows
            # For now, it just passes through
            logging.info("Processing HITL checkpoint")
            return state
        except Exception as e:
            logging.error(f"HITL checkpoint node error: {e}")
            return state
    
    def _route_from_supervisor(self, state: IntelligenceState) -> str:
        """Enhanced routing with HITL considerations."""
        try:
            # Check if we need additional HITL checkpoints
            pending_approvals = state.get("pending_approvals", [])
            if pending_approvals:
                return "hitl_checkpoint"
            
            # Use standard supervisor routing
            return self.supervisor.route_request(state)
            
        except Exception as e:
            logging.error(f"HITL Routing error: {e}")
            return "__end__"
    
    async def generate_intelligence_brief_with_hitl(
        self,
        user_message: str,
        investor_name: Optional[str] = None,
        investor_id: Optional[str] = None,
        org_info: Optional[Dict] = None,
        hitl_config: Optional[HITLWorkflowConfig] = None,
        **kwargs
    ) -> AsyncGenerator[RAGResponseWithCitations, None]:
        """
        Generate intelligence brief with human-in-the-loop approval workflows.
        """
        
        trace_id = kwargs.get("trace_id")
        
        # Update HITL configuration if provided
        if hitl_config:
            self.hitl_manager.config = hitl_config
        
        try:
            # Initialize state with HITL fields
            initial_state: IntelligenceState = {
                "messages": [HumanMessage(content=user_message)],
                "classification": "",
                "confidence": 0.0,
                "analysis_results": {},
                "investor_id": investor_id,
                "investor_name": investor_name,
                "investor_data": None,
                "external_data": None,
                "brief_sections": None,
                "citations": None,
                "hitl_session_id": None,
                "pending_approvals": [],
                "approval_required": True,
                "hitl_checkpoints": []
            }
            
            # Track processing steps
            step_count = 0
            current_agent = "supervisor"
            
            # Stream the graph execution
            async for state_update in self.graph.astream(initial_state):
                step_count += 1
                
                # Get the current state
                current_state = list(state_update.values())[0] if state_update else initial_state
                current_node = list(state_update.keys())[0] if state_update else "supervisor"
                
                # Determine status based on current processing
                if current_node == "knowledge_agent":
                    status = ChatMessageStatus.RETRIEVING
                    message = "Gathering investor data with approval workflows..."
                elif current_node == "market_agent":
                    status = ChatMessageStatus.GENERATING_ANSWER
                    message = "Analyzing market conditions..."
                elif current_node == "reporting_agent":
                    status = ChatMessageStatus.GENERATING_CITATIONS
                    message = "Generating brief and awaiting final approval..."
                elif current_node == "hitl_checkpoint":
                    status = ChatMessageStatus.VALIDATING_CITATIONS
                    message = "Processing human approval checkpoint..."
                elif current_node == "supervisor":
                    status = ChatMessageStatus.GENERATING_ANSWER
                    message = f"Orchestrating intelligence gathering with approvals (Step {step_count})..."
                else:
                    status = ChatMessageStatus.GENERATING_ANSWER
                    message = "Processing intelligence request with human oversight..."
                
                # Add HITL status information
                hitl_checkpoints = current_state.get("hitl_checkpoints", [])
                if hitl_checkpoints:
                    checkpoint_info = f" | Completed checkpoints: {', '.join(hitl_checkpoints)}"
                    message += checkpoint_info
                
                # Check if we have a final brief
                analysis_results = current_state.get("analysis_results", {})
                if analysis_results.get("brief_generated"):
                    final_brief = analysis_results.get("final_brief", {})
                    brief_answer = final_brief.get("answer", "Brief generation completed.")
                    
                    # Use citations from state (Citation objects) instead of final_brief (CitationFinalAnwser objects)
                    state_citations = current_state.get("citations", [])
                    
                    # Add approval information to the response
                    approval_metadata = analysis_results.get("approval_metadata", {})
                    if approval_metadata.get("approved"):
                        brief_answer += f"\n\n✅ **Approved by:** {approval_metadata.get('approver', 'Unknown')}"
                        brief_answer += f"\n📅 **Approval Date:** {approval_metadata.get('approval_date', 'Unknown')}"
                    
                    yield RAGResponseWithCitations(
                        answer=brief_answer,
                        citations=state_citations,
                        status=ChatMessageStatus.READY,
                        traceId=trace_id
                    )
                    return
                
                # Yield intermediate status
                yield RAGResponseWithCitations(
                    answer=message,
                    citations=[],
                    status=status,
                    traceId=trace_id
                )
                
                # Safety check to prevent infinite loops
                if step_count > 15:  # Increased limit for HITL workflows
                    yield RAGResponseWithCitations(
                        answer="Intelligence brief generation with approvals exceeded maximum steps. Please try again.",
                        citations=[],
                        status=ChatMessageStatus.ERROR,
                        traceId=trace_id
                    )
                    return
            
            # If we reach here without a final brief, something went wrong
            yield RAGResponseWithCitations(
                answer="Intelligence brief generation with approvals completed but no final output was produced.",
                citations=[],
                status=ChatMessageStatus.ERROR,
                traceId=trace_id
            )
            
        except Exception as e:
            logging.error(f"HITL Intelligence graph execution failed: {e}")
            yield RAGResponseWithCitations(
                answer=f"Failed to generate intelligence brief with approvals: {str(e)}",
                citations=[],
                status=ChatMessageStatus.ERROR,
                traceId=trace_id
            )
    
    def get_pending_approvals(self) -> List:
        """Get all pending approval requests."""
        return self.hitl_manager.get_pending_approvals()
    
    async def submit_approval(self, approval_response) -> bool:
        """Submit human approval response."""
        return await self.hitl_manager.submit_approval(approval_response)
    
    def get_hitl_session(self, session_id: str):
        """Get HITL session information."""
        return self.hitl_manager.get_session(session_id)