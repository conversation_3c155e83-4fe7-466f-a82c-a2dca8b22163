#!/bin/bash

# Test script for Intelligence Brief endpoint using curl
# Make sure the FastAPI server is running on localhost:8000

echo "Testing Intelligence Brief Endpoint with curl"
echo "=============================================="

# Test payload
cat > /tmp/intelligence_test_payload.json << 'EOF'
{
  "user_message": "Generate intelligence brief for CalPERS",
  "context": {
    "institutionName": "Test Investment Management",
    "orgId": "test_org_001"
  },
  "modelParameters": {
    "model": "gpt-4o-mini",
    "temperature": 0.0,
    "top_p": 1e-9,
    "thinking_mode": false,
    "agentic_mode_langgraph": false,
    "citation_verification_mode": false,
    "answer_reflexion_mode": false
  },
  "tracingContext": {
    "traceId": "curl_test_123",
    "sessionId": "curl_session_123"
  },
  "investor_name": "CalPERS"
}
EOF

echo "Payload:"
cat /tmp/intelligence_test_payload.json
echo ""
echo "Sending request..."
echo "=================="

# Send the request and stream the response
curl -X POST \
  -H "Content-Type: application/json" \
  -d @/tmp/intelligence_test_payload.json \
  http://localhost:8000/pyrpc/generate-answer/intelligence-brief \
  --no-buffer \
  -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n"

# Clean up
rm -f /tmp/intelligence_test_payload.json

echo ""
echo "Test completed!"
echo "If you see streaming JSON responses above, the endpoint is working correctly."