"""
HITL-Aware Reporting Agent

Reporting agent with human approval workflows for final brief review and approval.
"""
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from langchain_core.messages import AIMessage, SystemMessage
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters
from pyrpc.models import RAGResponseWithCitationsFinalAnwser, CitationFinalAnwser
from .state import IntelligenceState
from .reporting_agent import ReportingAgent
from .hitl_manager import H<PERSON>LManager
from .hitl_models import ApprovalType, ApprovalStatus


class HITLReportingAgent(ReportingAgent):
    """
    HITL-Aware Reporting Agent
    
    Extends the Reporting Agent with human approval workflows for final brief review.
    """
    
    def __init__(self, model_hyperparams: ModelHyperparameters = None, hitl_manager: HITLManager = None):
        super().__init__(model_hyperparams)
        self.hitl_manager = hitl_manager or HITLManager()
    
    async def process_with_approval(self, state: IntelligenceState) -> IntelligenceState:
        """Process brief generation with human approval for final output."""
        
        messages = state["messages"]
        investor_data = state.get("investor_data", {})
        external_data = state.get("external_data", {})
        analysis_results = state.get("analysis_results", {})
        
        if not investor_data or not external_data:
            error_msg = "Insufficient data for brief generation. Both investor and market data required."
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {**analysis_results, "reporting_error": error_msg}
            }
        
        try:
            # Step 1: Generate the intelligence brief (as before)
            brief_content = self.generate_brief_template(investor_data, external_data)
            
            # Step 2: Analyze brief for approval requirements
            brief_analysis = self._analyze_brief_content(brief_content, investor_data)
            
            # Step 3: Create approval request for final brief
            approval_request = await self.hitl_manager.create_approval_request(
                approval_type=ApprovalType.FINAL_BRIEF,
                title=f"Intelligence Brief Approval: {investor_data.get('name', 'Unknown')}",
                description=f"Review and approve intelligence brief for delivery",
                data={
                    "brief_content": brief_content,
                    "word_count": brief_analysis["word_count"],
                    "investor_name": investor_data.get("name"),
                    "brief_analysis": brief_analysis,
                    "confidence": state.get("confidence", 0.0)
                },
                context={
                    "investor_data": investor_data,
                    "external_data": external_data,
                    "agent": "reporting_agent"
                },
                priority="HIGH"
            )
            
            # Step 4: Wait for human approval
            approval_response = await self.hitl_manager.wait_for_approval(approval_request)
            
            if approval_response.status == ApprovalStatus.REJECTED:
                error_msg = f"Intelligence brief rejected: {approval_response.feedback}"
                return {
                    **state,
                    "messages": messages + [AIMessage(content=error_msg)],
                    "analysis_results": {**analysis_results, "reporting_error": error_msg}
                }
            
            # Step 5: Apply modifications if requested
            final_brief_content = brief_content
            if approval_response.status == ApprovalStatus.MODIFIED and approval_response.modifications:
                final_brief_content = self._apply_brief_modifications(
                    brief_content, approval_response.modifications
                )
            
            # Step 6: Generate citations and finalize
            data_sources = []
            data_sources.extend(analysis_results.get("investor_data", {}).get("data_sources", []))
            data_sources.extend(external_data.get("data_sources", []))
            
            citations_str = self.generate_citations(final_brief_content, data_sources)
            citations_data = json.loads(citations_str)
            
            structured_citations = []
            for citation in citations_data:
                structured_citation = CitationFinalAnwser(
                    quote=citation.get("quote", ""),
                    document_chunk_id=citation.get("document_chunk_id")
                )
                structured_citations.append(structured_citation)
            
            # Step 7: Create final response with approval metadata
            final_response = RAGResponseWithCitationsFinalAnwser(
                answer=final_brief_content,
                citations=structured_citations
            )
            
            brief_sections = {
                "executive_summary": "Generated and Approved",
                "investor_profile": "Generated and Approved", 
                "market_intelligence": "Generated and Approved",
                "sentiment_analysis": "Generated and Approved",
                "competitive_intelligence": "Generated and Approved",
                "strategic_recommendations": "Generated and Approved"
            }
            
            # Add approval metadata to the brief
            approval_footer = f"""

---

**Approval Information:**
- Approved by: {approval_response.approver}
- Approval Date: {approval_response.approved_at.strftime('%Y-%m-%d %H:%M:%S')}
- Status: {approval_response.status}
- Notes: {approval_response.notes or 'None'}
"""
            
            final_brief_with_approval = final_brief_content + approval_footer
            
            response_message = AIMessage(
                content=f"Intelligence Brief Generated and Approved:\n\n{final_brief_with_approval}"
            )
            
            # Update HITL checkpoints
            hitl_checkpoints = state.get("hitl_checkpoints", [])
            hitl_checkpoints.append("final_brief_approval")
            
            return {
                **state,
                "messages": messages + [response_message],
                "brief_sections": brief_sections,
                "citations": citations_data,
                "hitl_checkpoints": hitl_checkpoints,
                "analysis_results": {
                    **analysis_results, 
                    "final_brief": final_response.model_dump(),
                    "brief_generated": True,
                    "approval_metadata": {
                        "approved": True,
                        "approver": approval_response.approver,
                        "approval_date": approval_response.approved_at.isoformat(),
                        "modifications_applied": bool(approval_response.modifications)
                    }
                }
            }
            
        except Exception as e:
            logging.error(f"HITL Reporting agent processing failed: {e}")
            error_msg = f"Failed to generate intelligence brief with approval: {str(e)}"
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {**analysis_results, "reporting_error": error_msg}
            }
    
    def _analyze_brief_content(self, brief_content: str, investor_data: Dict) -> Dict:
        """Analyze brief content for approval requirements."""
        
        word_count = len(brief_content.split())
        
        # Check for sensitive information
        sensitive_indicators = []
        if "confidential" in brief_content.lower():
            sensitive_indicators.append("confidential_content")
        if any(term in brief_content.lower() for term in ["proprietary", "internal", "restricted"]):
            sensitive_indicators.append("proprietary_content")
        
        # Check brief quality metrics
        quality_metrics = {
            "word_count": word_count,
            "has_executive_summary": "executive summary" in brief_content.lower(),
            "has_recommendations": "recommendation" in brief_content.lower(),
            "has_market_analysis": "market" in brief_content.lower(),
            "citation_count": brief_content.count("*") // 2  # Rough estimate
        }
        
        return {
            "word_count": word_count,
            "sensitive_indicators": sensitive_indicators,
            "quality_metrics": quality_metrics,
            "requires_review": word_count > 2000 or bool(sensitive_indicators)
        }
    
    def _apply_brief_modifications(self, brief_content: str, modifications: Dict) -> str:
        """Apply modifications requested by human approver."""
        
        modified_content = brief_content
        
        # Apply text replacements
        if "text_replacements" in modifications:
            for old_text, new_text in modifications["text_replacements"].items():
                modified_content = modified_content.replace(old_text, new_text)
        
        # Remove sections if requested
        if "remove_sections" in modifications:
            for section in modifications["remove_sections"]:
                # Simple section removal (in practice would be more sophisticated)
                section_start = modified_content.find(f"## {section}")
                if section_start != -1:
                    section_end = modified_content.find("## ", section_start + 1)
                    if section_end == -1:
                        section_end = len(modified_content)
                    modified_content = modified_content[:section_start] + modified_content[section_end:]
        
        # Add disclaimers if requested
        if "add_disclaimers" in modifications:
            disclaimers = modifications["add_disclaimers"]
            disclaimer_text = "\n\n**Disclaimers:**\n" + "\n".join([f"- {d}" for d in disclaimers])
            modified_content += disclaimer_text
        
        return modified_content
    
    async def process(self, state: IntelligenceState) -> IntelligenceState:
        """Main processing method - delegates to approval-aware processing."""
        return await self.process_with_approval(state)