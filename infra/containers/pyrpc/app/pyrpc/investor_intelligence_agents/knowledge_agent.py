"""
Knowledge & Data Extraction Agent

Responsible for collecting and structuring investor data from internal sources
and external databases. Handles investor research and data organization.
"""
import json
import logging
import requests
from typing import Dict, Optional
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.tools import tool
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters
from pyrpc.utils.base_url import get_base_url
from .state import IntelligenceState


class KnowledgeAgent:
    """
    Knowledge & Data Extraction Agent

    Specializes in gathering and structuring investor data from various sources.
    """

    def __init__(self, model_hyperparams: ModelHyperparameters = None):
        self.model_hyperparams = model_hyperparams or ModelHyperparameters()
        self.llm = new_llm(self.model_hyperparams)

    def search_investor_data(self, investor_name: str, investor_id: Optional[str] = None) -> str:
        """
        Search for investor data in internal database.

        Args:
            investor_name: Name of the investor to search for
            investor_id: Optional investor ID for direct lookup
        """
        try:
            # Mock investor data search - in real implementation would call actual API
            base_url = get_base_url()

            # Simulate API call to investor database
            mock_data = {
                "id": investor_id or "mock_id_123",
                "name": investor_name,
                "type": "PENSION_FUND",
                "status": "ACTIVE",
                "aum": 50000000000,  # $50B in cents
                "geography": "North America",
                "investment_focus": ["Private Equity", "Real Estate", "Infrastructure"],
                "esg_focus": True,
                "recent_allocations": [
                    {"asset_class": "Private Credit",
                        "amount": 2000000000, "date": "2024-Q3"},
                    {"asset_class": "Infrastructure",
                        "amount": 1500000000, "date": "2024-Q2"}
                ]
            }

            return json.dumps(mock_data, indent=2)

        except Exception as e:
            logging.error(f"Investor data search failed: {e}")
            return json.dumps({"error": f"Failed to retrieve investor data: {str(e)}"})

    def extract_ddq_patterns(self, investor_id: str) -> str:
        """
        Extract historical DDQ patterns and preferences for an investor.

        Args:
            investor_id: ID of the investor to analyze
        """
        try:
            # Mock DDQ pattern analysis
            mock_patterns = {
                "common_questions": [
                    "ESG integration methodology",
                    "Climate risk assessment",
                    "Portfolio company governance",
                    "Diversity metrics"
                ],
                "focus_areas": ["ESG", "Risk Management", "Performance Attribution"],
                "typical_timeline": "45-60 days",
                "decision_makers": ["Investment Committee", "CIO", "ESG Team"],
                "preferred_formats": ["Detailed written responses", "Supporting data tables"]
            }

            return json.dumps(mock_patterns, indent=2)

        except Exception as e:
            logging.error(f"DDQ pattern extraction failed: {e}")
            return json.dumps({"error": f"Failed to extract DDQ patterns: {str(e)}"})

    async def process(self, state: IntelligenceState) -> IntelligenceState:
        """
        Main knowledge agent processing logic.

        Extracts investor data and structures it for other agents.
        """
        messages = state["messages"]
        last_message = messages[-1] if messages else None

        # Extract investor information from the request
        investor_name = state.get("investor_name")
        investor_id = state.get("investor_id")

        if not investor_name and last_message:
            # Try to extract investor name from the message
            content = last_message.content if hasattr(
                last_message, 'content') else str(last_message)
            # Simple extraction - in real implementation would use NER
            if "brief for" in content.lower():
                investor_name = content.lower().split("brief for")[-1].strip()

        if not investor_name:
            error_msg = "No investor specified. Please provide an investor name or ID."
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {"error": error_msg}
            }

        try:
            # Search for investor data
            investor_data_str = self.search_investor_data(
                investor_name, investor_id)
            investor_data = json.loads(investor_data_str)

            # Extract DDQ patterns if we have an investor ID
            ddq_patterns = {}
            if investor_data.get("id"):
                ddq_patterns_str = self.extract_ddq_patterns(
                    investor_data["id"])
                ddq_patterns = json.loads(ddq_patterns_str)

            # Structure the results
            analysis_results = {
                "investor_data": investor_data,
                "ddq_patterns": ddq_patterns,
                "data_sources": ["Internal CRM", "Historical DDQ Database"]
            }

            # Create response message
            response_content = f"""Knowledge Agent Analysis Complete:

**Investor Profile:**
- Name: {investor_data.get('name', 'Unknown')}
- Type: {investor_data.get('type', 'Unknown')}
- Status: {investor_data.get('status', 'Unknown')}
- AUM: ${investor_data.get('aum', 0) / 100000000:.1f}B
- Geography: {investor_data.get('geography', 'Unknown')}

**Investment Focus:** {', '.join(investor_data.get('investment_focus', []))}

**DDQ Patterns Identified:** {len(ddq_patterns.get('common_questions', []))} common question types

Ready for market intelligence gathering and brief generation."""

            return {
                **state,
                "messages": messages + [AIMessage(content=response_content)],
                "investor_data": investor_data,
                "analysis_results": {**state.get("analysis_results", {}), **analysis_results},
                "investor_name": investor_data.get("name", investor_name),
                "investor_id": investor_data.get("id")
            }

        except Exception as e:
            logging.error(f"Knowledge agent processing failed: {e}")
            error_msg = f"Failed to process investor data: {str(e)}"
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {"error": error_msg}
            }
