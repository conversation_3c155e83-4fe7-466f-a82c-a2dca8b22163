"""
HITL-Aware Knowledge Agent

Knowledge agent with human-in-the-loop approval workflows for data extraction
and sensitive information handling.
"""
import json
import logging
from typing import Dict, List, Optional
from langchain_core.messages import AIMessage, SystemMessage
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters
from .state import IntelligenceState
from .knowledge_agent import KnowledgeAgent
from .hitl_manager import HITLManager
from .hitl_models import ApprovalType, ApprovalStatus


class HITLKnowledgeAgent(KnowledgeAgent):
    """
    HITL-Aware Knowledge Agent
    
    Extends the Knowledge Agent with human approval workflows for sensitive
    data extraction and processing decisions.
    """
    
    def __init__(self, model_hyperparams: ModelHyperparameters = None, hitl_manager: HITLManager = None):
        super().__init__(model_hyperparams)
        self.hitl_manager = hitl_manager or HITLManager()
    
    async def process_with_approval(self, state: IntelligenceState) -> IntelligenceState:
        """
        Process knowledge extraction with human approval checkpoints.
        """
        messages = state["messages"]
        
        # Extract investor information
        investor_name = state.get("investor_name")
        investor_id = state.get("investor_id")
        
        if not investor_name and messages:
            last_message = messages[-1] if messages else None
            if last_message and hasattr(last_message, 'content'):
                content = last_message.content
                if "brief for" in content.lower():
                    investor_name = content.lower().split("brief for")[-1].strip()
        
        if not investor_name:
            error_msg = "No investor specified. Please provide an investor name or ID."
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {"error": error_msg}
            }
        
        try:
            # Step 1: Extract investor data (as before)
            investor_data_str = self.search_investor_data(investor_name, investor_id)
            investor_data = json.loads(investor_data_str)
            
            # Step 2: Check if data contains sensitive information requiring approval
            sensitive_data = self._identify_sensitive_data(investor_data)
            
            if sensitive_data:
                # Create approval request for sensitive data handling
                approval_request = await self.hitl_manager.create_approval_request(
                    approval_type=ApprovalType.SENSITIVE_DATA,
                    title=f"Sensitive Data Access: {investor_name}",
                    description=f"Request to process sensitive information for {investor_name}",
                    data={
                        "investor_name": investor_name,
                        "sensitive_fields": sensitive_data,
                        "data_summary": self._create_data_summary(investor_data)
                    },
                    context={"agent": "knowledge_agent", "operation": "data_extraction"},
                    priority="HIGH"
                )
                
                # Wait for approval
                approval_response = await self.hitl_manager.wait_for_approval(approval_request)
                
                if approval_response.status != ApprovalStatus.APPROVED:
                    # Handle rejection or timeout
                    error_msg = f"Data access not approved: {approval_response.feedback}"
                    return {
                        **state,
                        "messages": messages + [AIMessage(content=error_msg)],
                        "analysis_results": {"error": error_msg, "approval_status": approval_response.status}
                    }
                
                # Apply any modifications requested by approver
                if approval_response.modifications:
                    investor_data = self._apply_data_modifications(investor_data, approval_response.modifications)
            
            # Step 3: Request approval for data extraction operation
            extraction_approval = await self.hitl_manager.create_approval_request(
                approval_type=ApprovalType.DATA_EXTRACTION,
                title=f"Data Extraction: {investor_name}",
                description=f"Extract and process investor data for {investor_name}",
                data={
                    "investor_data": investor_data,
                    "confidence": state.get("confidence", 0.0),
                    "operation": "knowledge_extraction"
                },
                context={"investor_name": investor_name, "agent": "knowledge_agent"}
            )
            
            extraction_response = await self.hitl_manager.wait_for_approval(extraction_approval)
            
            if extraction_response.status == ApprovalStatus.REJECTED:
                error_msg = f"Data extraction rejected: {extraction_response.feedback}"
                return {
                    **state,
                    "messages": messages + [AIMessage(content=error_msg)],
                    "analysis_results": {"error": error_msg}
                }
            
            # Step 4: Extract DDQ patterns (if approved)
            ddq_patterns = {}
            if investor_data.get("id"):
                ddq_patterns_str = self.extract_ddq_patterns(investor_data["id"])
                ddq_patterns = json.loads(ddq_patterns_str)
            
            # Step 5: Structure results with approval metadata
            analysis_results = {
                "investor_data": investor_data,
                "ddq_patterns": ddq_patterns,
                "data_sources": ["Internal CRM", "Historical DDQ Database"],
                "approval_metadata": {
                    "extraction_approved": extraction_response.status == ApprovalStatus.APPROVED,
                    "approver": extraction_response.approver,
                    "approval_notes": extraction_response.notes,
                    "sensitive_data_handled": bool(sensitive_data)
                }
            }
            
            # Create response message
            approval_note = ""
            if extraction_response.status == ApprovalStatus.APPROVED:
                approval_note = f" (Approved by {extraction_response.approver})"
            
            response_content = f"""Knowledge Agent Analysis Complete{approval_note}:

**Investor Profile:**
- Name: {investor_data.get('name', 'Unknown')}
- Type: {investor_data.get('type', 'Unknown')}
- Status: {investor_data.get('status', 'Unknown')}
- AUM: ${investor_data.get('aum', 0) / 100000000:.1f}B
- Geography: {investor_data.get('geography', 'Unknown')}

**Investment Focus:** {', '.join(investor_data.get('investment_focus', []))}

**DDQ Patterns Identified:** {len(ddq_patterns.get('common_questions', []))} common question types

**Approval Status:** Data extraction approved for intelligence brief generation.

Ready for market intelligence gathering and brief generation."""
            
            # Update state with HITL information
            pending_approvals = state.get("pending_approvals", [])
            hitl_checkpoints = state.get("hitl_checkpoints", [])
            hitl_checkpoints.append("knowledge_extraction")
            
            return {
                **state,
                "messages": messages + [AIMessage(content=response_content)],
                "investor_data": investor_data,
                "analysis_results": {**state.get("analysis_results", {}), **analysis_results},
                "investor_name": investor_data.get("name", investor_name),
                "investor_id": investor_data.get("id"),
                "hitl_checkpoints": hitl_checkpoints
            }
            
        except Exception as e:
            logging.error(f"HITL Knowledge agent processing failed: {e}")
            error_msg = f"Failed to process investor data with approval: {str(e)}"
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {"error": error_msg}
            }
    
    def _identify_sensitive_data(self, investor_data: Dict) -> List[str]:
        """Identify sensitive data fields that require approval."""
        sensitive_fields = []
        
        # Check for sensitive information
        if investor_data.get("email"):
            sensitive_fields.append("email")
        if investor_data.get("phone"):
            sensitive_fields.append("phone")
        if investor_data.get("address"):
            sensitive_fields.append("address")
        if investor_data.get("aum", 0) > 10000000000:  # >$100B AUM
            sensitive_fields.append("large_aum")
        
        return sensitive_fields
    
    def _create_data_summary(self, investor_data: Dict) -> str:
        """Create a summary of data for approval review."""
        return f"""
        Investor: {investor_data.get('name', 'Unknown')}
        Type: {investor_data.get('type', 'Unknown')}
        AUM: ${investor_data.get('aum', 0) / 100000000:.1f}B
        Geography: {investor_data.get('geography', 'Unknown')}
        Data Fields: {len(investor_data)} total fields
        """
    
    def _apply_data_modifications(self, investor_data: Dict, modifications: Dict) -> Dict:
        """Apply modifications requested by human approver."""
        modified_data = investor_data.copy()
        
        # Apply field removals
        if "remove_fields" in modifications:
            for field in modifications["remove_fields"]:
                modified_data.pop(field, None)
        
        # Apply field masking
        if "mask_fields" in modifications:
            for field in modifications["mask_fields"]:
                if field in modified_data:
                    modified_data[field] = "[REDACTED]"
        
        # Apply value replacements
        if "replace_values" in modifications:
            for field, new_value in modifications["replace_values"].items():
                if field in modified_data:
                    modified_data[field] = new_value
        
        return modified_data
    
    async def process(self, state: IntelligenceState) -> IntelligenceState:
        """Main processing method - delegates to approval-aware processing."""
        return await self.process_with_approval(state)