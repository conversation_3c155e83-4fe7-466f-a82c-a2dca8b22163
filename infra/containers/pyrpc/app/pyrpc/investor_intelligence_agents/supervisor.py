"""
Supervisor Agent

Central orchestrator for the Intelligence Agent system. Routes tasks to specialized
expert agents based on user intent classification.
"""
import json
import logging
from typing import Annotated, Literal
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState
from langchain_core.tools import InjectedToolCallId
from langgraph.types import Command
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters
from .state import IntelligenceState


def create_handoff_tool(*, agent_name: str, description: str | None = None):
    """Create a handoff tool for transferring control to a specific agent."""
    name = f"transfer_to_{agent_name}"
    description = description or f"Transfer to {agent_name} for specialized processing."

    @tool(name, description=description)
    def handoff_tool(
        state: Annotated[IntelligenceState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ) -> Command:
        tool_message = {
            "role": "tool",
            "content": f"Successfully transferred to {agent_name}",
            "name": name,
            "tool_call_id": tool_call_id,
        }
        return Command(
            goto=agent_name,
            update={**state, "messages": state["messages"] + [tool_message]},
            graph=Command.PARENT,
        )

    return handoff_tool


class SupervisorAgent:
    """
    Supervisor Agent for Intelligence System

    Analyzes user intent and routes requests to appropriate expert agents.
    """

    def __init__(self, model_hyperparams: ModelHyperparameters = None):
        self.model_hyperparams = model_hyperparams or ModelHyperparameters()
        self.llm = new_llm(self.model_hyperparams)

        # Available expert agents
        self.expert_agents = [
            "knowledge_agent",
            "market_agent",
            "reporting_agent"
        ]

        # Create handoff tools
        self.handoff_tools = [
            create_handoff_tool(
                agent_name="knowledge_agent",
                description="Transfer to Knowledge Agent for data extraction and investor research"
            ),
            create_handoff_tool(
                agent_name="market_agent",
                description="Transfer to Market Agent for market intelligence and sentiment analysis"
            ),
            create_handoff_tool(
                agent_name="reporting_agent",
                description="Transfer to Reporting Agent for brief generation and formatting"
            )
        ]

        self.llm_with_tools = self.llm.bind_tools(self.handoff_tools)

    async def analyze_intent(self, state: IntelligenceState) -> IntelligenceState:
        """
        Analyze user intent and classify the request.

        Returns updated state with classification and confidence score.
        """
        messages = state["messages"]
        last_message = messages[-1] if messages else None

        if not last_message:
            return {
                **state,
                "classification": "unknown",
                "confidence": 0.0
            }

        # Intent classification prompt
        system_prompt = """You are an intelligent router for an investor intelligence system.
        
        Analyze the user's request and classify it into one of these categories:
        - "brief_generation": Generate intelligence brief for specific investor
        - "market_analysis": Market intelligence and sentiment analysis
        - "data_extraction": Extract and structure investor data
        - "unknown": Cannot determine intent
        
        Also provide a confidence score from 0.0 to 1.0.
        
        Respond with JSON: {"classification": "category", "confidence": 0.95}
        """

        try:
            response = await self.llm.ainvoke([
                SystemMessage(content=system_prompt),
                last_message
            ])

            # Parse classification result
            result = json.loads(response.content)
            classification = result.get("classification", "unknown")
            confidence = float(result.get("confidence", 0.0))

            return {
                **state,
                "classification": classification,
                "confidence": confidence
            }

        except Exception as e:
            logging.error(f"Intent analysis failed: {e}")
            return {
                **state,
                "classification": "unknown",
                "confidence": 0.0
            }

    def route_request(self, state: IntelligenceState) -> Literal["knowledge_agent", "market_agent", "reporting_agent", "__end__"]:
        """
        Route request to appropriate expert agent based on classification and confidence.
        """
        classification = state.get("classification", "unknown")
        confidence = state.get("confidence", 0.0)

        # Low confidence - route to knowledge agent for data gathering
        if confidence < 0.5:
            return "knowledge_agent"

        # High confidence routing based on classification
        if classification == "brief_generation":
            # Check if we have investor data, if not get it first
            if not state.get("investor_data"):
                return "knowledge_agent"
            # If we have data but no market intelligence, get that
            elif not state.get("external_data"):
                return "market_agent"
            # If we have both, generate the brief
            else:
                return "reporting_agent"

        elif classification == "market_analysis":
            return "market_agent"

        elif classification == "data_extraction":
            return "knowledge_agent"

        else:
            # Default to knowledge agent for unknown intents
            return "knowledge_agent"

    async def process(self, state: IntelligenceState) -> IntelligenceState:
        """Main supervisor processing logic."""
        # First analyze intent if not already done
        if not state.get("classification"):
            state = await self.analyze_intent(state)

        # Route based on current state and classification
        next_agent = self.route_request(state)

        if next_agent == "__end__":
            return state

        # Add routing decision to messages for transparency
        routing_message = SystemMessage(
            content=f"Supervisor routing to {next_agent} (classification: {state.get('classification')}, confidence: {state.get('confidence')})"
        )

        return {
            **state,
            "messages": state["messages"] + [routing_message]
        }
