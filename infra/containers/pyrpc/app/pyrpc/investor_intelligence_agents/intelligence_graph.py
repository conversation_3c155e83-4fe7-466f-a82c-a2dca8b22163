"""
Intelligence Agent Graph

Main orchestration graph that implements the supervisor-expert architecture
for the Intelligence Agent system using LangGraph.
"""
import logging
from typing import AsyncGenerator, Dict, List, Optional
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langgraph.constants import END, START
from langgraph.graph import StateGraph
from pyrpc.rag_response.new_llm import ModelHyperparameters
from pyrpc.models import RAGResponseWithCitations, ChatMessageStatus
from .state import IntelligenceState
from .supervisor import SupervisorAgent
from .knowledge_agent import KnowledgeAgent
from .market_agent import MarketAgent
from .reporting_agent import ReportingAgent


class IntelligenceGraph:
    """
    Intelligence Agent Graph
    
    Orchestrates the multi-agent intelligence system using LangGraph.
    """
    
    def __init__(self, model_hyperparams: ModelHyperparameters = None):
        self.model_hyperparams = model_hyperparams or ModelHyperparameters()
        
        # Initialize agents
        self.supervisor = SupervisorAgent(self.model_hyperparams)
        self.knowledge_agent = KnowledgeAgent(self.model_hyperparams)
        self.market_agent = MarketAgent(self.model_hyperparams)
        self.reporting_agent = ReportingAgent(self.model_hyperparams)
        
        # Build the graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow."""
        
        # Create the state graph
        workflow = StateGraph(IntelligenceState)
        
        # Add nodes for each agent
        workflow.add_node("supervisor", self._supervisor_node)
        workflow.add_node("knowledge_agent", self._knowledge_node)
        workflow.add_node("market_agent", self._market_node)
        workflow.add_node("reporting_agent", self._reporting_node)
        
        # Define the workflow edges
        workflow.add_edge(START, "supervisor")
        
        # Conditional edges from supervisor to expert agents
        workflow.add_conditional_edges(
            "supervisor",
            self._route_from_supervisor,
            {
                "knowledge_agent": "knowledge_agent",
                "market_agent": "market_agent", 
                "reporting_agent": "reporting_agent",
                "__end__": END
            }
        )
        
        # All expert agents return to supervisor for next routing decision
        workflow.add_edge("knowledge_agent", "supervisor")
        workflow.add_edge("market_agent", "supervisor")
        workflow.add_edge("reporting_agent", END)  # Reporting agent ends the workflow
        
        return workflow.compile()
    
    async def _supervisor_node(self, state: IntelligenceState) -> IntelligenceState:
        """Supervisor node processing."""
        try:
            return await self.supervisor.process(state)
        except Exception as e:
            logging.error(f"Supervisor node error: {e}")
            return {
                **state,
                "classification": "error",
                "confidence": 0.0
            }
    
    async def _knowledge_node(self, state: IntelligenceState) -> IntelligenceState:
        """Knowledge agent node processing."""
        try:
            return await self.knowledge_agent.process(state)
        except Exception as e:
            logging.error(f"Knowledge agent node error: {e}")
            return state
    
    async def _market_node(self, state: IntelligenceState) -> IntelligenceState:
        """Market agent node processing."""
        try:
            return await self.market_agent.process(state)
        except Exception as e:
            logging.error(f"Market agent node error: {e}")
            return state
    
    async def _reporting_node(self, state: IntelligenceState) -> IntelligenceState:
        """Reporting agent node processing."""
        try:
            return await self.reporting_agent.process(state)
        except Exception as e:
            logging.error(f"Reporting agent node error: {e}")
            return state
    
    def _route_from_supervisor(self, state: IntelligenceState) -> str:
        """Route from supervisor to appropriate expert agent."""
        try:
            return self.supervisor.route_request(state)
        except Exception as e:
            logging.error(f"Routing error: {e}")
            return "__end__"
    
    async def generate_intelligence_brief(
        self,
        user_message: str,
        investor_name: Optional[str] = None,
        investor_id: Optional[str] = None,
        org_info: Optional[Dict] = None,
        **kwargs
    ) -> AsyncGenerator[RAGResponseWithCitations, None]:
        """
        Generate an intelligence brief for a specific investor.
        
        Args:
            user_message: User's request message
            investor_name: Optional investor name
            investor_id: Optional investor ID
            org_info: Organization information
            **kwargs: Additional parameters
        """
        
        trace_id = kwargs.get("trace_id")
        
        try:
            # Initialize state
            initial_state: IntelligenceState = {
                "messages": [HumanMessage(content=user_message)],
                "classification": "",
                "confidence": 0.0,
                "analysis_results": {},
                "investor_id": investor_id,
                "investor_name": investor_name,
                "investor_data": None,
                "external_data": None,
                "brief_sections": None,
                "citations": None
            }
            
            # Track processing steps
            step_count = 0
            current_agent = "supervisor"
            
            # Stream the graph execution
            async for state_update in self.graph.astream(initial_state):
                step_count += 1
                
                # Get the current state
                current_state = list(state_update.values())[0] if state_update else initial_state
                current_node = list(state_update.keys())[0] if state_update else "supervisor"
                
                # Determine status based on current processing
                if current_node == "knowledge_agent":
                    status = ChatMessageStatus.RETRIEVING
                    message = "Gathering investor data and historical patterns..."
                elif current_node == "market_agent":
                    status = ChatMessageStatus.GENERATING_ANSWER
                    message = "Analyzing market conditions and sentiment..."
                elif current_node == "reporting_agent":
                    status = ChatMessageStatus.GENERATING_CITATIONS
                    message = "Generating comprehensive intelligence brief..."
                elif current_node == "supervisor":
                    status = ChatMessageStatus.GENERATING_ANSWER
                    message = f"Orchestrating intelligence gathering (Step {step_count})..."
                else:
                    status = ChatMessageStatus.GENERATING_ANSWER
                    message = "Processing intelligence request..."
                
                # Check if we have a final brief
                analysis_results = current_state.get("analysis_results", {})
                if analysis_results.get("brief_generated"):
                    final_brief = analysis_results.get("final_brief", {})
                    brief_answer = final_brief.get("answer", "Brief generation completed.")
                    brief_citations = final_brief.get("citations", [])
                    
                    yield RAGResponseWithCitations(
                        answer=brief_answer,
                        citations=brief_citations,
                        status=ChatMessageStatus.READY,
                        traceId=trace_id
                    )
                    return
                
                # Yield intermediate status
                yield RAGResponseWithCitations(
                    answer=message,
                    citations=[],
                    status=status,
                    traceId=trace_id
                )
                
                # Safety check to prevent infinite loops
                if step_count > 10:
                    yield RAGResponseWithCitations(
                        answer="Intelligence brief generation exceeded maximum steps. Please try again.",
                        citations=[],
                        status=ChatMessageStatus.ERROR,
                        traceId=trace_id
                    )
                    return
            
            # If we reach here without a final brief, something went wrong
            yield RAGResponseWithCitations(
                answer="Intelligence brief generation completed but no final output was produced.",
                citations=[],
                status=ChatMessageStatus.ERROR,
                traceId=trace_id
            )
            
        except Exception as e:
            logging.error(f"Intelligence graph execution failed: {e}")
            yield RAGResponseWithCitations(
                answer=f"Failed to generate intelligence brief: {str(e)}",
                citations=[],
                status=ChatMessageStatus.ERROR,
                traceId=trace_id
            )