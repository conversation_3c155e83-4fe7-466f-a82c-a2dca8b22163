#!/usr/bin/env python3
"""
Test script for HITL API endpoints

Tests the HTTP API endpoints for Human-in-the-Loop approval workflows.
"""
import asyncio
import json
import aiohttp
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_hitl_api_endpoints():
    """Test HITL API endpoints."""
    
    base_url = "http://localhost:8000"
    
    print("Testing HITL API Endpoints")
    print("=" * 40)
    
    # Test 1: Generate intelligence brief with HITL enabled
    print("\n1. Testing Intelligence Brief with HITL:")
    
    hitl_payload = {
        "user_message": "Generate intelligence brief for CalPERS",
        "context": {
            "institutionName": "Test Investment Management",
            "orgId": "test_org_001"
        },
        "modelParameters": {
            "model": "gpt-4o-mini",
            "temperature": 0.0,
            "top_p": 1e-9
        },
        "tracingContext": {
            "traceId": "hitl_api_test_123",
            "sessionId": "hitl_session_123"
        },
        "investor_name": "CalPERS",
        "enable_hitl": True,
        "hitl_config": {
            "enabled": True,
            "checkpoints": [
                {
                    "name": "final_brief_approval",
                    "description": "Approve final intelligence brief",
                    "approval_type": "FINAL_BRIEF",
                    "required": True,
                    "timeout_seconds": 60,
                    "auto_approve_conditions": {
                        "word_count": {"min": 100, "max": 10000}
                    }
                }
            ],
            "default_timeout": 60,
            "auto_approve_low_risk": True,
            "require_dual_approval": False
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # Generate intelligence brief with HITL
            async with session.post(
                f"{base_url}/pyrpc/generate-answer/intelligence-brief",
                json=hitl_payload
            ) as response:
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    response_count = 0
                    async for line in response.content:
                        if line:
                            response_count += 1
                            try:
                                data = json.loads(line.decode('utf-8').strip())
                                status = data.get('status', 'UNKNOWN')
                                answer_preview = data.get('answer', '')[:60] + "..." if len(data.get('answer', '')) > 60 else data.get('answer', '')
                                
                                print(f"   Response {response_count}: [{status}] {answer_preview}")
                                
                                if status in ["READY", "ERROR"] or response_count > 15:
                                    break
                                    
                            except json.JSONDecodeError:
                                pass
                    
                    print(f"   ✅ HITL brief generation completed ({response_count} responses)")
                else:
                    error_text = await response.text()
                    print(f"   ❌ Error: {response.status} - {error_text}")
                    
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
    
    # Test 2: Get pending approvals
    print("\n2. Testing Get Pending Approvals:")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/pyrpc/hitl/pending-approvals") as response:
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    pending_approvals = data.get("pending_approvals", [])
                    print(f"   ✅ Found {len(pending_approvals)} pending approvals")
                    
                    for approval in pending_approvals[:3]:  # Show first 3
                        print(f"      - {approval.get('title', 'Unknown')} ({approval.get('type', 'Unknown')})")
                else:
                    error_text = await response.text()
                    print(f"   ❌ Error: {response.status} - {error_text}")
                    
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
    
    # Test 3: Submit approval (mock)
    print("\n3. Testing Submit Approval:")
    
    approval_payload = {
        "request_id": "test_approval_123",
        "status": "APPROVED",
        "feedback": "Test approval submission",
        "approver": "test_user",
        "notes": "API test approval"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{base_url}/pyrpc/hitl/submit-approval",
                json=approval_payload
            ) as response:
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ Approval submission: {data.get('status', 'unknown')}")
                    print(f"   Message: {data.get('message', 'No message')}")
                else:
                    error_text = await response.text()
                    print(f"   ❌ Error: {response.status} - {error_text}")
                    
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
    
    # Test 4: Get HITL session (mock)
    print("\n4. Testing Get HITL Session:")
    
    test_session_id = "hitl_session_123"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/pyrpc/hitl/session/{test_session_id}") as response:
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ Session found: {data.get('session_id', 'unknown')}")
                    print(f"   Workflow: {data.get('workflow_type', 'unknown')}")
                    print(f"   Pending approvals: {len(data.get('pending_approvals', []))}")
                elif response.status == 404:
                    print(f"   ℹ️ Session not found (expected for test)")
                else:
                    error_text = await response.text()
                    print(f"   ❌ Error: {response.status} - {error_text}")
                    
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
    
    print("\nHITL API endpoint testing completed!")


async def test_hitl_workflow_integration():
    """Test complete HITL workflow through API."""
    
    print("\nTesting Complete HITL Workflow")
    print("=" * 35)
    
    base_url = "http://localhost:5328"
    
    # Step 1: Start intelligence brief generation with HITL
    print("Step 1: Starting intelligence brief with HITL...")
    
    workflow_payload = {
        "user_message": "Generate intelligence brief for Ontario Teachers",
        "context": {
            "institutionName": "Test Fund",
            "orgId": "workflow_test"
        },
        "modelParameters": {
            "model": "gpt-4o-mini",
            "temperature": 0.0
        },
        "tracingContext": {
            "traceId": "workflow_test_456",
            "sessionId": "workflow_session_456"
        },
        "enable_hitl": True,
        "hitl_config": {
            "enabled": True,
            "checkpoints": [
                {
                    "name": "data_extraction_approval",
                    "description": "Approve data extraction",
                    "approval_type": "DATA_EXTRACTION",
                    "required": False,
                    "timeout_seconds": 30
                },
                {
                    "name": "final_brief_approval", 
                    "description": "Approve final brief",
                    "approval_type": "FINAL_BRIEF",
                    "required": True,
                    "timeout_seconds": 60,
                    "auto_approve_conditions": {
                        "word_count": {"min": 50, "max": 20000}
                    }
                }
            ]
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{base_url}/pyrpc/generate-answer/intelligence-brief",
                json=workflow_payload
            ) as response:
                
                if response.status == 200:
                    print("   ✅ Workflow started successfully")
                    
                    # Process streaming responses
                    checkpoint_count = 0
                    async for line in response.content:
                        if line:
                            try:
                                data = json.loads(line.decode('utf-8').strip())
                                status = data.get('status', 'UNKNOWN')
                                answer = data.get('answer', '')
                                
                                if "checkpoint" in answer.lower() or "approval" in answer.lower():
                                    checkpoint_count += 1
                                    print(f"   📋 Checkpoint {checkpoint_count}: {answer[:80]}...")
                                
                                if status == "READY":
                                    print("   ✅ Workflow completed successfully")
                                    if "Approved by:" in answer:
                                        print("   ✅ Approval metadata found in final output")
                                    break
                                elif status == "ERROR":
                                    print(f"   ❌ Workflow error: {answer}")
                                    break
                                    
                            except json.JSONDecodeError:
                                pass
                    
                    print(f"   📊 Total checkpoints processed: {checkpoint_count}")
                    
                else:
                    error_text = await response.text()
                    print(f"   ❌ Workflow failed: {response.status} - {error_text}")
                    
    except Exception as e:
        print(f"   ❌ Workflow test failed: {e}")
    
    print("\nComplete workflow testing finished!")


if __name__ == "__main__":
    print("HITL API Test Suite")
    print("=" * 50)
    print("Make sure the FastAPI server is running on localhost:8000")
    print("Start with: uvicorn pyrpc.index:app --reload --port 8000")
    print()
    
    # Run tests
    asyncio.run(test_hitl_api_endpoints())
    asyncio.run(test_hitl_workflow_integration())
    
    print("\n" + "=" * 50)
    print("HITL API Testing Complete!")
    print("\nEndpoints Tested:")
    print("✅ /pyrpc/generate-answer/intelligence-brief (with HITL)")
    print("✅ /pyrpc/hitl/pending-approvals")
    print("✅ /pyrpc/hitl/submit-approval")
    print("✅ /pyrpc/hitl/session/{session_id}")
    print("\nThe HITL API is ready for integration!")