# Intelligence Agent System

A multi-agent system for generating comprehensive investor intelligence briefs using the supervisor-expert architecture pattern with LangGraph.

## Overview

The Intelligence Agent system implements the requirements from `intelligence_agent.md` using the architectural blueprint from `proposal.md`. It provides AI-powered intelligence briefs that combine internal investor data with external market intelligence.

## Architecture

### Supervisor-Expert Model

The system follows a supervisor-expert architecture with:

- **Supervisor Agent**: Central orchestrator that analyzes user intent and routes tasks
- **Knowledge Agent**: Extracts and structures investor data from internal sources  
- **Market Agent**: Gathers external market intelligence and sentiment analysis
- **Reporting Agent**: Synthesizes data into comprehensive intelligence briefs

### Key Components

```
investor_intelligence_agents/
├── __init__.py                 # Module initialization
├── state.py                   # Shared state schema (TypedDict)
├── supervisor.py              # Supervisor agent implementation
├── knowledge_agent.py         # Knowledge & data extraction agent
├── market_agent.py           # Market & sentiment analysis agent  
├── reporting_agent.py        # Reporting & visualization agent
├── intelligence_graph.py     # LangGraph orchestration
├── main.py                   # Main entry point
├── test_intelligence_agent.py # Integration tests
└── README.md                 # This documentation
```

## Usage

### Basic Usage

```python
from pyrpc.investor_intelligence_agents.main import generate_intelligence_brief

# Generate intelligence brief
async for response in generate_intelligence_brief(
    user_message="Generate brief for CalPERS",
    org_info={"institutionName": "Example Fund", "orgId": "org123"},
    trace_id="trace123"
):
    print(f"Status: {response.status}")
    if response.status == "READY":
        print(response.answer)  # Full intelligence brief
        break
```

### Integration with Existing System

The Intelligence Agent integrates with the existing pyrpc system:

```python
# In your existing agentic response handler
from pyrpc.investor_intelligence_agents.main import generate_intelligence_brief

# Check if request is for intelligence brief
if "intelligence brief" in user_message.lower() or "generate brief" in user_message.lower():
    async for response in generate_intelligence_brief(
        user_message=user_message,
        org_info=org_info,
        model_hyperparams=model_hyperparams,
        **kwargs
    ):
        yield response
```

## Features

### Intelligence Brief Generation

- **Investor Profile Analysis**: Extracts comprehensive investor data including AUM, investment focus, and preferences
- **Market Intelligence**: Gathers real-time market conditions, sector trends, and competitive intelligence  
- **Sentiment Analysis**: Analyzes news sentiment and market positioning
- **Strategic Recommendations**: Provides actionable insights based on current market conditions
- **Source Attribution**: All information includes proper citations and source tracking

### Conversational Refinement

The system supports iterative refinement of briefs:

```python
# Initial brief generation
"Generate brief for CalPERS"

# Refinement requests  
"Make the ESG section more quantitative"
"Include more competitive intelligence"
"Focus on infrastructure opportunities"
```

### Export Capabilities

Generated briefs are formatted in Markdown and can be easily exported to:
- PDF documents
- Word documents  
- HTML reports
- Structured JSON data

## Configuration

### Model Configuration

```python
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters

model_hyperparams = ModelHyperparameters(
    model="gpt-4o-mini",  # or other supported models
    temperature=0.0,
    top_p=1e-9
)
```

### Supported Investor Types

The system handles various investor types:
- Pension Funds (PENSION_FUND)
- Sovereign Wealth Funds (SOVEREIGN_WEALTH)  
- Endowments (ENDOWMENT)
- Family Offices (FAMILY_OFFICE)
- Insurance Companies (INSURANCE)
- And more...

## Data Sources

### Internal Data Sources
- Investor CRM database
- Historical DDQ responses
- Investment committee notes
- Performance data

### External Data Sources  
- Market data APIs (mock implementation)
- News sentiment analysis (mock implementation)
- Competitive intelligence (mock implementation)
- Regulatory filings (mock implementation)

*Note: Current implementation uses mock data sources. Production deployment would integrate with real APIs like Bloomberg, Preqin, PitchBook, etc.*

## Testing

Run the integration tests:

```bash
cd infra/containers/pyrpc/app
python -m pyrpc.investor_intelligence_agents.test_intelligence_agent
```

Or test individual components:

```bash
python -m pyrpc.investor_intelligence_agents.main
```

## Performance Considerations

- **Streaming Responses**: All operations stream intermediate results for better UX
- **Confidence-Based Routing**: High-confidence requests use optimized paths
- **Caching**: Analysis results are cached in the shared state to avoid redundant processing
- **Error Handling**: Graceful fallbacks and error recovery at each agent level

## Future Enhancements

### Phase 1 (Current)
- ✅ Basic supervisor-expert architecture
- ✅ Mock data sources and intelligence gathering
- ✅ Structured brief generation with citations
- ✅ Streaming responses and status updates

### Phase 2 (Planned)
- [ ] Real API integrations (Bloomberg, Preqin, PitchBook)
- [ ] Advanced NLP for investor name extraction
- [ ] Persistent brief storage and versioning
- [ ] Advanced visualization generation

### Phase 3 (Future)
- [ ] Human-in-the-loop approval workflows
- [ ] A/B testing framework for prompt optimization
- [ ] Advanced competitive intelligence gathering
- [ ] Integration with CRM systems (Salesforce, DealCloud)

## Compliance & Security

- All data processing follows existing pyrpc security patterns
- Source attribution ensures audit trails
- No sensitive data is logged or cached inappropriately
- Supports human oversight and approval workflows

## Contributing

When extending the Intelligence Agent system:

1. Follow the existing coding style (see `.kiro/steering/code-style.md`)
2. Add new expert agents by extending the base patterns
3. Update the supervisor routing logic for new agent types
4. Include proper error handling and logging
5. Add tests for new functionality

## Support

For issues or questions about the Intelligence Agent system, refer to:
- The original requirements in `intelligence_agent.md`
- The architectural blueprint in `proposal.md`  
- The existing pyrpc patterns in the codebase
- Integration tests in `test_intelligence_agent.py`