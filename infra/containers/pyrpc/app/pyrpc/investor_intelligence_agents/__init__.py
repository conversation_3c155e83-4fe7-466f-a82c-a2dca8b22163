"""
Investor Intelligence Agents Module

This module implements the Intelligence Agent system for generating comprehensive
investor intelligence briefs using a supervisor-expert multi-agent architecture.
"""

from .main import generate_intelligence_brief
from .intelligence_graph import IntelligenceGraph
from .hitl_intelligence_graph import HITLIntelligenceGraph
from .hitl_manager import HITLManager
from .state import IntelligenceState
from .hitl_models import HITLWorkflowConfig, ApprovalType, ApprovalStatus

__all__ = [
    "generate_intelligence_brief",
    "IntelligenceGraph", 
    "HITLIntelligenceGraph",
    "HITLManager",
    "IntelligenceState",
    "HITLWorkflowConfig",
    "ApprovalType",
    "ApprovalStatus"
]