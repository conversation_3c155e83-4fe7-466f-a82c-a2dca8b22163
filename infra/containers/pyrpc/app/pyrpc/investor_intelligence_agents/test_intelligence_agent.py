"""
Test Intelligence Agent Implementation

Simple test to verify the Intelligence Agent system works correctly.
"""
import asyncio
import logging
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters
from .main import generate_intelligence_brief

# Configure logging
logging.basicConfig(level=logging.INFO)


async def test_basic_brief_generation():
    """Test basic intelligence brief generation."""
    
    print("Testing Intelligence Agent System")
    print("=" * 50)
    
    # Test configuration
    org_info = {
        "institutionName": "Test Investment Management",
        "orgId": "test_org_001"
    }
    
    model_hyperparams = ModelHyperparameters()
    
    test_cases = [
        "Generate brief for CalPERS",
        "Generate intelligence brief for Ontario Teachers",
        "Create investor analysis for NBIM"
    ]
    
    for i, user_message in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {user_message}")
        print("-" * 30)
        
        try:
            response_count = 0
            async for response in generate_intelligence_brief(
                user_message=user_message,
                org_info=org_info,
                model_hyperparams=model_hyperparams,
                trace_id=f"test_trace_{i}"
            ):
                response_count += 1
                status = response.status
                answer_preview = response.answer[:80] + "..." if len(response.answer) > 80 else response.answer
                
                print(f"  [{status}] {answer_preview}")
                
                # Stop after getting the final response
                if status == "READY":
                    print(f"  ✓ Brief generated successfully ({response_count} responses)")
                    break
                elif status == "ERROR":
                    print(f"  ✗ Error occurred: {response.answer}")
                    break
                    
                # Safety check
                if response_count > 10:
                    print(f"  ⚠ Too many responses, stopping test")
                    break
                    
        except Exception as e:
            print(f"  ✗ Test failed with exception: {e}")
    
    print("\nTest completed!")


if __name__ == "__main__":
    asyncio.run(test_basic_brief_generation())