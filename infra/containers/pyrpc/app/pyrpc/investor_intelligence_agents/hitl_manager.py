"""
Human-in-the-Loop Manager

Manages human approval workflows, state persistence, and approval routing.
"""
import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from .hitl_models import (
    ApprovalRequest, ApprovalResponse, ApprovalStatus, ApprovalType,
    HITLCheckpoint, HITLWorkflowConfig, HITLSession
)
from .state import IntelligenceState


class HITLManager:
    """
    Human-in-the-Loop Manager
    
    Manages approval workflows, state persistence, and human oversight.
    """
    
    def __init__(self, config: HITLWorkflowConfig = None):
        self.config = config or self._default_config()
        self.pending_approvals: Dict[str, ApprovalRequest] = {}
        self.approval_responses: Dict[str, ApprovalResponse] = {}
        self.sessions: Dict[str, HITLSession] = {}
        self.approval_callbacks: Dict[str, Callable] = {}
        
    def _default_config(self) -> HITLWorkflowConfig:
        """Create default HITL configuration."""
        return HITLWorkflowConfig(
            enabled=True,
            checkpoints=[
                HITLCheckpoint(
                    name="data_extraction_approval",
                    description="Approve investor data extraction and processing",
                    approval_type=ApprovalType.DATA_EXTRACTION,
                    required=False,  # Optional for testing
                    timeout_seconds=1800,  # 30 minutes
                    auto_approve_conditions={"confidence": 0.9}
                ),
                HITLCheckpoint(
                    name="market_analysis_approval", 
                    description="Approve market intelligence and sentiment analysis",
                    approval_type=ApprovalType.MARKET_ANALYSIS,
                    required=False,  # Optional for testing
                    timeout_seconds=1800
                ),
                HITLCheckpoint(
                    name="final_brief_approval",
                    description="Approve final intelligence brief before delivery",
                    approval_type=ApprovalType.FINAL_BRIEF,
                    required=True,  # Always require approval for final brief
                    timeout_seconds=3600,  # 1 hour
                    auto_approve_conditions={"word_count": {"min": 500, "max": 5000}}
                )
            ],
            default_timeout=3600,
            auto_approve_low_risk=True,
            require_dual_approval=False
        )
    
    async def create_approval_request(
        self,
        approval_type: ApprovalType,
        title: str,
        description: str,
        data: Dict[str, Any],
        context: Dict[str, Any] = None,
        requester: str = "intelligence_agent",
        priority: str = "NORMAL"
    ) -> ApprovalRequest:
        """Create a new approval request."""
        
        request_id = str(uuid.uuid4())
        
        # Find matching checkpoint configuration
        checkpoint = self._find_checkpoint(approval_type)
        timeout_seconds = checkpoint.timeout_seconds if checkpoint else self.config.default_timeout
        
        request = ApprovalRequest(
            id=request_id,
            type=approval_type,
            title=title,
            description=description,
            data=data,
            context=context or {},
            requester=requester,
            expires_at=datetime.now() + timedelta(seconds=timeout_seconds),
            priority=priority
        )
        
        self.pending_approvals[request_id] = request
        
        logging.info(f"Created approval request {request_id}: {title}")
        return request
    
    def _find_checkpoint(self, approval_type: ApprovalType) -> Optional[HITLCheckpoint]:
        """Find checkpoint configuration for approval type."""
        for checkpoint in self.config.checkpoints:
            if checkpoint.approval_type == approval_type:
                return checkpoint
        return None
    
    async def check_auto_approval(self, request: ApprovalRequest) -> bool:
        """Check if request can be auto-approved based on conditions."""
        
        if not self.config.enabled:
            return True  # Auto-approve if HITL is disabled
        
        checkpoint = self._find_checkpoint(request.type)
        if not checkpoint or not checkpoint.auto_approve_conditions:
            return False
        
        conditions = checkpoint.auto_approve_conditions
        data = request.data
        
        # Check confidence threshold
        if "confidence" in conditions:
            required_confidence = conditions["confidence"]
            actual_confidence = data.get("confidence", 0.0)
            if actual_confidence >= required_confidence:
                logging.info(f"Auto-approving {request.id} due to high confidence: {actual_confidence}")
                return True
        
        # Check word count for briefs
        if "word_count" in conditions and request.type == ApprovalType.FINAL_BRIEF:
            word_limits = conditions["word_count"]
            brief_content = data.get("answer", "")
            word_count = len(brief_content.split())
            
            min_words = word_limits.get("min", 0)
            max_words = word_limits.get("max", float('inf'))
            
            if min_words <= word_count <= max_words:
                logging.info(f"Auto-approving {request.id} due to appropriate word count: {word_count}")
                return True
        
        return False
    
    async def wait_for_approval(
        self,
        request: ApprovalRequest,
        timeout_seconds: Optional[int] = None
    ) -> ApprovalResponse:
        """Wait for human approval or timeout."""
        
        # Check for auto-approval first
        if await self.check_auto_approval(request):
            response = ApprovalResponse(
                request_id=request.id,
                status=ApprovalStatus.APPROVED,
                feedback="Auto-approved based on conditions",
                approver="system",
                notes="Automatic approval due to meeting predefined criteria"
            )
            self.approval_responses[request.id] = response
            return response
        
        # Check if checkpoint is required
        checkpoint = self._find_checkpoint(request.type)
        if checkpoint and not checkpoint.required:
            logging.info(f"Checkpoint {request.type} is optional, auto-approving")
            response = ApprovalResponse(
                request_id=request.id,
                status=ApprovalStatus.APPROVED,
                feedback="Auto-approved - optional checkpoint",
                approver="system",
                notes="Optional checkpoint, proceeding without human approval"
            )
            self.approval_responses[request.id] = response
            return response
        
        # Wait for human approval
        timeout = timeout_seconds or (request.expires_at - datetime.now()).total_seconds()
        timeout = max(1, int(timeout))  # Ensure positive timeout
        
        logging.info(f"Waiting for human approval for {request.id} (timeout: {timeout}s)")
        
        try:
            # In a real implementation, this would integrate with a UI/notification system
            # For now, we'll simulate human approval or timeout
            await asyncio.sleep(min(5, timeout))  # Simulate quick approval for testing
            
            # Check if approval was provided during wait
            if request.id in self.approval_responses:
                return self.approval_responses[request.id]
            
            # Simulate approval decision for testing
            if request.type == ApprovalType.FINAL_BRIEF:
                # Simulate human reviewing and approving the brief
                response = ApprovalResponse(
                    request_id=request.id,
                    status=ApprovalStatus.APPROVED,
                    feedback="Brief looks good, approved for delivery",
                    approver="test_user",
                    notes="Simulated human approval for testing"
                )
            else:
                # Auto-approve other types for testing
                response = ApprovalResponse(
                    request_id=request.id,
                    status=ApprovalStatus.APPROVED,
                    feedback="Approved for testing",
                    approver="test_user"
                )
            
            self.approval_responses[request.id] = response
            return response
            
        except asyncio.TimeoutError:
            # Handle timeout
            response = ApprovalResponse(
                request_id=request.id,
                status=ApprovalStatus.TIMEOUT,
                feedback="Approval request timed out",
                approver="system",
                notes=f"No response received within {timeout} seconds"
            )
            self.approval_responses[request.id] = response
            return response
    
    async def submit_approval(self, response: ApprovalResponse) -> bool:
        """Submit human approval response."""
        
        if response.request_id not in self.pending_approvals:
            logging.error(f"Approval request {response.request_id} not found")
            return False
        
        self.approval_responses[response.request_id] = response
        
        # Remove from pending
        if response.request_id in self.pending_approvals:
            del self.pending_approvals[response.request_id]
        
        logging.info(f"Received approval response for {response.request_id}: {response.status}")
        return True
    
    def get_pending_approvals(self) -> List[ApprovalRequest]:
        """Get all pending approval requests."""
        return list(self.pending_approvals.values())
    
    def get_approval_status(self, request_id: str) -> Optional[ApprovalStatus]:
        """Get status of an approval request."""
        if request_id in self.approval_responses:
            return self.approval_responses[request_id].status
        elif request_id in self.pending_approvals:
            return ApprovalStatus.PENDING
        return None
    
    async def create_session(self, workflow_type: str, initial_state: Dict[str, Any]) -> HITLSession:
        """Create a new HITL session for state tracking."""
        
        session_id = str(uuid.uuid4())
        session = HITLSession(
            session_id=session_id,
            workflow_type=workflow_type,
            state_snapshot=initial_state,
            pending_approvals=[],
            completed_approvals=[]
        )
        
        self.sessions[session_id] = session
        return session
    
    async def update_session(self, session_id: str, state_update: Dict[str, Any]) -> bool:
        """Update session state."""
        
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        session.state_snapshot.update(state_update)
        session.updated_at = datetime.now()
        
        return True
    
    def get_session(self, session_id: str) -> Optional[HITLSession]:
        """Get session by ID."""
        return self.sessions.get(session_id)
    
    async def rollback_to_checkpoint(self, session_id: str, checkpoint_name: str) -> Optional[Dict[str, Any]]:
        """Rollback session state to a specific checkpoint."""
        
        session = self.get_session(session_id)
        if not session:
            return None
        
        # In a real implementation, this would restore state from a specific checkpoint
        # For now, return the current state snapshot
        logging.info(f"Rolling back session {session_id} to checkpoint {checkpoint_name}")
        return session.state_snapshot.copy()
    
    def cleanup_expired_requests(self):
        """Clean up expired approval requests."""
        
        now = datetime.now()
        expired_ids = []
        
        for request_id, request in self.pending_approvals.items():
            if request.expires_at and request.expires_at < now:
                expired_ids.append(request_id)
        
        for request_id in expired_ids:
            logging.info(f"Cleaning up expired approval request: {request_id}")
            del self.pending_approvals[request_id]
            
            # Create timeout response
            response = ApprovalResponse(
                request_id=request_id,
                status=ApprovalStatus.TIMEOUT,
                feedback="Request expired",
                approver="system"
            )
            self.approval_responses[request_id] = response