# Intelligence Agent

## Overview

# **Virgil Intelligence Agent**

### **Contextual Intelligence Briefs**

- AI-powered briefs combining all internal + external intelligence on investor
- Interactive briefs that users can modify conversationally (e.g., "Make ESG section more quantitative" or “Include investment team profiles”)
- Identify expected questions, analysis, and insights from available data
- Quote from a potential customer: “What I need to focus on changes from day-to-day, <PERSON> can help me identify where my critical thinking should be focused.”


### **Product Design:**

**Interactive Brief Generator**

- **Output Format: Initial Draft will be in our predefined format**

- **Conversational Refinement:** Chat-style interface to modify briefs ("Make ESG section more quantitative") – should be like creating artifacts in Claude or Gemini
- **Source Attribution:** Clear citations linking to original documents or market data
- **Export Options:** One-click export to PDF, Word

## **Strategic Vision: Market Intelligence + Investor Research**

The Intelligence Agent is designed to **elevate the IQ of IR professionals** by providing comprehensive market intelligence, investor research, and AI-powered strategic document creation that connects internal fund knowledge with external market data. This creates contextually relevant insights and strategically optimized fund positioning materials that dramatically increase the probability of success in every investor interaction.

It is also a key building block towards an AI-native Virgil CRM specifically designed for investor relations. We will initially integrate with industry CRMs, including Salesforce, DealCloud, and Dynamo, to disrupt the incumbents with better insights and intelligence of our own.

The **simple MVP,** 
- Provide the ability to generate an intelligence brief on each investor (or add new investors), which will access external data that is not used in the DDQ/RFP generation process
- Create a customized Master DDQ that transforms years of historical responses and insights from all investor diligence into a competitively DDQ with AI reasoning, citations and insights.

### **Use Cases**

**Scenario 1: CalPERS Quarterly Review Intelligence** Preparing for CalPERS quarterly review, the Intelligence Agent provides: (NOTE: this means investors with status of Active)

- Analysis showing CalPERS increased private credit allocation 15% this quarter (Preqin data)
- Historical DDQ pattern showing consistent focus on ESG metrics and climate risk
- Performance positioning showing fund's 18.2% IRR ranks top 10% among CalPERS PE investments
- Market context explaining how current interest rate environment affects their allocation strategy
- Competitive intelligence showing three peer funds recently presented similar strategies

**Scenario 2: Prospective Investor Research** Before initial outreach to Ontario Teachers', the Intelligence Agent delivers: (NOTE: this means investors with status of Prospect)

- Recent $2B private equity allocation announcement analysis (PitchBook tracking)
- Investment committee structure and decision-making timeline research
- Portfolio gap analysis showing alignment with fund strategy
- Market timing intelligence indicating optimal approach window
- Strategic positioning recommendations based on their stated investment priorities

**Scenario 3: Market Crisis Intelligence** During market volatility, the Intelligence Agent provides:

- Investor-specific analysis of how market conditions affect their mandate
- Historical performance during previous market stress periods
- Peer comparison showing relative fund positioning during downturns
- Strategic talking points addressing anticipated concerns
- Relationship risk assessment and recommended engagement approach

## User Acceptance Criteria

### AC3.1: Generate Brief from Investor Page

**Given** I am viewing an investor profile

**When** I click "Generate Brief"

**Then** Intelligence chat opens with a comprehensive brief

**And** Brief includes internal relationship data + external market intelligence

**And** All information has source citations

### AC3.2: Generate Brief from Intelligence Page

**Given** I am in Intelligence chat

**When** I type "Generate brief for [Investor Name]"

**Then** A comprehensive brief is generated

**And** Brief format matches predefined template

### AC3.3: Conversational Refinement

**Given** An intelligence brief has been generated

**When** I request modifications (e.g., "Make ESG section more quantitative")

**Then** The brief updates accordingly

**And** Changes are reflected in real-time like Claude artifacts

### AC3.4: Export Brief

**Given** An intelligence brief is displayed

**When** I click export options

**Then** I can download as PDF or Word document

**And** Formatting is preserved in exported version

## Cross-Component Acceptance Criteria

### AC5.1: Source Attribution

**Given** Any intelligence output is generated

**When** I view the content

**Then** All claims have clear source citations

**And** Citations link back to original documents or data sources

### AC5.2: Performance

**Given** I request any intelligence generation

**When** Processing begins

**Then** Initial response appears within 3 seconds

**And** Loading states are clearly indicated
