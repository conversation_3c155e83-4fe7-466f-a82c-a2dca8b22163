"""
Market & Sentiment Analysis Agent

Focused on real-time market data, macroeconomic indicators, and market sentiment
analysis from external sources like news feeds and market data providers.
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.tools import tool
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters
from .state import IntelligenceState


class MarketAgent:
    """
    Market & Sentiment Analysis Agent
    
    Specializes in gathering external market intelligence and sentiment analysis.
    """
    
    def __init__(self, model_hyperparams: ModelHyperparameters = None):
        self.model_hyperparams = model_hyperparams or ModelHyperparameters()
        self.llm = new_llm(self.model_hyperparams)
    
    def get_market_data(self, investor_type: str, geography: str = "Global") -> str:
        """
        Retrieve relevant market data and trends for investor type and geography.
        
        Args:
            investor_type: Type of investor (e.g., PENSION_FUND, SOVEREIGN_WEALTH)
            geography: Geographic focus area
        """
        try:
            # Mock market data - in real implementation would call market data APIs
            current_date = datetime.now()
            
            mock_market_data = {
                "market_conditions": {
                    "interest_rates": {
                        "fed_funds_rate": 5.25,
                        "10_year_treasury": 4.35,
                        "trend": "stable"
                    },
                    "equity_markets": {
                        "sp500_ytd": 12.5,
                        "volatility_index": 18.2,
                        "trend": "positive"
                    },
                    "private_markets": {
                        "pe_fundraising_ytd": 285000000000,  # $285B
                        "dry_powder": 1**********00,  # $1.2T
                        "trend": "selective"
                    }
                },
                "sector_trends": {
                    "technology": {"allocation_trend": "increasing", "performance": "strong"},
                    "healthcare": {"allocation_trend": "stable", "performance": "moderate"},
                    "infrastructure": {"allocation_trend": "increasing", "performance": "strong"},
                    "real_estate": {"allocation_trend": "decreasing", "performance": "weak"}
                },
                "peer_activity": {
                    "similar_investors": [
                        {"name": "Ontario Teachers", "recent_allocation": "Infrastructure", "amount": **********},
                        {"name": "CalSTRS", "recent_allocation": "Private Credit", "amount": **********}
                    ]
                },
                "data_timestamp": current_date.isoformat()
            }
            
            return json.dumps(mock_market_data, indent=2)
            
        except Exception as e:
            logging.error(f"Market data retrieval failed: {e}")
            return json.dumps({"error": f"Failed to retrieve market data: {str(e)}"})
    
    def analyze_sentiment(self, investor_name: str, timeframe_days: int = 30) -> str:
        """
        Analyze market sentiment and news related to specific investor.
        
        Args:
            investor_name: Name of the investor to analyze
            timeframe_days: Number of days to look back for sentiment analysis
        """
        try:
            # Mock sentiment analysis - in real implementation would use news APIs
            mock_sentiment = {
                "overall_sentiment": "neutral_positive",
                "sentiment_score": 0.65,  # -1 to 1 scale
                "news_mentions": 12,
                "key_themes": [
                    "ESG investment strategy",
                    "Infrastructure allocation increase", 
                    "Climate risk management"
                ],
                "recent_headlines": [
                    f"{investor_name} announces $2B infrastructure commitment",
                    f"{investor_name} strengthens ESG investment criteria",
                    f"{investor_name} reports strong Q3 performance"
                ],
                "peer_comparison": {
                    "relative_sentiment": "above_average",
                    "industry_sentiment": 0.58
                },
                "analysis_period": f"Last {timeframe_days} days"
            }
            
            return json.dumps(mock_sentiment, indent=2)
            
        except Exception as e:
            logging.error(f"Sentiment analysis failed: {e}")
            return json.dumps({"error": f"Failed to analyze sentiment: {str(e)}"})
    
    def get_competitive_intelligence(self, investor_type: str, geography: str) -> str:
        """
        Gather competitive intelligence on peer investors and market positioning.
        
        Args:
            investor_type: Type of investor for peer comparison
            geography: Geographic region for competitive analysis
        """
        try:
            # Mock competitive intelligence
            mock_competitive_data = {
                "peer_analysis": {
                    "total_peers_analyzed": 15,
                    "average_aum": 45000000000,  # $45B
                    "allocation_trends": {
                        "private_equity": {"average_allocation": 25, "trend": "stable"},
                        "private_credit": {"average_allocation": 15, "trend": "increasing"},
                        "infrastructure": {"average_allocation": 12, "trend": "increasing"},
                        "real_estate": {"average_allocation": 18, "trend": "decreasing"}
                    }
                },
                "market_positioning": {
                    "innovation_leader": ["Ontario Teachers", "GIC"],
                    "esg_leaders": ["AP7", "NBIM"],
                    "performance_leaders": ["CPP Investments", "Temasek"]
                },
                "recent_strategies": [
                    "Increased focus on climate transition investments",
                    "Expansion into private credit markets",
                    "Technology infrastructure investments"
                ]
            }
            
            return json.dumps(mock_competitive_data, indent=2)
            
        except Exception as e:
            logging.error(f"Competitive intelligence gathering failed: {e}")
            return json.dumps({"error": f"Failed to gather competitive intelligence: {str(e)}"})
    
    async def process(self, state: IntelligenceState) -> IntelligenceState:
        """
        Main market agent processing logic.
        
        Gathers external market intelligence and sentiment data.
        """
        messages = state["messages"]
        investor_data = state.get("investor_data", {})
        
        if not investor_data:
            error_msg = "No investor data available. Knowledge agent must run first."
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {**state.get("analysis_results", {}), "market_error": error_msg}
            }
        
        try:
            investor_name = investor_data.get("name", "Unknown")
            investor_type = investor_data.get("type", "UNKNOWN")
            geography = investor_data.get("geography", "Global")
            
            # Gather market data
            market_data_str = self.get_market_data(investor_type, geography)
            market_data = json.loads(market_data_str)
            
            # Analyze sentiment
            sentiment_data_str = self.analyze_sentiment(investor_name)
            sentiment_data = json.loads(sentiment_data_str)
            
            # Get competitive intelligence
            competitive_data_str = self.get_competitive_intelligence(investor_type, geography)
            competitive_data = json.loads(competitive_data_str)
            
            # Structure external data
            external_data = {
                "market_conditions": market_data.get("market_conditions", {}),
                "sector_trends": market_data.get("sector_trends", {}),
                "peer_activity": market_data.get("peer_activity", {}),
                "sentiment_analysis": sentiment_data,
                "competitive_intelligence": competitive_data,
                "data_sources": ["Market Data APIs", "News Sentiment Analysis", "Peer Intelligence"]
            }
            
            # Create response message
            fed_rate = market_data.get("market_conditions", {}).get("interest_rates", {}).get("fed_funds_rate", "N/A")
            sentiment_score = sentiment_data.get("sentiment_score", 0)
            peer_count = competitive_data.get("peer_analysis", {}).get("total_peers_analyzed", 0)
            
            response_content = f"""Market Agent Analysis Complete:

**Market Conditions:**
- Fed Funds Rate: {fed_rate}%
- Private Markets Fundraising YTD: ${market_data.get('market_conditions', {}).get('private_markets', {}).get('pe_fundraising_ytd', 0) / 1000000000:.0f}B
- Market Trend: {market_data.get('market_conditions', {}).get('private_markets', {}).get('trend', 'Unknown')}

**Sentiment Analysis:**
- Overall Sentiment: {sentiment_data.get('overall_sentiment', 'Unknown')}
- Sentiment Score: {sentiment_score:.2f}/1.0
- Recent News Mentions: {sentiment_data.get('news_mentions', 0)}

**Competitive Intelligence:**
- Peer Investors Analyzed: {peer_count}
- Key Market Trends: Infrastructure & Private Credit allocation increases

Ready for intelligence brief generation."""
            
            return {
                **state,
                "messages": messages + [AIMessage(content=response_content)],
                "external_data": external_data,
                "analysis_results": {**state.get("analysis_results", {}), "market_intelligence": external_data}
            }
            
        except Exception as e:
            logging.error(f"Market agent processing failed: {e}")
            error_msg = f"Failed to process market intelligence: {str(e)}"
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {**state.get("analysis_results", {}), "market_error": error_msg}
            }