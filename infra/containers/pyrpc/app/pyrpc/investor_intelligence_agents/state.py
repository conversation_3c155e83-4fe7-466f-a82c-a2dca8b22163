"""
Intelligence Agent State Management

Defines the shared state schema for the multi-agent intelligence system.
"""
from typing import Annotated, Dict, List, Optional
from typing_extensions import TypedDict
from langchain_core.messages import BaseMessage
from langgraph.graph import add_messages
import operator


class IntelligenceState(TypedDict):
    """
    Shared state for the Intelligence Agent system.
    
    This TypedDict enforces a structured schema for all agent communications,
    ensuring data consistency and simplifying debugging.
    """
    messages: Annotated[List[BaseMessage], add_messages]
    classification: str  # Intent classification (e.g., "brief_generation", "market_analysis")
    confidence: float  # Confidence score 0.0-1.0 for routing decisions
    analysis_results: Dict  # Structured results from expert agents
    investor_id: Optional[str]  # Target investor ID
    investor_name: Optional[str]  # Target investor name
    investor_data: Optional[Dict]  # Internal investor data
    external_data: Optional[Dict]  # External market/intelligence data
    brief_sections: Optional[Dict]  # Generated brief sections
    citations: Optional[List[Dict]]  # Source citations
    hitl_session_id: Optional[str]  # Human-in-the-loop session ID
    pending_approvals: Optional[List[str]]  # List of pending approval request IDs
    approval_required: Optional[bool]  # Whether human approval is required
    hitl_checkpoints: Optional[List[str]]  # Completed HITL checkpoints