"""
Reporting & Visualization Agent

Synthesizes complex data into clear, digestible intelligence briefs with proper
formatting, citations, and export capabilities.
"""
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.tools import tool
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters
from pyrpc.models import RAGResponseWithCitationsFinalAnwser, CitationFinalAnwser, DocumentMetadata
from .state import IntelligenceState


class ReportingAgent:
    """
    Reporting & Visualization Agent
    
    Specializes in synthesizing intelligence data into comprehensive briefs.
    """
    
    def __init__(self, model_hyperparams: ModelHyperparameters = None):
        self.model_hyperparams = model_hyperparams or ModelHyperparameters()
        self.llm = new_llm(self.model_hyperparams)
    
    def generate_brief_template(self, investor_data: Dict, external_data: Dict) -> str:
        """Generate a structured intelligence brief template."""
        
        investor_name = investor_data.get("name", "Unknown Investor")
        investor_type = investor_data.get("type", "Unknown")
        aum = investor_data.get("aum", 0) / 100000000  # Convert from cents to billions
        
        # Market conditions
        market_conditions = external_data.get("market_conditions", {})
        sentiment = external_data.get("sentiment_analysis", {})
        competitive = external_data.get("competitive_intelligence", {})
        
        brief_template = f"""# Intelligence Brief: {investor_name}

*Generated on {datetime.now().strftime("%B %d, %Y")}*

## Executive Summary

{investor_name} is a {investor_type.replace('_', ' ').title()} with ${aum:.1f}B in assets under management. Current market conditions present both opportunities and challenges for their investment strategy.

## Investor Profile

**Basic Information:**
- **Name:** {investor_name}
- **Type:** {investor_type.replace('_', ' ').title()}
- **Status:** {investor_data.get('status', 'Unknown')}
- **Assets Under Management:** ${aum:.1f}B
- **Geography:** {investor_data.get('geography', 'Unknown')}

**Investment Focus:**
{self._format_list(investor_data.get('investment_focus', []))}

**ESG Integration:** {'Yes' if investor_data.get('esg_focus') else 'No'}

## Market Intelligence

**Current Market Conditions:**
- **Fed Funds Rate:** {market_conditions.get('interest_rates', {}).get('fed_funds_rate', 'N/A')}%
- **10-Year Treasury:** {market_conditions.get('interest_rates', {}).get('10_year_treasury', 'N/A')}%
- **S&P 500 YTD:** {market_conditions.get('equity_markets', {}).get('sp500_ytd', 'N/A')}%
- **Private Markets Fundraising:** ${market_conditions.get('private_markets', {}).get('pe_fundraising_ytd', 0) / 1000000000:.0f}B YTD

**Sector Trends:**
{self._format_sector_trends(external_data.get('sector_trends', {}))}

## Sentiment Analysis

**Overall Sentiment:** {sentiment.get('overall_sentiment', 'Unknown').replace('_', ' ').title()}
**Sentiment Score:** {sentiment.get('sentiment_score', 0):.2f}/1.0
**Recent News Mentions:** {sentiment.get('news_mentions', 0)}

**Key Themes:**
{self._format_list(sentiment.get('key_themes', []))}

## Competitive Intelligence

**Peer Analysis:**
- **Total Peers Analyzed:** {competitive.get('peer_analysis', {}).get('total_peers_analyzed', 0)}
- **Average AUM:** ${competitive.get('peer_analysis', {}).get('average_aum', 0) / 1000000000:.0f}B

**Recent Peer Activity:**
{self._format_peer_activity(external_data.get('peer_activity', {}))}

## Strategic Recommendations

Based on current market conditions and peer activity:

1. **Infrastructure Focus:** Market trends show increasing allocation to infrastructure investments
2. **Private Credit Opportunity:** Growing interest in private credit markets among peer institutions
3. **ESG Integration:** Continued emphasis on ESG criteria aligns with market expectations
4. **Risk Management:** Current interest rate environment requires careful duration risk management

## Key Questions & Discussion Points

- How does current market volatility affect your risk tolerance?
- What is your view on the infrastructure investment opportunity?
- How are you positioning for potential interest rate changes?
- What role does ESG play in your current investment strategy?

---

*This brief combines internal relationship data with external market intelligence. All market data is current as of {datetime.now().strftime("%B %d, %Y")}.*
"""
        return brief_template
    
    def _format_list(self, items: List[str]) -> str:
        """Format a list of items as bullet points."""
        if not items:
            return "- None specified"
        return "\n".join([f"- {item}" for item in items])
    
    def _format_sector_trends(self, trends: Dict) -> str:
        """Format sector trends data."""
        if not trends:
            return "- No sector trend data available"
        
        formatted = []
        for sector, data in trends.items():
            trend = data.get('allocation_trend', 'Unknown')
            performance = data.get('performance', 'Unknown')
            formatted.append(f"- **{sector.title()}:** {trend.title()} allocation, {performance} performance")
        
        return "\n".join(formatted)
    
    def _format_peer_activity(self, peer_data: Dict) -> str:
        """Format peer activity data."""
        similar_investors = peer_data.get('similar_investors', [])
        if not similar_investors:
            return "- No recent peer activity data available"
        
        formatted = []
        for investor in similar_investors:
            name = investor.get('name', 'Unknown')
            allocation = investor.get('recent_allocation', 'Unknown')
            amount = investor.get('amount', 0) / 1000000000  # Convert to billions
            formatted.append(f"- **{name}:** ${amount:.1f}B allocation to {allocation}")
        
        return "\n".join(formatted)
    
    def generate_citations(self, brief_content: str, data_sources: List[str]) -> str:
        """
        Generate proper citations for the intelligence brief.
        
        Args:
            brief_content: The generated brief content
            data_sources: List of data sources used
        """
        try:
            citations = []
            
            # Generate citations based on data sources
            for i, source in enumerate(data_sources, 1):
                citation = {
                    "sourceId": i,  # int, not string
                    "quote": f"Data sourced from {source}",
                    "filename": f"{source.lower().replace(' ', '_')}.json",  # Use alias 'filename', not 'fileName'
                    "metadata": {
                        "languages": ["eng"],
                        "page_number": None,
                        "page_name": None,
                        "text_as_html": None,
                        "image_base64": None,
                        "source": source,
                        "filetype": "json",
                        "page_content": None,
                        "document_chunk_id": f"intelligence_source_{i}",
                        "_distance": None
                    }
                }
                citations.append(citation)
            
            return json.dumps(citations, indent=2)
            
        except Exception as e:
            logging.error(f"Citation generation failed: {e}")
            return json.dumps([])
    
    async def process(self, state: IntelligenceState) -> IntelligenceState:
        """
        Main reporting agent processing logic.
        
        Generates comprehensive intelligence brief from gathered data.
        """
        messages = state["messages"]
        investor_data = state.get("investor_data", {})
        external_data = state.get("external_data", {})
        analysis_results = state.get("analysis_results", {})
        
        # Check if we have required data
        if not investor_data:
            error_msg = "No investor data available for brief generation."
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {**analysis_results, "reporting_error": error_msg}
            }
        
        if not external_data:
            error_msg = "No market intelligence data available for brief generation."
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {**analysis_results, "reporting_error": error_msg}
            }
        
        try:
            # Generate the intelligence brief
            brief_content = self.generate_brief_template(investor_data, external_data)
            
            # Collect all data sources for citations
            data_sources = []
            data_sources.extend(analysis_results.get("investor_data", {}).get("data_sources", []))
            data_sources.extend(external_data.get("data_sources", []))
            
            # Generate citations
            citations_str = self.generate_citations(brief_content, data_sources)
            citations_data = json.loads(citations_str)
            
            # Create structured citations for the response
            structured_citations = []
            for citation in citations_data:
                structured_citation = CitationFinalAnwser(
                    quote=citation.get("quote", ""),
                    document_chunk_id=citation.get("document_chunk_id")
                )
                structured_citations.append(structured_citation)
            
            # Create final response
            final_response = RAGResponseWithCitationsFinalAnwser(
                answer=brief_content,
                citations=structured_citations
            )
            
            # Update state with generated brief
            brief_sections = {
                "executive_summary": "Generated",
                "investor_profile": "Generated", 
                "market_intelligence": "Generated",
                "sentiment_analysis": "Generated",
                "competitive_intelligence": "Generated",
                "strategic_recommendations": "Generated"
            }
            
            response_message = AIMessage(
                content=f"Intelligence Brief Generated Successfully:\n\n{brief_content}"
            )
            
            return {
                **state,
                "messages": messages + [response_message],
                "brief_sections": brief_sections,
                "citations": citations_data,
                "analysis_results": {
                    **analysis_results, 
                    "final_brief": final_response.model_dump(),
                    "brief_generated": True
                }
            }
            
        except Exception as e:
            logging.error(f"Reporting agent processing failed: {e}")
            error_msg = f"Failed to generate intelligence brief: {str(e)}"
            return {
                **state,
                "messages": messages + [AIMessage(content=error_msg)],
                "analysis_results": {**analysis_results, "reporting_error": error_msg}
            }