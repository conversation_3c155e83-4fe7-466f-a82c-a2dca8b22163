from pyrpc.rag_response.chat_model import ChatModel

#!/usr/bin/env python3
"""
Test script for the Intelligence Brief endpoint

This script tests the new /pyrpc/generate-answer/intelligence-brief endpoint
to ensure the Intelligence Agent system works correctly.
"""
import asyncio
import json
import aiohttp
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_intelligence_endpoint():
    """Test the intelligence brief endpoint."""
    
    # Test configuration
    base_url = "http://localhost:8000"  # Adjust as needed
    endpoint = f"{base_url}/pyrpc/generate-answer/intelligence-brief"
    
    # Test payload
    test_payload = {
        "user_message": "Generate intelligence brief for CalPERS",
        "context": {
            "institutionName": "Test Investment Management",
            "orgId": "test_org_001"
        },
        "modelParameters": {
            "model": ChatModel.GPT_4O_MINI,
            "temperature": 0.0,
            "top_p": 1e-9,
            "thinking_mode": False,
            "agentic_mode_langgraph": False,
            "citation_verification_mode": False,
            "answer_reflexion_mode": False
        },
        "investor_name": "CalPERS",
        "investor_id": None
    }
    
    print("Testing Intelligence Brief Endpoint")
    print("=" * 50)
    print(f"Endpoint: {endpoint}")
    print(f"Payload: {json.dumps(test_payload, indent=2)}")
    print("=" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(endpoint, json=test_payload) as response:
                print(f"Response Status: {response.status}")
                print(f"Response Headers: {dict(response.headers)}")
                print("-" * 30)
                
                if response.status == 200:
                    # Stream the response
                    response_count = 0
                    async for line in response.content:
                        if line:
                            response_count += 1
                            try:
                                # Parse JSON response
                                response_data = json.loads(line.decode('utf-8').strip())
                                status = response_data.get('status', 'UNKNOWN')
                                answer_preview = response_data.get('answer', '')[:100] + "..." if len(response_data.get('answer', '')) > 100 else response_data.get('answer', '')
                                
                                print(f"Response {response_count}: [{status}] {answer_preview}")
                                
                                # Stop after getting final response
                                if status == "READY":
                                    print("\n" + "=" * 50)
                                    print("FINAL INTELLIGENCE BRIEF:")
                                    print("=" * 50)
                                    print(response_data.get('answer', 'No answer provided'))
                                    break
                                elif status == "ERROR":
                                    print(f"Error: {response_data.get('answer', 'Unknown error')}")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"Failed to parse response line: {line}")
                                print(f"JSON Error: {e}")
                            except Exception as e:
                                print(f"Error processing response: {e}")
                                
                        # Safety check
                        if response_count > 20:
                            print("Too many responses, stopping test")
                            break
                else:
                    # Handle error responses
                    error_text = await response.text()
                    print(f"Error Response: {error_text}")
                    
    except aiohttp.ClientError as e:
        print(f"HTTP Client Error: {e}")
    except Exception as e:
        print(f"Unexpected Error: {e}")
        import traceback
        traceback.print_exc()


async def test_simple_request():
    """Test with a simpler request format."""
    
    base_url = "http://localhost:5328"
    endpoint = f"{base_url}/pyrpc/generate-answer/intelligence-brief"
    
    # Minimal test payload
    simple_payload = {
        "user_message": "Generate brief for Ontario Teachers",
        "context": {
            "institutionName": "Test Fund",
            "orgId": "test123"
        },
        "modelParameters": {
            "model": "gpt-4o-mini",
            "temperature": 0.0
        },
        "tracingContext": {
            "traceId": "simple_test_123"
        }
    }
    
    print("\nTesting Simple Intelligence Brief Request")
    print("=" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(endpoint, json=simple_payload) as response:
                print(f"Status: {response.status}")
                
                if response.status == 200:
                    count = 0
                    async for line in response.content:
                        if line:
                            count += 1
                            try:
                                data = json.loads(line.decode('utf-8').strip())
                                status = data.get('status', 'UNKNOWN')
                                print(f"  [{status}] Response {count}")
                                
                                if status in ["READY", "ERROR"]:
                                    break
                                    
                            except:
                                pass
                                
                        if count > 10:
                            break
                            
                    print(f"✓ Simple test completed with {count} responses")
                else:
                    error_text = await response.text()
                    print(f"✗ Error: {response.status} - {error_text}")
                    
    except Exception as e:
        print(f"✗ Simple test failed: {e}")


if __name__ == "__main__":
    print("Intelligence Brief Endpoint Test")
    print("Make sure the FastAPI server is running on localhost:8000")
    print("You can start it with: uvicorn pyrpc.index:app --reload --port 8000")
    print()
    
    # Run tests
    asyncio.run(test_simple_request())
    asyncio.run(test_intelligence_endpoint())