from typing import List
from jinja2 import Template
from pydantic import BaseModel, Field
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters
from google import genai
from pyrpc.utils.tracing import tracing

import logging

from pyrpc.utils.get_env_var import get_env_var
logging.basicConfig(level=logging.INFO)

class DocumentChunkInput(BaseModel):
    """A single document chunk input"""
    chunk: str = Field(description="The document chunk as provided in the input list.")

class DocumentInput(BaseModel):
    """A single document input"""
    chunks: List[DocumentChunkInput] = Field(description="The document chunks as provided in the input list.")

class CategoryInput(BaseModel):
    """A single category input"""
    id: str = Field(description="The category ID as provided in the input list.")
    description: str = Field(description="The category description as provided in the input list.")

class CategoryAnalyzeResponse(BaseModel):
    """A single response for category assignment"""
    name: str = Field(description="The category name as provided in the input list.")
    result: bool = Field(description="Status of the category assignment. True if the document was assigned to the category, False otherwise.")
    reason: str = Field(description="A single paragraph explanation of the reason why the document was assigned to this category. Must be concise, short, and logical.")

class DocumentCategoryAnalyzeResponse(BaseModel):
    """A list of responses for category assignment"""
    categories: List[CategoryAnalyzeResponse] = Field(description="List of category responses")

tool_prompt = """ 
You are a helpful assistant, specializing in classifying financial documents.

You will be given a document, in JSON format.
The document contains a document ID, a document content, and a document metadata.

Your task is to first reconstruct the document, and then assign up to as many categories as you can to the document, based on the category names and description, by analyzing the document content.
Assign the categories whos summaries best describe the document. If document doesnt exist let mark it as False, otherwise mark it as True.
Need to return all of input categories with True or False.

Output Format: Return your results as valid JSON.

Formatting:
- Do not include any extra text or explanations outside of the JSON block.
Validation:
- Do not create any new categories. Only use the categories provided.
- Do not assign a document to a category that does not exist in the list of categories.
- The category name that you assign must match the associated category name, as passed in the input.

Here is the document name:
{{ name }}

Here is the document chunks:
{{ chunks }}

Here is the categories:
{{ categories }}

"""

class DocumentInput(BaseModel):
    """A single document input"""
    id: str = Field(description="The document ID as provided in the input list.")
    chunks: List[str] = Field(description="The document chunks as provided in the input list.")
    name: str = Field(description="The document name as provided in the input list.")

class CategoryInput(BaseModel):
    """A single category input"""
    id: str = Field(description="The category ID as provided in the input list.")
    name: str = Field(description="The category name as provided in the input list.")
    description: str = Field(description="The category description as provided in the input list.")

class CategoryResponse(BaseModel):
    """A single response for category assignment"""
    id: str = Field(description="The category ID as provided in the input list.")
    name: str = Field(description="The category name as provided in the input list.")
    reason: str = Field(description="A single paragraph explanation of the reason why the document was assigned to this category. Must be concise, short, and logical.")

class ClassifyDocumentCategoryResponse(BaseModel):
    categories: List[CategoryResponse] = Field(description="A list of responses, one for each question.")

def classifier_categories(
    name: str,
    chunks: List[str],
    categories: List[CategoryInput],
    model_hyperparameters: ModelHyperparameters,
    secret: dict = None,
    **kwargs
) :
    logging.info('Classifier document category response...')

    try:
        template = Template(tool_prompt)
        prompt_content = template.render(
            name=name,
            chunks="\n\n".join(chunks),
            categories="\n".join([f"- CategoryName: {c['name']}, CategoryDescription: {c['description']}" for c in categories]),
        )

        structured_output_schema = DocumentCategoryAnalyzeResponse.model_json_schema()

        client = genai.Client(api_key=get_env_var("GEMINI_API_KEY", secret).split(",")[0])
        model = get_env_var("GEMINI_MODEL", secret)
        trace_context =kwargs.get("trace_context", {})
        
        with tracing.start_as_current_span(
            name="gemini_classify_categories",
            trace_context=trace_context,
            input={
                "model": model,
                "prompt": prompt_content,
                "temperature": model_hyperparameters.temperature,
                "top_p": model_hyperparameters.top_p,
                "document_name": name,
                "categories_count": len(categories),
                "chunks_count": len(chunks)
            },
        ) as generation_span:
            try:
                response = client.models.generate_content(
                    model=model,
                    contents=prompt_content,
                    config={
                        "response_mime_type": "application/json",
                        "response_schema": structured_output_schema,
                        "temperature": model_hyperparameters.temperature,
                        "top_p": model_hyperparameters.top_p,
                    },
                )
                
                # Update generation span with response metadata
                usage_metadata = getattr(response, 'usage_metadata', None)
                if generation_span and usage_metadata:
                    generation_span.update(
                        model=model,
                        output=response.parsed,
                        usage={
                            "input": usage_metadata.prompt_token_count,
                            "output": usage_metadata.candidates_token_count,
                            "total": usage_metadata.total_token_count,
                            "unit": "TOKENS"
                        }
                    )

                    logging.info(
                        f"Gemini API usage: {usage_metadata.prompt_token_count} input + {usage_metadata.candidates_token_count} output = {usage_metadata.total_token_count} total tokens")
                elif generation_span:
                    # Fallback if no usage metadata available
                    generation_span.update(
                        model=model,
                        output=response.parsed,
                        usage={}
                    )
            except Exception as llm_error:
                # Update generation span with error information
                if generation_span:
                    generation_span.update(
                        level="ERROR",
                        status_message=str(llm_error)
                    )
                raise llm_error
            
        print(f"Classifier Usage token count:")
        print(f"- Candidates token count: {response.usage_metadata.candidates_token_count}")
        print(f"- Prompt token count: {response.usage_metadata.prompt_token_count}")
        print(f"- Total token count: {response.usage_metadata.total_token_count}")

        response_list = response.parsed
        responses = []

        available_categories = {str(c["name"]).lower(): c for c in categories}

        for item in response_list.get('categories', []):
            if not item:
                continue

            categoryName = item.get('name', '')
            if not categoryName:
                continue

            if item.get('result', False) == False:
                logging.info(f"Ignore category {categoryName} which not related to document")
                continue

            category_input = available_categories.get(str(categoryName).lower())
            if not category_input:
                logging.error(f"Category {categoryName} not found in input list")
                continue
            
            try:
                responses.append(CategoryResponse(
                    id=str(category_input.get('id', '')),
                    name=category_input.get('name', ''),
                    reason=item.get('reason', ''),
                ))
                print(f"Found category: {category_input.get('name', '')}")
            except Exception as e:
                print(f"classifier_document_category: Error creating CategoryResponse: {e}")
                logging.error(f"classifier_document_category: Error creating CategoryResponse: {e}")
                raise Exception(f"Error creating CategoryResponse: {e}")
                
        return ClassifyDocumentCategoryResponse(categories=responses)

    except Exception as e:
        print(f"classifier_document_category: Error generating answer: {e}")
        logging.error(f"classifier_document_category: Error generating answer: {e}")
        raise e

def classifier_document_category(
    document: DocumentInput,
    categories: List[CategoryInput],
    model_hyperparameters: ModelHyperparameters,
    secret: dict = None,
    **kwargs
) -> ClassifyDocumentCategoryResponse:
    return classifier_categories(document.name, document.chunks, categories, model_hyperparameters, secret, **kwargs)
