from enum import Enum
import json
from datetime import datetime
from typing import Generator, List
import requests
from jinja2 import Template
import os

from pydantic import BaseModel, Field, validator

from pyrpc.rag_response.chat_model import ChatModel
from pyrpc.rag_response.response_style import ResponseStyle
from pyrpc.rag_response.persona_prompt_string import persona_prompt_string
# from pyrpc.utils.tracing import tracing
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters

import logging

from pyrpc.utils.get_env_var import get_env_var
logging.basicConfig(level=logging.INFO)

class DataMode(Enum):
    JUMBO_CHUNKS = 0
    CITATIONS = 1


tool_prompt = """ 
<role>
{{persona_prompt_string}}
</role>

<relevant_information>
Today's date is {{ today_date }}.
</relevant_information>

<task>
You are given a list of empty table templates and a list of document excerpts. The tables are originated from a Diligence Questionnaire (DDQ) spreadsheet.
Your task is to generate a complete table for each input table using the provided document excerpts. 
</task>

<guidelines>
- NEVER use any phrase like "Based on the information provided", "According to the information provided", "Based on the provided document excerpts", "Based on the provided documents", "Based on the financial statements provided", "Based on the financial statements", "Based on the information provided in the document excerpts", or any variation of it in your answer.
- NEVER refer to the document excerpts, available sources, financial statements, or document excerpts in your answer.
- YOU MUST answer as if you are an experienced investment professional, and you are answering the question based on your professional knowledge and experience using a confident tone.
- You are an employee of the firm, and you are answering the questions in the first person, as part of the firm. Do not refer to the firm as "the firm" or "the company".
- The table templates you will receive contain specific "id" properties per table cell, as represented by the HTML <td> tags. You MUST preserve those tags while removing the "sjs-" prefix.
</guidelines>


<fund_restrictions>
You are free to use all information from the document excerpts to generate the table.
Every table has a list of funds associated with it. 
If you find information in your document excerpts that is related to funds other than the ones associated with the table, you should ignore it.
If the list of funds is empty, you do not have any restrictions.
</fund_restrictions>

<answer_formatting>
You must answer your query **only using the retrieved content**, and format your answer based on the table provided. Here's your formatting guide:

Table list format is an array of objects, each containing the following fields:
- table_id: 'the table ID as provided in the input list'
- table_template: 'the table template, provided as HTML containing the table structure.'
- instructions: 'instructions for filling out this table.'
- custom_prompt: 'this is an OPTIONAL field, used by the user to help you generate the table. It is only provided if this table has already been generated previously, and the user is asking you to refine the table. It is not required, and you should not use it if it is not provided.'
- existing_answer: 'this is an OPTIONAL field, used to provide the existing table. It is only provided if this table has already been generated previously, and the user is asking you to refine the table. It is not required, and you should not use it if it is not provided.'
- funds: 'this is an OPTIONAL field, used to provide the list of funds associated with the table.'
</answer_formatting>

<existing_answer_and_custom_prompt>
If the existing_answer field is provided, you should use it as a starting point for your table, as it was previously generated by the LLM, and contains a table that is likely to be correct or very relevant to the table.
If both existing_answer and custom_prompt are provided, you should use the custom_prompt to refine your table, and the existing_answer as a starting point.
</existing_answer_and_custom_prompt>

<answer_rules>
Use **HTML** formatting wherever possible.  
Ensure that HTML tables include a proper table structure, with headers, rows, and cells. Use standard HTML table tags.

If the content includes **names or personnel**, be **verbose** and include **full details**, including name, email, phone number, title, and any other relevant information.

If a question indicates you should return an amount or a number, the answer should include a number, amount, or a quantitative piece of information that directly answers the question.
You are not allowed to modify the amount or the number in any way. Do not round it up or down. You should use the exact number provided in the sources to answer the question.

If a question is asking for a specific date (For example, "When do you anticipate the Fund completes its first investment?"), the answer must include a specific date that is determined to be accurate.
Do not provide an answer without a specific date if one is requested. If you cannot determine a specific date, say "N/A".

If a question instructs you to fill out an attached (or external) form, say "N/A".
If you are unable to answer a question because it refers to an external asset, such as an attachment, a document, or a file, say "Virgil is unable to access external assets or attachments.".

You must take into account the custom prompt when answering the question. If the custom prompt is provided, you should use it to refine your answer. Treat it as a user instruction.
If the customer prompt is an empty string, you should not use it.

You should use the table template when formatting the final table.
If the table template contains a table outline, you should try and format your table to match the table outline.
Do not copy the entire table template verbatim into the final table. It is designed to only be used as a guideline.
If the table template contains a reference to an external source, such as "Please see the attached document", "Download Source File", "Download Source Document", you should not include it in the final table.
If the table template contains instructions on how to answer the question using an external file, such as "Please complete the attribution table in the provided excel files attached in the upcoming question.", you should not include it in the final table.
</answer_rules>

<final_output_formatting>
Use HTML whenever possible.
You MUST preserve the original HTML tag "id" attribute in the generated table.

Your answer output should be a list of JSON objects, each containing the following fields:
- table_id: (this is the table_id of the table that you are answering. Do not use any other value in this field.)
- generated_table: (this is the generated table, that was generated by the LLM)
- reason: (this is a single paragraph explanation of the reason this specific table was generated. Must be concise, short, and logical. If the table contains numerical values, you should include a simple list to break down the values and where they came from in your reasoning.)

The output list should be in the same order as the input list, and must contain the same number of elements as the input list.
The output must be a valid JSON list. Do not include any other text or formatting in the output.

If you are unable to generate a table, return an empty table with the reason why you are unable to generate the table.

- NEVER state, imply or allude to the fact that you are using documents, financial statements, sources, or document excerpts to answer the question. 
- Do not start an answer with "Based on the ..." or any variation of it.
- Do not start an answer with "Based on the information provided" or any variation of it.
- Do not start an answer with "Based on the document excerpts" or any variation of it.
- Do not start an answer with "According to the document excerpts" or any variation of it.
- Do not start an answer with "According to the document" or any variation of it.
- Do not start an answer with "According to the information provided" or any variation of it.
- Do not start an answer with "According to the information" or any variation of it.
- Do not start an answer with "Based on the provided document excerpts" or any variation of it.
- Do not start an answer with "Based on the provided documents" or any variation of it.
- Do not start an answer with "Based on the financial statements provided" or any variation of it.
- Do not start an answer with "Based on the financial statements" or any variation of it.

- Do not use "Based on the provided information" or any variation of it in your answer.
- Do not use "According to the provided information" or any variation of it in your answer.
- Do not use "Based on the available information" or any variation of it in your answer.
- Do not use "Based on the information provided in the document excerpts" or any variation of it in your answer.

- NEVER use any phrase like "Based on the information provided", "According to the information provided", "Based on the provided document excerpts", "Based on the provided documents", "Based on the financial statements provided", "Based on the financial statements", "Based on the information provided in the document excerpts", or any variation of it in your answer.
- NEVER refer to the document excerpts, available sources, financial statements, or document excerpts in your answer.
- YOU MUST answer as if you are a professional analyst, and you are answering the question based on your professional knowledge and experience using a confident tone.

Before you return the output, make sure to validate that the output is a valid JSON list.
</final_output_formatting>

Here are the tables:
{{ input }}

Here are the document excerpts:
{{ excerpts }}
"""

DELIM = "__@__"

class DDQTableInput(BaseModel):
    """A single table input"""
    table_id: str = Field(description="The table ID as provided in the input list.")   
    table_template: str = Field(description="The table template, provided as HTML containing the table structure.")
    instructions: str = Field(description="Instructions for filling out this table.")
    custom_prompt: str = Field(description="The custom prompt as provided in the input list.", default="")
    existing_answer: str = Field(description="The existing answer as previously generated by the LLM.", default="")
    funds: str = Field(description="The funds as provided in the input list.", default="")

class SingleDDQTableCell(BaseModel):
    """A single cell in a table"""
    cell_address: str = Field(description="The cell address as provided in the input list, without the 'sjs-' prefix.")
    cell_value: str = Field(description="The cell value as generated by the LLM.")
    reason: str = Field(description="A single paragraph explanation of the reason this specific result was generated for this cell. Must be concise, short, and logical.")


class SingleDDQTableResponse(BaseModel):
    """A single response"""
    table_id: str = Field(description="The table ID as provided in the input list.")
    generated_table: str = Field(description="The generated table, provided as HTML containing the table structure.")
    cells: List[SingleDDQTableCell] = Field(description="The generated cells in the table.")
    reason: str = Field(description="A single paragraph explanation of the reason this specific table was generated using these values. Must be concise, short, and logical.")
    
    @validator('generated_table', pre=True)
    def ensure_string(cls, v):
        if v is None:
            return ""
        return str(v)

class DDQTableResponses(BaseModel):
    responses: List[SingleDDQTableResponse] = Field(description="A list of responses, one for each table.")

# @tracing.observe(name="generate_table_responses")
def generate_table_response(
        table_inputs: List[DDQTableInput],
        data_mode: DataMode,
        data: str,
        model_hyperparams: ModelHyperparameters = ModelHyperparameters(),
        institution_name: str = None,
        secret: dict = None,
        **kwargs
) :
    print('Generate table response...')
    
    # print(table_inputs)
    
    try:
        # Process data if needed
        if data_mode == DataMode.JUMBO_CHUNKS:
            # Need to remove the doc context.
            _content_records = [json.loads(d['pageContent'])['content'] for d in json.loads(data)]
            _content_records = [': '.join(c.split(DELIM)) for c in _content_records]
            _content_records = [c.replace('\n', ' ') for c in _content_records]
            data = '\n\n'.join(_content_records)

        persona = persona_prompt_string.replace("{institution_name}", institution_name)

        # Prepare the prompt using jinja2
        template = Template(tool_prompt)
        prompt_content = template.render(
            persona_prompt_string=persona,
            input=table_inputs,            
            excerpts=data,
            today_date=datetime.now().strftime("%Y-%m-%d")
        )

        # Use Pydantic model to generate the structured output schema
        structured_output_schema = DDQTableResponses.model_json_schema()

        # Prepare the request payload for Anthropic API
        payload = {
            "model": model_hyperparams.model_name if hasattr(model_hyperparams, 'model_name') else ChatModel.CLAUDE_40_LATEST,
            "max_tokens": model_hyperparams.max_tokens if hasattr(model_hyperparams, 'max_tokens') else 64000,
            "temperature": model_hyperparams.temperature if hasattr(model_hyperparams, 'temperature') else 0.0,
            "messages": [
                {
                    "role": "user",
                    "content": prompt_content
                }
            ],
            # "system": "You are a helpful assistant that generates structured JSON responses for DDQ questions.",
            
            "tools": [
                {
                    "name": "table_responses",
                    "description": "Generate structured responses for DDQ tables using the provided schema.",
                    "input_schema": structured_output_schema
                }
            ],
            "tool_choice": {"type": "tool", "name": "table_responses"}
        }

        # print(f"payload: {payload}")

        # Get API key from secret
        api_key =   get_env_var("ANTHROPIC_API_KEY", secret)
        if not api_key:
            raise ValueError("Anthropic API key not found in secret")

        # Make the API call
        headers = {
            "Content-Type": "application/json",
            "x-api-key": api_key,
            "anthropic-beta": "context-1m-2025-08-07",
            "max_tokens": "128000",
            "anthropic-version": "2023-06-01"
        }

        response = requests.post(
            "https://api.anthropic.com/v1/messages",
            headers=headers,
            json=payload,
            timeout=600  # 10 minute timeout
        )

        if response.status_code != 200:
            raise Exception(f"Anthropic API error: {response.status_code} - {response.text}")

        # Parse the response
        response_data = response.json()
        
        # Extract structured output from the response
        if 'content' not in response_data or len(response_data['content']) == 0:
            raise Exception("No content found in Anthropic API response")
            
        content = response_data['content'][0]
        print(response_data)

        if content.get('type') != 'tool_use':
            raise Exception(f"Expected tool_use content type, got: {content.get('type')}")
            
        # Extract structured output from tool_use
        structured_output = content['input']
        responses_data = structured_output.get('responses', [])
        
        # Convert to our expected format
        responses = []
        for item in responses_data:
            responses.append(SingleDDQTableResponse(
                table_id=item.get('table_id', ''),
                generated_table=item.get('generated_table', ''),
                cells=item.get('cells', ''),
                reason=item.get('reason', '')
            ))
        
        print(f"responses: {responses}")
        return responses

    except Exception as e:
        print(f"generate_table_response: Error generating table: {e}")
        logging.error(f"generate_table_response: Error generating table: {e}")
        raise e
