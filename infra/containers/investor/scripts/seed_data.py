"""
Seed data for Investor Infrastructure database
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

# Add investor module to path
investor_dir = app_dir / "investor"
sys.path.insert(0, str(investor_dir))

from database import get_prisma_client

async def seed_investors():
    """Seed the database with sample investor data"""
    client = await get_prisma_client()
    
    # Sample investors
    investors_data = [
        {
            "name": "Acme Private Equity",
            "type": "PRIVATE_EQUITY",
            "geography": "North America",
            "email": "<EMAIL>",
            "phone": "******-555-0123",
            "website": "https://acmepe.com",
            "description": "Leading middle-market private equity firm focusing on technology and healthcare sectors.",
            "aum": 250000000000,  # $2.5B in cents
            "vintage": 2010,
            "address": "123 Wall Street",
            "city": "New York",
            "state": "NY",
            "country": "United States",
            "zipCode": "10005"
        },
        {
            "name": "Global Venture Partners",
            "type": "VENTURE_CAPITAL",
            "geography": "Global",
            "email": "<EMAIL>",
            "phone": "******-555-0456",
            "website": "https://globalvc.com",
            "description": "Early-stage venture capital firm investing in innovative technology startups worldwide.",
            "aum": 50000000000,  # $500M in cents
            "vintage": 2015,
            "address": "456 Sand Hill Road",
            "city": "Menlo Park",
            "state": "CA",
            "country": "United States",
            "zipCode": "94025"
        },
        {
            "name": "European Family Office",
            "type": "FAMILY_OFFICE",
            "geography": "Europe",
            "email": "<EMAIL>",
            "phone": "+44-20-555-0789",
            "description": "Multi-family office serving ultra-high-net-worth individuals and families in Europe.",
            "aum": 100000000000,  # $1B in cents
            "vintage": 1995,
            "address": "789 Mayfair Street",
            "city": "London",
            "country": "United Kingdom",
            "zipCode": "W1K 1AA"
        },
        {
            "name": "Sovereign Wealth Fund Singapore",
            "type": "SOVEREIGN_WEALTH",
            "geography": "Asia Pacific",
            "email": "<EMAIL>",
            "description": "Singapore's sovereign wealth fund managing national reserves and strategic investments.",
            "aum": 50000000000000,  # $500B in cents
            "vintage": 1974,
            "city": "Singapore",
            "country": "Singapore"
        },
        {
            "name": "University Endowment Fund",
            "type": "ENDOWMENT",
            "geography": "North America",
            "email": "<EMAIL>",
            "phone": "******-555-0321",
            "description": "Large university endowment fund focused on long-term value creation and diversified investments.",
            "aum": 1500000000000,  # $15B in cents
            "vintage": 1962,
            "address": "100 University Avenue",
            "city": "Cambridge",
            "state": "MA",
            "country": "United States",
            "zipCode": "02138"
        }
    ]
    
    print("Creating sample investors...")
    
    for investor_data in investors_data:
        try:
            # Check if investor already exists
            existing = await client.investor.find_first(
                where={"name": investor_data["name"]}
            )
            
            if existing:
                print(f"  ⚠️  Investor '{investor_data['name']}' already exists, skipping...")
                continue
            
            # Create investor
            investor = await client.investor.create(data=investor_data)
            print(f"  ✅ Created investor: {investor.name}")
            
            # Add sample contacts for each investor
            contacts_data = [
                {
                    "firstName": "John",
                    "lastName": "Smith",
                    "title": "Managing Partner",
                    "email": f"john.smith@{investor_data['name'].lower().replace(' ', '')}.com",
                    "phone": "******-0001",
                    "isPrimary": True,
                    "investorId": investor.id
                },
                {
                    "firstName": "Sarah",
                    "lastName": "Johnson",
                    "title": "Investment Director",
                    "email": f"sarah.johnson@{investor_data['name'].lower().replace(' ', '')}.com",
                    "phone": "******-0002",
                    "isPrimary": False,
                    "investorId": investor.id
                }
            ]
            
            for contact_data in contacts_data:
                contact = await client.investorcontact.create(data=contact_data)
                print(f"    ➕ Added contact: {contact.firstName} {contact.lastName}")
            
            # Add sample investments
            if investor_data["type"] in ["PRIVATE_EQUITY", "VENTURE_CAPITAL"]:
                investments_data = [
                    {
                        "fundName": f"{investor.name} Fund I",
                        "vintage": 2018,
                        "commitmentAmount": 5000000000,  # $50M in cents
                        "calledAmount": 3000000000,      # $30M in cents
                        "currentValue": 4000000000,      # $40M in cents
                        "strategy": "Growth Capital",
                        "investorId": investor.id
                    },
                    {
                        "fundName": f"{investor.name} Fund II",
                        "vintage": 2021,
                        "commitmentAmount": 7500000000,  # $75M in cents
                        "calledAmount": 2500000000,      # $25M in cents
                        "currentValue": 2800000000,      # $28M in cents
                        "strategy": "Buyout",
                        "investorId": investor.id
                    }
                ]
                
                for investment_data in investments_data:
                    investment = await client.investment.create(data=investment_data)
                    print(f"    💰 Added investment: {investment.fundName}")
                    
        except Exception as e:
            print(f"  ❌ Error creating investor '{investor_data['name']}': {e}")
            continue
    
    print("\n🎉 Database seeding completed!")

async def main():
    """Main seed function"""
    print("🌱 Seeding Investor Infrastructure database...")
    await seed_investors()

if __name__ == "__main__":
    asyncio.run(main())
