#!/usr/bin/env python3
"""
Prisma migration script for Investor Infrastructure
Handles database migration and setup
"""

import asyncio
import os
import subprocess
import sys
from pathlib import Path

# Add the app directory to Python path
app_dir = Path(__file__).parent.parent / "app"
sys.path.insert(0, str(app_dir))

async def run_prisma_command(command: list[str], cwd: str = None):
    """Run a Prisma command and handle errors"""
    if cwd is None:
        cwd = Path(__file__).parent.parent  # Root of investor-infra
    
    # Add schema path to prisma commands
    if command[0] == "prisma" and "--schema" not in command:
        command.extend(["--schema", "./prisma/schema.prisma"])
    
    print(f"Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False

async def generate_prisma_client():
    """Generate Prisma client"""
    print("Generating Prisma client...")
    return await run_prisma_command(["prisma", "generate"])

async def create_migration(name: str = None):
    """Create a new migration"""
    command = ["prisma", "migrate", "dev"]
    if name:
        command.extend(["--name", name])
    
    print(f"Creating migration: {name or 'auto-generated'}")
    return await run_prisma_command(command)

async def deploy_migrations():
    """Deploy migrations to production database"""
    print("Deploying migrations...")
    return await run_prisma_command(["prisma", "migrate", "deploy"])

async def reset_database():
    """Reset database (development only)"""
    print("Resetting database...")
    return await run_prisma_command(["prisma", "migrate", "reset", "--force"])

async def seed_database():
    """Seed database with initial data"""
    print("Seeding database...")
    try:
        from seed_data import seed_investors
        await seed_investors()
        print("Database seeded successfully!")
        return True
    except ImportError:
        print("No seed data script found. Skipping...")
        return True
    except Exception as e:
        print(f"Error seeding database: {e}")
        return False

async def main():
    """Main migration function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Prisma migration utilities")
    parser.add_argument(
        "action",
        choices=["generate", "migrate", "deploy", "reset", "seed", "setup"],
        help="Action to perform"
    )
    parser.add_argument(
        "--name",
        help="Migration name (for migrate action)"
    )
    
    args = parser.parse_args()
    
    # Ensure DATABASE_URL is set
    if not os.getenv("DATABASE_URL"):
        print("ERROR: DATABASE_URL environment variable is required")
        sys.exit(1)
    
    if args.action == "generate":
        success = await generate_prisma_client()
    elif args.action == "migrate":
        success = await create_migration(args.name)
    elif args.action == "deploy":
        success = await deploy_migrations()
    elif args.action == "reset":
        success = await reset_database()
    elif args.action == "seed":
        success = await seed_database()
    elif args.action == "setup":
        # Full setup: generate client, run migrations, seed data
        print("Setting up database...")
        success = (
            await generate_prisma_client() and
            await create_migration("initial_investor_schema") and
            await seed_database()
        )
    else:
        print(f"Unknown action: {args.action}")
        success = False
    
    if success:
        print(f"✅ {args.action.title()} completed successfully!")
        sys.exit(0)
    else:
        print(f"❌ {args.action.title()} failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
