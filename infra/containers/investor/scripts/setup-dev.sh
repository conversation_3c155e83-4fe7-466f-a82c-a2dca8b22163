#!/bin/bash

# Setup development environment for Investor Infrastructure
set -e

echo "🔧 Setting up development environment..."

cd "$(dirname "$0")/.."

echo "📁 Working directory: $(pwd)"

# Check if virtual environment exists
if [[ ! -d "venv" ]]; then
    echo "🐍 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "⚡ Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"
export INVESTOR_DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"

# Install Prisma CLI if not available
if ! command -v prisma &> /dev/null; then
    echo "📦 Installing Prisma CLI..."
    npm install -g prisma
fi

echo "✅ Development environment setup complete!"
echo ""
echo "💡 To activate the environment manually:"
echo "   source venv/bin/activate"
echo ""
echo "💡 To run commands in this environment:"
echo "   source venv/bin/activate"
echo "   export DATABASE_URL='postgresql://virgin:virgin@localhost:5433/investor_db'"
echo "   python3 scripts/seed_data.py"
echo ""
