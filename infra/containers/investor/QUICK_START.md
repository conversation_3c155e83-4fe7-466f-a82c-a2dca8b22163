# 🚀 Quick Start Guide - Investor Infrastructure

## Prerequisites

- Docker and Docker Compose
- Node.js (for Prisma CLI)
- Python 3.8+ (for virtual environment)

## Start Service (One Command)

```bash
# From project root
./infra/containers/investor-infra/scripts/start-local.sh
```

This will:

- ✅ Start PostgreSQL database
- ✅ Start FastAPI service
- ✅ Create Python virtual environment
- ✅ Install Python dependencies
- ✅ Install Prisma CLI if needed
- ✅ Generate Prisma client
- ✅ Run database migrations
- ✅ Seed with sample data

## Verify Setup

```bash
# Run pre-flight check
./infra/containers/investor-infra/scripts/test-setup.sh

# Check service health
curl http://localhost:8081/health

# List sample investors
curl http://localhost:8081/investors
```

## Services

- **API Server**: http://localhost:8081
- **API Docs**: http://localhost:8081/docs
- **Database**: localhost:5433 (user: virgin, pass: virgin, db: investor_db)

## Stop Service

```bash
./infra/containers/investor-infra/scripts/stop-local.sh
```

## Troubleshooting

### Prisma Issues

If you see Prisma schema errors:

```bash
cd infra/containers/investor-infra
export DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"
prisma generate --schema=./prisma/schema.prisma
```

### Port Conflicts

If port 8081 or 5433 are in use:

- Edit `docker-compose.local.yaml` to change ports
- Update environment variables accordingly

### Database Connection

To connect to database directly:

```bash
docker-compose -f docker-compose.local.yaml exec postgres psql -U virgin -d investor_db
```

## MCP Integration

To use as MCP server:

```bash
cd infra/containers/investor-infra/app
export DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"
python3 investor/index.py
```

## Development

For active development with auto-reload:

```bash
# Start database only
docker-compose -f docker-compose.local.yaml up postgres -d

# Run FastAPI with reload
cd app
export DATABASE_URL="postgresql://virgin:virgin@localhost:5433/investor_db"
uvicorn investor.index:app --reload --host 0.0.0.0 --port 8081
```
