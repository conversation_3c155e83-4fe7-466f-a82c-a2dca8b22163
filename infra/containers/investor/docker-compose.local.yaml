services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: virgin
      POSTGRES_PASSWORD: virgin
      POSTGRES_DB: investor_db
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U virgin -d investor_db"]
      interval: 5s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8081:8080"
    environment:
      - UVICORN_RELOAD=true
      - DATABASE_URL=****************************************/investor_db
      - INVESTOR_DATABASE_URL=****************************************/investor_db
    volumes:
      - .:/code
    env_file:
      - ../../../.env
    depends_on:
      postgres:
        condition: service_healthy
    extra_hosts:
      - "host.docker.internal:host-gateway"
    tty: true

volumes:
  postgres_data:
