-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "InvestorType" AS ENUM ('PRIVATE_EQUITY', 'VENTURE_CAPITAL', 'HEDGE_FUND', 'FAMILY_OFFICE', 'SOVEREIGN_WEALTH', 'PENSION_FUND', 'INSURANCE', 'ENDOWMENT', 'FOUNDATION', 'BANK', 'CORPORATE', 'INDIVIDUAL', 'OTHER');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "InvestorStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PROSPECT', 'ARCHIVED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "DocumentType" AS ENUM ('DDQ', 'PRESENTATION', 'REPORT', 'CONTRACT', 'EMAIL', 'OTHER');

-- CreateTable
CREATE TABLE "investors" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "InvestorType" NOT NULL,
    "status" "InvestorStatus" NOT NULL DEFAULT 'ACTIVE',
    "geography" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "website" TEXT,
    "description" TEXT,
    "aum" BIGINT,
    "vintage" INTEGER,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "country" TEXT,
    "zipCode" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "investors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "investor_contacts" (
    "id" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "title" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "linkedin" TEXT,
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "investorId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "investor_contacts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "investments" (
    "id" TEXT NOT NULL,
    "fundName" TEXT NOT NULL,
    "vintage" INTEGER NOT NULL,
    "commitmentAmount" BIGINT NOT NULL,
    "calledAmount" BIGINT,
    "currentValue" BIGINT,
    "strategy" TEXT,
    "investorId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "investments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "investor_notes" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "isPrivate" BOOLEAN NOT NULL DEFAULT false,
    "investorId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "investor_notes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "investor_documents" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "DocumentType" NOT NULL,
    "url" TEXT,
    "filePath" TEXT,
    "size" INTEGER,
    "mimeType" TEXT,
    "investorId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "investor_documents_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "investors_name_key" ON "investors"("name");

-- CreateIndex
CREATE UNIQUE INDEX "investors_email_key" ON "investors"("email");

-- CreateIndex
CREATE UNIQUE INDEX "investors_phone_key" ON "investors"("phone");

-- CreateIndex
CREATE INDEX "investors_name_idx" ON "investors"("name");

-- CreateIndex
CREATE INDEX "investors_type_idx" ON "investors"("type");

-- CreateIndex
CREATE INDEX "investors_geography_idx" ON "investors"("geography");

-- CreateIndex
CREATE INDEX "investors_status_idx" ON "investors"("status");

-- CreateIndex
CREATE INDEX "investor_contacts_investorId_idx" ON "investor_contacts"("investorId");

-- CreateIndex
CREATE INDEX "investor_contacts_email_idx" ON "investor_contacts"("email");

-- CreateIndex
CREATE INDEX "investments_investorId_idx" ON "investments"("investorId");

-- CreateIndex
CREATE INDEX "investments_vintage_idx" ON "investments"("vintage");

-- CreateIndex
CREATE INDEX "investor_notes_investorId_idx" ON "investor_notes"("investorId");

-- CreateIndex
CREATE INDEX "investor_documents_investorId_idx" ON "investor_documents"("investorId");

-- CreateIndex
CREATE INDEX "investor_documents_type_idx" ON "investor_documents"("type");

-- AddForeignKey
ALTER TABLE "investor_contacts" ADD CONSTRAINT "investor_contacts_investorId_fkey" FOREIGN KEY ("investorId") REFERENCES "investors"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "investments" ADD CONSTRAINT "investments_investorId_fkey" FOREIGN KEY ("investorId") REFERENCES "investors"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "investor_notes" ADD CONSTRAINT "investor_notes_investorId_fkey" FOREIGN KEY ("investorId") REFERENCES "investors"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "investor_documents" ADD CONSTRAINT "investor_documents_investorId_fkey" FOREIGN KEY ("investorId") REFERENCES "investors"("id") ON DELETE CASCADE ON UPDATE CASCADE;
