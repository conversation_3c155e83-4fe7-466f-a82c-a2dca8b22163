// This is your Prisma schema file for Investor Infrastructure,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider        = "prisma-client-py"
    interface       = "asyncio"
    recursive_type_depth = 5
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

enum InvestorType {
    PRIVATE_EQUITY
    VENTURE_CAPITAL
    HEDGE_FUND
    FAMILY_OFFICE
    SOVEREIGN_WEALTH
    PENSION_FUND
    INSURANCE
    ENDOWMENT
    FOUNDATION
    BANK
    CORPORATE
    INDIVIDUAL
    OTHER
}

enum InvestorStatus {
    ACTIVE
    INACTIVE
    PROSPECT
    ARCHIVED
}

model Investor {
    id          String        @id @default(cuid())
    name        String        @unique
    type        InvestorType
    status      InvestorStatus @default(ACTIVE)
    geography   String?
    email       String?       @unique
    phone       String?       @unique
    website     String?
    description String?       @db.Text
    
    // Financial information
    aum         BigInt?       // Assets Under Management in cents
    vintage     Int?          // Year founded
    
    // Contact information
    address     String?
    city        String?
    state       String?
    country     String?
    zipCode     String?
    
    // Metadata
    createdAt   DateTime      @default(now())
    updatedAt   DateTime      @updatedAt
    
    // Relations
    contacts    InvestorContact[]
    investments Investment[]
    notes       InvestorNote[]
    documents   InvestorDocument[]
    
    @@map("investors")
    @@index([name])
    @@index([type])
    @@index([geography])
    @@index([status])
}

model InvestorContact {
    id          String   @id @default(cuid())
    firstName   String
    lastName    String
    title       String?
    email       String?
    phone       String?
    linkedin    String?
    isPrimary   Boolean  @default(false)
    
    // Relations
    investorId  String
    investor    Investor @relation(fields: [investorId], references: [id], onDelete: Cascade)
    
    // Metadata
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt
    
    @@map("investor_contacts")
    @@index([investorId])
    @@index([email])
}

model Investment {
    id              String   @id @default(cuid())
    fundName        String
    vintage         Int
    commitmentAmount BigInt   // in cents
    calledAmount    BigInt?  // in cents
    currentValue    BigInt?  // in cents
    strategy        String?
    
    // Relations
    investorId      String
    investor        Investor @relation(fields: [investorId], references: [id], onDelete: Cascade)
    
    // Metadata
    createdAt       DateTime @default(now())
    updatedAt       DateTime @updatedAt
    
    @@map("investments")
    @@index([investorId])
    @@index([vintage])
}

model InvestorNote {
    id         String   @id @default(cuid())
    title      String
    content    String   @db.Text
    isPrivate  Boolean  @default(false)
    
    // Relations
    investorId String
    investor   Investor @relation(fields: [investorId], references: [id], onDelete: Cascade)
    
    // Metadata
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt
    
    @@map("investor_notes")
    @@index([investorId])
}

enum DocumentType {
    DDQ
    PRESENTATION
    REPORT
    CONTRACT
    EMAIL
    OTHER
}

model InvestorDocument {
    id          String       @id @default(cuid())
    name        String
    type        DocumentType
    url         String?
    filePath    String?
    size        Int?
    mimeType    String?
    
    // Relations
    investorId  String
    investor    Investor     @relation(fields: [investorId], references: [id], onDelete: Cascade)
    
    // Metadata
    createdAt   DateTime     @default(now())
    updatedAt   DateTime     @updatedAt
    
    @@map("investor_documents")
    @@index([investorId])
    @@index([type])
}
