"""
Investor Infrastructure FastAPI + FastMCP Server
Provides MCP tools for interacting with PostgreSQL investor database using Prisma
"""

import os
import asyncio
from typing import Any, Iterable, Optional
from fastapi import FastAPI, HTTPException
from fastmcp import FastMCP
from .database import get_prisma_client, get_db_connection, close_prisma_client
from .models import Investor, InvestorSearchParams

# Initialize FastAPI app
app = FastAPI(title="Investor Infrastructure API", version="1.0.0")

# Initialize FastMCP server
mcp = FastMCP("investor-infra")

# FastAPI lifecycle events
@app.on_event("startup")
async def startup_event():
    """Initialize database connection on startup"""
    await get_prisma_client()

@app.on_event("shutdown")
async def shutdown_event():
    """Close database connection on shutdown"""
    await close_prisma_client()

# MCP Tools for Database Operations using Prisma
@mcp.tool()
async def search_investors(
    query: str | None = None, 
    geography: str | None = None, 
    investor_type: str | None = None,
    status: str | None = None,
    row_limit: int = 100
) -> list[dict]:
    """Search investors using Prisma with advanced filters."""
    client = await get_prisma_client()
    
    # Build where clause
    where_conditions = {}
    
    if query:
        where_conditions["OR"] = [
            {"name": {"contains": query, "mode": "insensitive"}},
            {"description": {"contains": query, "mode": "insensitive"}}
        ]
    
    if geography:
        where_conditions["geography"] = {"contains": geography, "mode": "insensitive"}
        
    if investor_type:
        where_conditions["type"] = investor_type
        
    if status:
        where_conditions["status"] = status
    
    investors = await client.investor.find_many(
        where=where_conditions,
        take=row_limit,
        order_by={"name": "asc"},
        include={
            "contacts": True,
            "investments": True
        }
    )
    
    return [investor.model_dump() for investor in investors]

@mcp.tool()
async def get_investor_by_phone(phone: str) -> dict | None:
    """Get investor details by phone number using Prisma."""
    client = await get_prisma_client()
    
    investor = await client.investor.find_first(
        where={"phone": phone},
        include={
            "contacts": True,
            "investments": True,
            "notes": True,
            "documents": True
        }
    )
    
    return investor.model_dump() if investor else None

@mcp.tool()
async def get_investor_by_email(email: str) -> dict | None:
    """Get investor details by email address using Prisma."""
    client = await get_prisma_client()
    
    investor = await client.investor.find_first(
        where={"email": email},
        include={
            "contacts": True,
            "investments": True,
            "notes": True,
            "documents": True
        }
    )
    
    return investor.model_dump() if investor else None

@mcp.tool()
async def get_investor_by_id(investor_id: str) -> dict | None:
    """Get full investor profile by ID using Prisma."""
    client = await get_prisma_client()
    
    investor = await client.investor.find_unique(
        where={"id": investor_id},
        include={
            "contacts": True,
            "investments": True,
            "notes": True,
            "documents": True
        }
    )
    
    return investor.model_dump() if investor else None

@mcp.tool()
async def list_investor_types() -> list[str]:
    """List all available investor types."""
    return [
        "PRIVATE_EQUITY",
        "VENTURE_CAPITAL", 
        "HEDGE_FUND",
        "FAMILY_OFFICE",
        "SOVEREIGN_WEALTH",
        "PENSION_FUND",
        "INSURANCE",
        "ENDOWMENT",
        "FOUNDATION",
        "BANK",
        "CORPORATE",
        "INDIVIDUAL",
        "OTHER"
    ]

@mcp.tool()
async def get_investor_contacts(investor_id: str) -> list[dict]:
    """Get all contacts for a specific investor."""
    client = await get_prisma_client()
    
    contacts = await client.investorcontact.find_many(
        where={"investorId": investor_id},
        order_by={"isPrimary": "desc"}  # Primary contacts first
    )
    
    return [contact.model_dump() for contact in contacts]

@mcp.tool()
async def get_investor_investments(investor_id: str) -> list[dict]:
    """Get all investments for a specific investor."""
    client = await get_prisma_client()
    
    investments = await client.investment.find_many(
        where={"investorId": investor_id},
        order_by={"vintage": "desc"}  # Most recent first
    )
    
    return [investment.model_dump() for investment in investments]

# Legacy SQL tools for backward compatibility
@mcp.tool()
def db_identity() -> dict:
    """Show current database identity, search_path, and version."""
    with get_db_connection() as conn, conn.cursor() as cur:
        cur.execute("SHOW search_path")
        search_path = cur.fetchone()["search_path"]
        cur.execute("SELECT current_database() db, current_user usr, inet_server_addr() host, inet_server_port() port")
        who = cur.fetchone()
        cur.execute("SELECT version() AS version")
        version = cur.fetchone()["version"]
        who["search_path"] = search_path
        who["version"] = version
        return who

@mcp.tool()
def list_schemas(include_system: bool = False) -> list[dict]:
    """List schemas (optionally hide system schemas)."""
    hide = "" if include_system else "AND nspname NOT LIKE 'pg_%' AND nspname <> 'information_schema'"
    sql = f"""
    SELECT nspname AS schema_name, pg_get_userbyid(nspowner) AS owner
    FROM pg_namespace
    WHERE true {hide}
    ORDER BY 1"""
    with get_db_connection() as conn, conn.cursor() as cur:
        cur.execute(sql)
        return cur.fetchall()

@mcp.tool()
def list_tables(db_schema: str, name_like: str | None = None, row_limit: int = 1000) -> list[dict]:
    """List tables/views inside a schema."""
    where = "table_schema = %s"
    params: list[Any] = [db_schema]
    if name_like:
        where += " AND table_name ILIKE %s"
        params.append(name_like)
    sql = f"""
    SELECT table_name, table_type
    FROM information_schema.tables
    WHERE {where}
    ORDER BY 1
    LIMIT %s"""
    params.append(row_limit)
    with get_db_connection() as conn, conn.cursor() as cur:
        cur.execute(sql, params)
        return cur.fetchall()

@mcp.tool()
def describe_table(db_schema: str, table_name: str) -> list[dict]:
    """Describe columns for a table."""
    sql = """
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns
    WHERE table_schema = %s AND table_name = %s
    ORDER BY ordinal_position
    """
    with get_db_connection() as conn, conn.cursor() as cur:
        cur.execute(sql, (db_schema, table_name))
        return cur.fetchall()

@mcp.tool()
def run_query(sql: str, parameters: list[Any] | None = None, row_limit: int = 200) -> list[dict]:
    """
    Execute a read-only SELECT. Arbitrary SQL is wrapped and limited safely.
    """
    allowed = ("select", "with", "explain", "show", "values")
    if not sql.strip().lower().startswith(allowed):
        raise ValueError("Only read-only statements are allowed.")
    
    with get_db_connection() as conn, conn.cursor() as cur:
        # enforce a timeout (15s default)
        cur.execute("SET LOCAL statement_timeout = %s", (int(os.getenv("STATEMENT_TIMEOUT_MS", "15000")),))
        # wrap to guarantee LIMIT is applied without breaking original SQL
        wrapped = f"SELECT * FROM ({sql}) AS q LIMIT %s"
        params: Iterable[Any] = (parameters or []) + [row_limit]
        cur.execute(wrapped, params)
        return cur.fetchall()

# MCP Resource for investor profiles
@mcp.resource("investor://{investor_id}")
async def investor_resource(investor_id: str) -> dict:
    """Get full investor profile as a resource using Prisma."""
    investor = await get_investor_by_id(investor_id)
    if not investor:
        raise ValueError(f"Investor with ID {investor_id} not found")
    return investor

# FastAPI endpoints
@app.get("/")
async def root():
    """Health check endpoint."""
    return {"message": "Investor Infrastructure API", "status": "healthy"}

@app.get("/health")
async def health():
    """Health check with database connectivity."""
    try:
        from .database import test_connection
        is_connected = await test_connection()
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "database": "connected" if is_connected else "disconnected"
        }
    except Exception as e:
        return {"status": "unhealthy", "database": "disconnected", "error": str(e)}

# FastAPI endpoints for Investor CRUD operations
@app.get("/investors", response_model=list[dict])
async def list_investors(
    query: Optional[str] = None,
    geography: Optional[str] = None,
    investor_type: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 100
):
    """List investors with optional filters."""
    client = await get_prisma_client()
    
    # Build filter conditions
    where_conditions = {}
    if query:
        where_conditions["OR"] = [
            {"name": {"contains": query, "mode": "insensitive"}},
            {"description": {"contains": query, "mode": "insensitive"}}
        ]
    if geography:
        where_conditions["geography"] = geography
    if investor_type:
        where_conditions["investor_type"] = investor_type
    if status:
        where_conditions["status"] = status
    
    investors = await client.investor.find_many(
        where=where_conditions,
        take=limit,
        include={
            "contacts": True,
            "investments": True
        }
    )
    
    return [investor.model_dump() for investor in investors]

@app.get("/investors/{investor_id}", response_model=dict)
async def get_investor(investor_id: str):
    """Get investor by ID."""
    client = await get_prisma_client()
    investor = await client.investor.find_unique(
        where={"id": investor_id},
        include={
            "contacts": True,
            "investments": True,
            "notes": True,
            "documents": True
        }
    )
    if not investor:
        raise HTTPException(status_code=404, detail="Investor not found")
    return investor.model_dump()

@app.get("/investors/{investor_id}/contacts", response_model=list[dict])
async def list_investor_contacts_endpoint(investor_id: str):
    """Get all contacts for an investor."""
    client = await get_prisma_client()
    contacts = await client.investorcontact.find_many(
        where={"investor_id": investor_id}
    )
    return [contact.model_dump() for contact in contacts]

@app.get("/investors/{investor_id}/investments", response_model=list[dict])
async def list_investor_investments_endpoint(investor_id: str):
    """Get all investments for an investor."""
    client = await get_prisma_client()
    investments = await client.investment.find_many(
        where={"investor_id": investor_id}
    )
    return [investment.model_dump() for investment in investments]

@app.get("/investor-types", response_model=list[str])
async def list_investor_types_endpoint():
    """List all available investor types."""
    return [
        "PRIVATE_EQUITY",
        "VENTURE_CAPITAL", 
        "FAMILY_OFFICE",
        "SOVEREIGN_WEALTH",
        "ENDOWMENT",
        "PENSION_FUND",
        "INSURANCE",
        "CORPORATE",
        "FUND_OF_FUNDS",
        "OTHER"
    ]

# Mount MCP server for stdio transport
if __name__ == "__main__":
    # stdio transport — suitable for Claude Desktop, Cursor, etc.
    mcp.run()
