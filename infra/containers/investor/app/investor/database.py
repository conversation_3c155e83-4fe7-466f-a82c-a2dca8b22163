"""
Database connection and utility functions using Prisma
"""

import os
from prisma import Prisma
from typing import Optional
import asyncio

# Global Prisma client instance
_prisma_client: Optional[Prisma] = None

async def get_prisma_client() -> Prisma:
    """Get or create Prisma client instance"""
    global _prisma_client
    if _prisma_client is None:
        _prisma_client = Prisma()
        await _prisma_client.connect()
    return _prisma_client

async def close_prisma_client():
    """Close Prisma client connection"""
    global _prisma_client
    if _prisma_client is not None:
        await _prisma_client.disconnect()
        _prisma_client = None

async def test_connection() -> bool:
    """Test database connectivity using Prisma"""
    try:
        client = await get_prisma_client()
        # Simple query to test connection
        await client.investor.count()
        return True
    except Exception:
        return False

# Legacy psycopg support for raw SQL queries
import psycopg
from psycopg.rows import dict_row
from contextlib import contextmanager

# Database connection configuration
PG_URL = os.getenv(
    "INVESTOR_DATABASE_URL",
    "postgresql://virgin:virgin@localhost:5432/investor_db",
)

@contextmanager
def get_db_connection():
    """Create database connection with autocommit and dict rows (for raw SQL)"""
    conn = None
    try:
        conn = psycopg.connect(PG_URL, autocommit=True, row_factory=dict_row)
        yield conn
    finally:
        if conn:
            conn.close()
