"""
Data models for Investor Infrastructure
"""

from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class InvestorBase(BaseModel):
    name: str
    investor_type: str
    geography: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    description: Optional[str] = None

class InvestorCreate(InvestorBase):
    pass

class InvestorUpdate(BaseModel):
    name: Optional[str] = None
    investor_type: Optional[str] = None
    geography: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    description: Optional[str] = None

class Investor(InvestorBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class InvestorSearchParams(BaseModel):
    query: Optional[str] = None
    geography: Optional[str] = None
    investor_type: Optional[str] = None
    row_limit: int = 100
