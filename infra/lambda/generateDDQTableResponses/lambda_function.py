import json
import logging
import boto3
# from pyrpc.utils.tracing import tracing
from pyrpc.data_mode import DataMode
from pyrpc.rag_response.generate_table_response import DDQTableInput, generate_table_response
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

secret = None

def lambda_handler(event, context):
    
    # print(f'\nEvent:\n{event}')
    print(f'\nContext:\n{context}')

    try:      
        global secret

        print(
            f'Retrieving secret',
        )

        try:
            # Validate secret_name
            if not event.get("secret"):
                raise ValueError("Secret must be provided.")

            if type(event.get("secret")) == str:
                secret = json.loads(GetSecretWrapper(boto3.client('secretsmanager')).get_secret(event.get("secret")))
            else:
                secret = event.get("secret")
            
        except Exception as e:
            print(f"Error retrieving secret: {e}")
            raise
    

        print(f'Retrieving table array')
        try:
            table_array = json.loads(event['table_array'])
        except Exception as e:
            print(f"Error getting table array: {e}")
            raise

        print(f'Retrieving context')
        try:
            payload_context = event["context"]
        except Exception as e:
            print(f"Error getting context: {e}")
            raise

        print(f'Retrieving institution name')
        try:
            institution_name = payload_context.get("institutionName", None)
        except Exception as e:
            print(f"Error getting institution name: {e}")
            raise

        print(f'Retrieving tracing context')
        try:
            tracing_context = event.get("tracingContext", {})
            trace_id = tracing_context.get("traceId", None)
            session_id = tracing_context.get("sessionId", None)
            parent_span_id = tracing_context.get("parentSpanId", None)
        except Exception as e:
            print(f"Error getting tracing context: {e}")
            raise

        print(f'Institution name: {institution_name}')
        print(f'Tracing context: {tracing_context}')
        print(f'Trace id: {trace_id}')
        print(f'Session id: {session_id}')
        print(f'Parent span id: {parent_span_id}')
     
        print(f'Retrieving data mode')
        # Get the url parameter selecting which data to use
        data_mode = DataMode(int(event.get("data_mode")))

        print(f'Data mode is {data_mode}')

        if data_mode is None:
            logging.error('Must send data selector')
            raise Exception("Must send data selector")

        logging.debug(f'\nUsing {DataMode(data_mode)} data:\n{data_mode}')

        # Append citations with the file name (which may contains "context" information, e.g. fund name)
        # for table in table_array:
        #     print(f'\nTable:\n{table}')

        print(f'Retrieving jumbo chunks')
        try:
            _data = event.get("jumbo_chunks", [])

            if data_mode == DataMode.CITATIONS:
                for citation in _data:
                    citation["quote"] = citation['file_name'] + ": " + citation["quote"]

        except Exception as e:
            print(f"Error getting jumbo chunks: {e}")
            raise

        print(f'Generating table payload')
        try:
            table_payload = [
                DDQTableInput(
                    table_id=table["table_id"],
                    table_template=table["table_template"],
                    instructions=table["instructions"],
                    custom_prompt=table["custom_prompt"],
                    existing_answer=table["existing_answer"],
                    funds=table["funds"],
                    # data = json.dumps(_data)
                )
                for table in table_array]

            model_params = event.get("modelParameters", {})
            model_hyperparams = ModelHyperparameters(**model_params)
        except Exception as e:
            print(f"Error generating table payload: {e}")
            raise

        # print(f"\nTable payload:\n{table_payload}")

        # Print table ids
        print(f"Generating table responses for table ids: {[table.table_id for table in table_payload]}")

        response = generate_table_response(
                table_inputs=table_payload,
                data_mode=data_mode,
                data=json.dumps(_data),
                model_hyperparams=model_hyperparams,
                institution_name=institution_name,
                tracing_context=tracing_context,
                session_id=session_id,
                trace_id=trace_id,
                parent_span_id=parent_span_id,
                secret=secret
                )

        print(f"\nTable responses:\n{response}")
        # Convert the response to a JSON string before returning
        return {
            "statusCode": 200,
            "body": [r.model_dump() for r in response]
        }
    except Exception as e:
        print(f"Error generating DDQ Table responses: {e}")

        return {
                "statusCode": 500,
                "body": "Unable to generate DDQ Table responses: " + str(e)
        }
      
    
 

class GetSecretWrapper:
    def __init__(self, secretsmanager_client):
        self.client = secretsmanager_client


    def get_secret(self, secret_name):
        """
        Retrieve individual secrets from AWS Secrets Manager using the get_secret_value API.

        :param secret_name: The name of the secret fetched.
        :type secret_name: str
        """
        try:
            get_secret_value_response = self.client.get_secret_value(SecretId=secret_name)
            logging.info("Secret retrieved successfully.")
            return get_secret_value_response["SecretString"]
        except self.client.exceptions.ResourceNotFoundException:
            msg = f"The requested secret {secret_name} was not found."
            logger.info(msg)
            return msg
        except Exception as e:
            logger.error(f"generateDDQResponses: GetSecretWrapper: {str(e)}.")
            raise