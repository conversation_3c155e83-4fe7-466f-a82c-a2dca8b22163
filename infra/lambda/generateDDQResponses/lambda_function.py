import json
import logging
import boto3
# from pyrpc.utils.tracing import tracing
from pyrpc.data_mode import DataMode
from pyrpc.rag_response.generate_ddq_response import QuestionInput
from pyrpc.rag_response import generate_ddq_response
from pyrpc.rag_response.model_hyperparameters import ModelHyperparameters

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

secret = None



def lambda_handler(event, context):
    
    # print(f'\nEvent:\n{event}')
    print(f'\nContext:\n{context}')

    try:
      

        global secret

        print(
            f'Retrieving secret',
        )

        try:
            # Validate secret_name
            if not event.get("secret"):
                raise ValueError("Secret must be provided.")

            if type(event.get("secret")) == str:
                secret = json.loads(GetSecretWrapper(boto3.client('secretsmanager')).get_secret(event.get("secret")))
            else:
                secret = event.get("secret")
            
        except Exception as e:
            print(f"Error retrieving secret: {e}")
            raise
    
        logging.debug(f'\nRequest:\n{event}')

        print(f'Retrieving question array')
        try:
            question_array = json.loads(event['question_array'])
            req_context = event["context"]
            institution_name = req_context["institutionName"]
            tracing_context = event["tracingContext"]
        except Exception as e:
            print(f"Error getting question array: {e}")
            raise

        print(f'Retrieving tracing context')
        try:
            trace_id = tracing_context.get("traceId", None)
            session_id = tracing_context.get("sessionId", None)
            parent_span_id = tracing_context.get("parentSpanId", None)
        except Exception as e:
            print(f"Error getting tracing context: {e}")
            raise

        print(f'Retrieving data mode')
        # Get the url parameter selecting which data to use
        data_mode = DataMode(int(event.get("data_mode")))

        logging.debug(f'Data mode is {data_mode}')

        if data_mode is None:
            logging.error('Must send data selector')
            raise Exception("Must send data selector")

        logging.debug(f'\nUsing {DataMode(data_mode)} data:\n{data_mode}')

        # Append citations with the file name (which may contains "context" information, e.g. fund name)
        for question in question_array:
            print(f'\nQuestion:\n{question}')

        print(f'Retrieving jumbo chunks')
        _data = json.loads(event["jumbo_chunks"])
        if data_mode == DataMode.CITATIONS:
            for citation in _data:
                citation["quote"] = citation['file_name'] + \
                    ": " + citation["quote"]

            # print(f'\nData:\n{_data}')

        try:
            print(f'Creating question payload')
            question_payload = [
                QuestionInput(
                    question_id=question["question_id"],
                    question_type=question["question_type"],
                    answer_template=question["answer_template"],
                    contextualized_question=question["contextualized_question"],
                    custom_prompt=question["custom_prompt"],
                    existing_answer=question["existing_answer"],
                    funds=question["funds"],
                    # data = json.dumps(_data)
                )
                for question in question_array]
        except Exception as e:
            print(f"Error creating question payload: {e}")
            raise

        try:
            print(f'Retrieving model parameters')
            model_params = event["modelParameters"]
            model_hyperparams = ModelHyperparameters(**model_params)
        except Exception as e:
            print(f"Error getting model parameters: {e}")
            raise

        logging.debug(f"\nQuestion payload:\n{question_payload}")

        print(f'Generating DDQ responses')
        # with tracing.continue_trace(trace_id=trace_id, session_id=session_id, parent_span_id=parent_span_id):
        response = generate_ddq_response.generate_ddq_responses(
            question_inputs=question_payload,
            data_mode=data_mode,
            data=json.dumps(_data),
            model_hyperparams=model_hyperparams,
            institution_name=institution_name,
            tracing_context=tracing_context,
            session_id=session_id,
            trace_id=trace_id,
            parent_span_id=parent_span_id,
            secret=secret,
            )

        print(f"\nResponse:\n{response}")

        # Validate that all question IDs in the response match the question IDs in the input
        print(f"generateDDQResponses: Validating response question IDs: {[r.questionId for r in response]}")
        print(f"generateDDQResponses: Input question IDs: {[q.question_id for q in question_payload]}")

        # Validate that the response question IDs are in the input question IDs
        for r in response:
            if r.questionId not in [q.question_id for q in question_payload]:
                print(f"generateDDQResponses: Response question ID {r.questionId} not found in input question IDs")
                raise Exception(f"generateDDQResponses: Response question ID {r.questionId} not found in input question IDs")

        # Convert the response to a JSON string before returning
        return {
            "statusCode": 200,
            "body": [r.model_dump() for r in response]
        }
    except Exception as e:
        print(f"Error generating DDQ responses: {e}")

        return {
                "statusCode": 500,
                "body": "Unable to generate DDQ responses: " + str(e)
        }
      
    
 

class GetSecretWrapper:
    def __init__(self, secretsmanager_client):
        self.client = secretsmanager_client


    def get_secret(self, secret_name):
        """
        Retrieve individual secrets from AWS Secrets Manager using the get_secret_value API.

        :param secret_name: The name of the secret fetched.
        :type secret_name: str
        """
        try:
            get_secret_value_response = self.client.get_secret_value(SecretId=secret_name)
            logging.info("Secret retrieved successfully.")
            return get_secret_value_response["SecretString"]
        except self.client.exceptions.ResourceNotFoundException:
            msg = f"The requested secret {secret_name} was not found."
            logger.info(msg)
            return msg
        except Exception as e:
            logger.error(f"generateDDQResponses: GetSecretWrapper: {str(e)}.")
            raise