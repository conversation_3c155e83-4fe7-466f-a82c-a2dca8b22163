-- CreateIndex
CREATE INDEX "DocumentResponses_responseId_idx" ON "DocumentResponses"("responseId");

-- CreateIndex
CREATE INDEX "DocumentResponses_documentId_idx" ON "DocumentResponses"("documentId");

-- CreateIndex
CREATE INDEX "Question_orgId_idx" ON "Question"("orgId");

-- CreateIndex
CREATE INDEX "QuestionContent_orgId_idx" ON "QuestionContent"("orgId");

-- CreateIndex
CREATE INDEX "QuestionContent_questionId_idx" ON "QuestionContent"("questionId");

-- CreateIndex
CREATE INDEX "Response_orgId_idx" ON "Response"("orgId");

-- Add vector indexes for optimal similarity search performance
-- This migration adds HNSW indexes on vector columns for fast approximate nearest neighbor search

-- Alter the vector column to specify dimensions (1024) for HNSW index compatibility
-- The original column was created as 'vector' without dimensions, but HNSW indexes require them
ALTER TABLE "QuestionContent" ALTER COLUMN vector TYPE vector(1024);

-- Create HNSW index on QuestionContent.vector for fast similarity search
-- HNSW is much faster than IVFFlat for similarity search operations
CREATE INDEX IF NOT EXISTS "QuestionContent_vector_hnsw_idx" 
ON "QuestionContent" USING hnsw (vector vector_cosine_ops);

-- Create composite index on QuestionContent for orgId + vector filtering
-- This optimizes queries that filter by orgId and then perform vector similarity search
CREATE INDEX IF NOT EXISTS "QuestionContent_orgId_vector_idx" 
ON "QuestionContent" ("orgId") WHERE vector IS NOT NULL;

-- Analyze tables to update statistics for better query planning
ANALYZE "QuestionContent";
ANALYZE "Question";
ANALYZE "Response";
ANALYZE "DocumentResponses";
