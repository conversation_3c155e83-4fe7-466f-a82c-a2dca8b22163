import { CategoryStatus } from "@prisma/client";
import fs from "fs";
import { stdin, stdout } from "node:process";
import path from "path";
import * as readline from "readline/promises";
import xlsx from "xlsx";
import { db as prisma } from "~/server/db";

const orgName = "Virgil AI Local";
const categoryFile = "diligence_checklist.xlsx";

async function processCategoryFile(filePath: string): Promise<
  {
    seed_id: string;
    parent_seed_id: string;
    name: string;
    description: string;
    stale_after_days: number;
  }[]
> {
  // Read the Excel file
  const workbook = xlsx.readFile(filePath);

  // eslint-disable-next-line @typescript-eslint/dot-notation
  const sheet = workbook.Sheets["v2"];

  // Convert sheet to JSON
  const jsonData = xlsx.utils.sheet_to_json(sheet!);

  // Ensure seed_id and parent_seed_id are always strings
  const categories = (jsonData as any[]).map((row) => ({
    ...row,
    seed_id: row.seed_id !== undefined && row.seed_id !== null ? String(row.seed_id) : "",
    parent_seed_id:
      row.parent_seed_id !== undefined && row.parent_seed_id !== null
        ? String(row.parent_seed_id)
        : "",
  }));
  return categories;
}

// Topological sort to ensure parents are processed before children
function topoSortCategories(records: any[]): any[] {
  const bySeedId = new Map<string, any>();
  const graph = new Map<string, Set<string>>();
  const indegree = new Map<string, number>();

  for (const rec of records) {
    bySeedId.set(rec.seed_id, rec);
    graph.set(rec.seed_id, new Set());
    indegree.set(rec.seed_id, 0);
  }
  for (const rec of records) {
    if (rec.parent_seed_id && bySeedId.has(rec.parent_seed_id)) {
      graph.get(rec.parent_seed_id)!.add(rec.seed_id);
      indegree.set(rec.seed_id, (indegree.get(rec.seed_id) || 0) + 1);
    }
  }
  // Kahn's algorithm
  const queue: string[] = [];
  for (const [seed_id, deg] of indegree.entries()) {
    if (deg === 0) queue.push(seed_id);
  }
  const sorted: any[] = [];
  while (queue.length > 0) {
    const sid = queue.shift()!;
    sorted.push(bySeedId.get(sid));
    for (const child of graph.get(sid)!) {
      indegree.set(child, indegree.get(child)! - 1);
      if (indegree.get(child) === 0) queue.push(child);
    }
  }
  if (sorted.length !== records.length) {
    throw new Error("Cycle detected in category parent-child relationships");
  }
  return sorted;
}

async function main() {
  const existingOrg = await prisma.org.findFirst({
    where: { name: orgName },
  });

  if (!existingOrg) {
    throw new Error("Org not found");
  }

  const xlsxPath = categoryFile;
  let resolvedXlsxPath;

  if (path.isAbsolute(xlsxPath)) {
    resolvedXlsxPath = xlsxPath;
  } else {
    const tryPaths = [
      path.join(process.cwd(), "data", xlsxPath),
      path.join(process.cwd(), xlsxPath),
    ];
    resolvedXlsxPath = tryPaths.find((p) => fs.existsSync(p));
    if (!resolvedXlsxPath) {
      console.error("XLSX file not found. Tried paths:");
      tryPaths.forEach((p) => console.error("  " + p));
      process.exit(1);
    }
  }

  const records = await processCategoryFile(resolvedXlsxPath);

  const seedHash = new Map<string, string>();
  // Topologically sort categories so parents always come before children
  const sortedRecords = topoSortCategories(records);

  for (const row of sortedRecords) {
    const { seed_id, parent_seed_id, name, description, stale_after_days } = row;
    try {
      const cat = await prisma.category.upsert({
        where: {
          orgId_name: {
            orgId: existingOrg.id,
            name,
          },
        },
        update: {
          description: description || null,
          staleAfterDays: stale_after_days,
          seedId: seed_id,
          parentSeedId: parent_seed_id || null,
          parentId: parent_seed_id ? seedHash.get(parent_seed_id) : null,
        },
        create: {
          name,
          description: description || null,
          staleAfterDays: stale_after_days,
          seedId: seed_id,
          parentSeedId: parent_seed_id || null,
          parentId: parent_seed_id ? seedHash.get(parent_seed_id) : null,
          orgId: existingOrg.id,
          status: CategoryStatus.ACTIVE,
        },
      });

      seedHash.set(seed_id, cat.id);
      console.log(`Upserted category ${name} (${cat.id})`);
    } catch (err) {
      if (err instanceof Error) {
        console.error(`Error upserting category ${name}:`, err.message);
      } else {
        console.error(`Error upserting category ${name}:`, err);
      }
    }
  }
  await prisma.$disconnect();
  console.log("Seeding done.");
}

async function verify() {
  const rl = readline.createInterface({ input: stdin, output: stdout });

  console.log("This will seed/update tags for", orgName);
  console.log("Database URL:", process.env.DATABASE_URL);

  const orgInDb = await prisma.org.findFirstOrThrow({
    where: {
      name: orgName,
    },
  });

  console.log("Org name in DB:", orgInDb?.name);

  const answer = await rl.question("Are you sure you want to continue? (y/n)");

  if (answer !== "y") {
    console.log("Exiting");
    process.exit(0);
  }

  rl.close();
}

verify()
  .then(() => {
    main()
      .then(() => {
        console.log("Seeded Categories");
      })
      .catch((error) => {
        console.error(error);
      });
  })
  .catch((error) => {
    console.error(error);
  });
