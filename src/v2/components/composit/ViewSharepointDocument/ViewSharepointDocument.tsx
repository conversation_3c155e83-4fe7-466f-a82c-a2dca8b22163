import { api } from "~/trpc/react";
import { Typography } from "~/v2/components/ui/Typography";

interface ViewSharepointDocumentProps {
  documentId: string;
  modal?: boolean;
}

export const ViewSharepointDocument = ({
  documentId,
  modal = true,
}: ViewSharepointDocumentProps) => {
  const { isLoading, data: previewUrl } = api.azure.getDocumentPreviewUrl.useQuery(
    {
      documentId,
    },
    {
      retry: false,
    },
  );

  return (
    <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${
      modal ? "w-4/5 h-[90%]" : "w-full h-full"
    } bg-background border border-border rounded-lg shadow-lg flex flex-col items-center justify-center p-0 m-0`}>
      {isLoading ? (
        <div className="flex flex-row justify-center items-center h-full gap-0">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          <Typography variant="h6" className="ml-2">
            Loading document preview...
          </Typography>
        </div>
      ) : !previewUrl ? (
        <div className="flex flex-col justify-center items-center h-full gap-0">
          <Typography variant="h5" className="mb-2">
            Unable to generate document preview.
          </Typography>
          <Typography variant="body" className="text-center">
            Previews are only available for Sharepoint documents.
          </Typography>
        </div>
      ) : (
        <iframe
          src={previewUrl}
          width="100%"
          height="100%"
          loading="lazy"
          className="border-none m-0 p-0"
        />
      )}
    </div>
  );
};
