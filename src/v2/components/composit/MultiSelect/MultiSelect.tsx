import { Check, ChevronsUpDown, X } from "lucide-react";
import * as React from "react";

import { Button } from "~/v2/components/ui/Button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "~/v2/components/ui/Command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/v2/components/ui/Popover";
import { cn } from "~/v2/lib/utils";
import { Badge } from "../../ui/Badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "../../ui/Tooltip";
import { overrideMuiTheme } from "../../../lib/mui-theme-overrides";

export type Option = {
  value: string;
  label: string;
  icon?: React.ReactNode;
};

// Context for MultiSelect
interface MultiSelectContextType {
  open: boolean;
  selected: string[];
  options: Option[];
  onSelect: (value: string) => void;
  onRemove: (value: string) => void;
  onClear: () => void;
  onClose: () => void;
  variant: "default" | "minimal";
}

const MultiSelectContext = React.createContext<MultiSelectContextType | null>(
  null,
);

function useMultiSelectContext() {
  const context = React.useContext(MultiSelectContext);
  if (!context) {
    throw new Error("MultiSelect components must be used within a MultiSelect");
  }
  return context;
}

// Main MultiSelect component
export interface MultiSelectProps {
  children: React.ReactNode;
  options: Option[];
  selected?: string[]; // Made optional since we'll maintain internal state
  onChange?: (selected: string[]) => void;
  onClose?: (selected: string[]) => void;
  open?: boolean;
  variant?: "default" | "minimal";
}

export function MultiSelect({
  children,
  options,
  selected: initialSelected = [],
  onChange,
  onClose,
  open: controlledOpen,
  variant = "default",
}: MultiSelectProps) {
  const [internalOpen, setInternalOpen] = React.useState(
    controlledOpen ?? false,
  );
  const [internalSelected, setInternalSelected] = React.useState<string[]>(initialSelected);

  // Update internal selected when initialSelected prop changes
  React.useEffect(() => {
    setInternalSelected(initialSelected);
  }, [JSON.stringify(initialSelected)]);

  // Call onChange when internal selected changes (but not when prop updates)
  React.useEffect(() => {
    if (onChange && JSON.stringify(internalSelected) !== JSON.stringify(initialSelected)) {
      onChange(internalSelected);
    }
  }, [internalSelected, onChange]);

  const handleOpenChange = React.useCallback((open: boolean) => {
    if (!open && internalOpen) {
      // Only call onClose when the dropdown is actually closing
      onClose?.(internalSelected);
    }
    setInternalOpen(open);
  }, [internalOpen, onClose, internalSelected]);

  const closeDropdown = React.useCallback(() => {
    setInternalOpen(false);
  }, []);

  const handleSelect = React.useCallback(
    (value: string) => {
      setInternalSelected(prev => 
        prev.includes(value)
          ? prev.filter((item) => item !== value)
          : [...prev, value]
      );
    },
    [],
  );

  const handleRemove = React.useCallback(
    (value: string) => {
      setInternalSelected(prev => prev.filter((item) => item !== value));
    },
    [],
  );

  const handleClear = React.useCallback(() => {
    setInternalSelected([]);
  }, []);

  const contextValue: MultiSelectContextType = {
    open: internalOpen,
    selected: internalSelected,
    options,
    onSelect: handleSelect,
    onRemove: handleRemove,
    onClear: handleClear,
    onClose: closeDropdown,
    variant,
  };

  return (
    <MultiSelectContext.Provider value={contextValue}>
      <Popover open={internalOpen} onOpenChange={handleOpenChange}>
        {children}
      </Popover>
    </MultiSelectContext.Provider>
  );
}

// MultiSelectValues component for displaying selected items as tags
interface MultiSelectValuesProps {
  placeholder?: string;
  className?: string;
}

export function MultiSelectValues({
  placeholder = "Select options...",
  className,
}: MultiSelectValuesProps) {
  const { selected, options, onRemove, variant } = useMultiSelectContext();

  // Get the selected options with their labels
  const selectedOptions = options.filter((option) =>
    selected.includes(option.value),
  );

  if (selected.length === 0) {
    return (
      <span className={cn("text-muted-foreground", className)}>
        {placeholder}
      </span>
    );
  }

  if (variant === "minimal") {
    return (
      <div className="flex items-center w-full h-full">
        <div className="leading-[16px] flex flex-row items-center gap-[4px] min-w-0 max-w-full overflow-hidden">
          <Badge
            variant="default"
            className="px-1 text-xs w-[80px] text-left justify-start items-center flex overflow-hidden whitespace-nowrap text-ellipsis rounded-sm"
          >
            <span className="block overflow-hidden text-ellipsis whitespace-nowrap w-full">
              {selectedOptions[0]?.label}
            </span>
          </Badge>
          {selectedOptions.length > 1 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="outline" className="text-xs px-1">
                  +{selectedOptions.length - 1}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                {selectedOptions
                  .slice(1)
                  .map((item) => item.label)
                  .join(", ")}
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(overrideMuiTheme, "flex flex-wrap gap-1 flex-1", className)}
    >
      {selectedOptions.map((option) => (
        <div
          key={option.value}
          className="inline-flex items-center gap-0 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-md max-w-full"
        >
          <span className="truncate">{option.label}</span>
          <Button
            asChild
            variant="ghost"
            size="icon"
            className="ml-1 mr-0 w-fit h-fit hover:bg-transparent hover:text-primary-foreground rounded-sm p-0"
          >
            <div
              className="w-fit h-fit"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onRemove(option.value);
              }}
            >
              <X className="m-0 size-3" />
            </div>
          </Button>
        </div>
      ))}
    </div>
  );
}

// MultiSelectTrigger component
interface MultiSelectTriggerProps {
  children?: React.ReactNode;
  className?: string;
  placeholder?: string;
}

export function MultiSelectTrigger({
  children,
  className,
  placeholder = "Select options...",
}: MultiSelectTriggerProps) {
  const { variant } = useMultiSelectContext();
  return (
    <PopoverTrigger asChild className={`${overrideMuiTheme}`}>
      <Button
        variant={variant === "minimal" ? "ghost" : "outline"}
        className={cn(
          "w-full justify-between h-fit",
          variant === "minimal" && "hover:bg-none",
          className,
        )}
      >
        {children || <MultiSelectValues placeholder={placeholder} />}
        {variant !== "minimal" && (
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        )}
      </Button>
    </PopoverTrigger>
  );
}

// MultiSelectContent component
interface MultiSelectContentProps {
  children: React.ReactNode;
  className?: string;
  align?: "start" | "center" | "end";
  sideOffset?: number;
}

export function MultiSelectContent({
  children,
  className,
  align = "end",
  sideOffset = 4,
}: MultiSelectContentProps) {
  return (
    <PopoverContent
      align={align}
      sideOffset={sideOffset}
      className={cn(overrideMuiTheme, "w-full p-0", className)}
    >
      {children}
    </PopoverContent>
  );
}

// MultiSelectCommand component (wrapper for Command)
interface MultiSelectCommandProps {
  children: React.ReactNode;
  className?: string;
  placeholder?: string;
}

export function MultiSelectCommand({
  children,
  className,
  placeholder = "Search options...",
}: MultiSelectCommandProps) {
  return (
    <Command className={className}>
      <CommandInput placeholder={placeholder} className="h-9" />
      {children}
    </Command>
  );
}

// MultiSelectCommandList component
interface MultiSelectCommandListProps {
  children: React.ReactNode;
  className?: string;
}

export function MultiSelectCommandList({
  children,
  className,
}: MultiSelectCommandListProps) {
  return <CommandList className={className}>{children}</CommandList>;
}

// MultiSelectCommandEmpty component
interface MultiSelectCommandEmptyProps {
  children?: React.ReactNode;
  className?: string;
}

export function MultiSelectCommandEmpty({
  children = "No options found.",
  className,
}: MultiSelectCommandEmptyProps) {
  return <CommandEmpty className={className}>{children}</CommandEmpty>;
}

// MultiSelectCommandGroup component
interface MultiSelectCommandGroupProps {
  children: React.ReactNode;
  className?: string;
  heading?: string;
}

export function MultiSelectCommandGroup({
  children,
  className,
  heading,
}: MultiSelectCommandGroupProps) {
  return (
    <CommandGroup className={className} heading={heading}>
      {children}
    </CommandGroup>
  );
}

// MultiSelectCommandItem component
interface MultiSelectCommandItemProps {
  value: string;
  keywords?: string[];
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export function MultiSelectCommandItem({
  value,
  keywords = [],
  children,
  className,
  disabled,
}: MultiSelectCommandItemProps) {
  const { selected, onSelect } = useMultiSelectContext();
  const commandKeywords = React.useMemo(() => {
    if (typeof children !== "string") {
      return keywords;
    }
    return children
      .split(" ")
      .map((word) => word.toLowerCase())
      .concat(keywords);
  }, [keywords, children]);

  return (
    <CommandItem
      value={value}
      onSelect={() => onSelect(value)}
      className={className}
      disabled={disabled}
      keywords={commandKeywords}
    >
      <div className="flex items-center w-full">
        <div className="w-4 h-4 mr-2 flex-shrink-0">
          {selected.includes(value) && <Check className="h-4 w-4" />}
        </div>
        <div className="flex-1 min-w-0">{children}</div>
        <div className="w-14 flex-shrink-0" />
      </div>
    </CommandItem>
  );
}

// MultiSelectClear component
interface MultiSelectClearProps {
  children?: React.ReactNode;
  className?: string;
}

export function MultiSelectClear({
  children = "Clear",
  className,
}: MultiSelectClearProps) {
  const { onClear } = useMultiSelectContext();

  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn("w-full border-t rounded-none", className)}
      onClick={() => {
        onClear();
      }}
    >
      {children}
    </Button>
  );
}

// MultiSelectSeparator component
interface MultiSelectSeparatorProps {
  className?: string;
}

export function MultiSelectSeparator({ className }: MultiSelectSeparatorProps) {
  return <div className={cn("h-px bg-border my-1", className)} />;
}

// MultiSelectList component for wrapping MultiSelectItem components
interface MultiSelectListProps {
  children: React.ReactNode;
  className?: string;
}

export function MultiSelectList({ children, className }: MultiSelectListProps) {
  return <div className={cn("p-1", className)}>{children}</div>;
}

// MultiSelectItem component for plain dropdown items (no Command system)
interface MultiSelectItemProps {
  value: string;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export function MultiSelectItem({
  value,
  children,
  className,
  disabled = false,
}: MultiSelectItemProps) {
  const { selected, onSelect } = useMultiSelectContext();

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      onSelect(value);
    }
  };

  return (
    <div
      className={cn(
        "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
        selected.includes(value) && "bg-accent text-accent-foreground",
        disabled && "pointer-events-none opacity-50",
        className,
      )}
      onClick={handleClick}
      data-disabled={disabled}
    >
      <div className="flex items-center w-full">
        <div className="w-4 h-4 mr-2 flex-shrink-0">
          {selected.includes(value) && <Check className="h-4 w-4" />}
        </div>
        <div className="flex-1 min-w-0">{children}</div>
        <div className="w-14 flex-shrink-0" />
      </div>
    </div>
  );
}
