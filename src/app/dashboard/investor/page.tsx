"use client";

import { api } from "~/trpc/react";
import { InvestorView } from "~/views/investor/view";
import Loading from "../loading";

// ----------------------------------------------------------------------

export default function Page() {
  const { data: org, isLoading } = api.organization.get.useQuery({});

  if (isLoading) {
    return <Loading />;
  }

  if (!org) {
    return <Loading />;
  }

  return <InvestorView org={org} />;
}
