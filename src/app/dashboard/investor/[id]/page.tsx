"use client";

import { useParams } from "next/navigation";
import { api } from "~/trpc/react";
import { InvestorDetailView } from "~/views/investor/view";
import Loading from "../../loading";

// ----------------------------------------------------------------------

export default function InvestorDetailPage() {
  const params = useParams();
  const investorId = params.id as string;

  const { data: org, isLoading } = api.organization.get.useQuery({});

  if (isLoading) {
    return <Loading />;
  }

  if (!org) {
    return <Loading />;
  }

  return <InvestorDetailView org={org} investorId={investorId} />;
}
