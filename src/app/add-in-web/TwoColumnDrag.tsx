
import React, { useState, useRef, useCallback } from "react";
import { Box } from "@mui/material";

interface TwoColumnDragProps {
    leftChildren: React.ReactNode;
    rightChildren: React.ReactNode;
    rightColumnWidth?: number;
    onRightColumnWidthChange?: (width: number) => void;
}

export const TwoColumnDrag = ({
    leftChildren,
    rightChildren,
    rightColumnWidth: controlledWidth,
    onRightColumnWidthChange,
}: TwoColumnDragProps) => {
    const [internalWidth, setInternalWidth] = useState(500);
    const rightColumnWidth = controlledWidth ?? internalWidth;
    const [isDragging, setIsDragging] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);

    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        setIsDragging(true);
        e.preventDefault();
    }, []);

    const handleMouseMove = useCallback((e: MouseEvent) => {
        if (!isDragging || !containerRef.current) return;

        const containerRect = containerRef.current.getBoundingClientRect();
        const newWidth = containerRect.right - e.clientX;

        // Set minimum and maximum width constraints
        const minWidth = 200;
        const maxWidth = containerRect.width - 200;

        if (newWidth >= minWidth && newWidth <= maxWidth) {
            if (onRightColumnWidthChange) {
                onRightColumnWidthChange(newWidth);
            } else {
                setInternalWidth(newWidth);
            }
        }
    }, [isDragging]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
    }, []);

    // Add and remove event listeners
    React.useEffect(() => {
        if (isDragging) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            return () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            };
        }
    }, [isDragging, handleMouseMove, handleMouseUp]);

    return (
        <Box
            ref={containerRef}
            sx={{
                display: 'flex',
                height: '100vh',
                width: '100%',
                position: 'relative',
                overflow: 'hidden',
            }}
        >
            {/* Left Column */}
            <Box
                sx={{
                    flex: 1,
                    height: '100%',
                    overflow: 'auto',
                }}
            >
                {leftChildren}
            </Box>

            {/* Resizable Divider */}
            <Box
                sx={{
                    width: 8,
                    height: '100%',
                    backgroundColor: isDragging ? '#1976d2' : '#e0e0e0',
                    cursor: 'col-resize',
                    position: 'relative',
                    '&:hover': {
                        backgroundColor: '#1976d2',
                    },
                }}
                onMouseDown={handleMouseDown}
            >
                <Box
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        width: 2,
                        height: 20,
                        backgroundColor: isDragging ? '#fff' : '#999',
                        borderRadius: 1,
                    }}
                />
            </Box>

            {/* Right Column */}
            <Box
                sx={{
                    width: rightColumnWidth,
                    minWidth: 200,
                    maxWidth: '50%',
                    height: '100%',
                    borderLeft: '1px solid #e0e0e0',
                    overflow: 'auto',
                }}
            >
                {rightChildren}
            </Box>
        </Box>
    );
};
