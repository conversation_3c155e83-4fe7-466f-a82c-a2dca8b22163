"use client";

import React from "react";
import AddInRootWord from "../../components/virgil/AddInRootWord";
import MicrosoftOfficeProvider from "~/components/virgil/OfficeAddIn/MicrosoftOfficeProvider";
import FullHeightContainer from "~/components/virgil/FullHeightContainer";
import MSOfficeAddInContainer from "../../components/virgil/OfficeAddIn/OfficeAddInContainer";
import { useDocumentInfoById } from "./useDocumentInfoById";
import { selectHostType } from "~/lib/features/addInSelectors";
import { useSelector } from "react-redux";
import { EXCEL_HOST_TYPE, WORD_HOST_TYPE } from "~/lib/features/addInSlice";
import AddInRootExcel from "~/components/virgil/AddInRootExcel";

export default function Page() {
  // this will load document by id instead of by url or filename
  useDocumentInfoById();
  const hostType = useSelector(selectHostType);

  switch (hostType) {
    case WORD_HOST_TYPE:
      return <AddInRootWord />;
    case EXCEL_HOST_TYPE:
      return <AddInRootExcel />;
    default:
      return null;
  }
}
