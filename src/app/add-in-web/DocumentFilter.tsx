import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON> } from "~/v2/components"
import { Autocomplete, TextField, Select, MenuItem, FormControl, InputLabel } from "@mui/material";
import { api } from "~/trpc/react";
import { useDispatch, useSelector } from "react-redux";
import { EXCEL_HOST_TYPE, setDocumentId, setHostType, WORD_HOST_TYPE } from "~/lib/features/addInSlice";
import { DocumentStatus, DocumentType } from "@prisma/client";
import { useOrg } from "../providers/OrgProvider";
import { useEffect, useState } from "react";
import { selectHostType } from "~/lib/features/addInSelectors";

export const DocumentFilter = () => {
    const dispatch = useDispatch();
    const { orgId } = useOrg();
    const [selectedStatuses, setSelectedStatuses] = useState<DocumentStatus[]>([
        DocumentStatus.READY,
    ]);
    const [selectedDocumentId, setSelectedDocumentId] = useState<string>(localStorage.getItem('selectedDocumentId') ?? "");
    const hostType = useSelector(selectHostType);

    const { data: documents, isLoading } = api.document.getAll.useQuery(
        {
            status: selectedStatuses,
            type: hostType === EXCEL_HOST_TYPE ? [DocumentType.XLSX, DocumentType.XLS] : undefined,
        },
        {
            enabled: !!orgId,
        },
    );

    const options = documents?.map((document) => ({
        value: document.id,
        label: document.name,
        id: document.id,
    })) || [];

    const handleDocumentChange = (event: any, newValue: any) => {
        if (newValue) {
            setSelectedDocumentId(newValue.id);
            localStorage.setItem('selectedDocumentId', newValue.id);
        } else {
            setSelectedDocumentId("");
            localStorage.removeItem('selectedDocumentId');
        }
    };

    const handleHostTypeChange = (value: string) => {
        dispatch(setHostType(value as "word" | "excel"));
        localStorage.setItem('hostType', value);
    };

    const handleStatusChange = (event: any) => {
        const value = event.target.value;
        setSelectedStatuses(typeof value === 'string' ? [value as DocumentStatus] : value as DocumentStatus[]);
    };

    const statusOptions = [
        { value: DocumentStatus.PENDING, label: 'Pending' },
        { value: DocumentStatus.PROCESSING, label: 'Processing' },
        { value: DocumentStatus.TAGGING, label: 'Tagging' },
        { value: DocumentStatus.GENERATING_ANSWERS, label: 'Generating Answers' },
        { value: DocumentStatus.READY, label: 'Ready' },
        { value: DocumentStatus.ERROR, label: 'Error' },
        { value: DocumentStatus.EXTERNAL, label: 'External' },
    ];

    useEffect(() => {
        dispatch(setDocumentId(selectedDocumentId));
    }, [selectedDocumentId]);

    return (
        <div style={{ padding: '10px' }}>
            <Typography variant="h5">
                Add in type
            </Typography>
            <Tabs
                value={hostType as "word" | "excel"}
                onValueChange={handleHostTypeChange}
                aria-label="add in type tabs"
            >
                <TabsList>
                    <TabsTrigger value={WORD_HOST_TYPE}>
                        Word
                    </TabsTrigger>
                    <TabsTrigger value={EXCEL_HOST_TYPE}>
                        Excel
                    </TabsTrigger>
                </TabsList>
            </Tabs>
            <Typography variant="h5">
                SelectDocument
            </Typography>

            {/* Status Filter */}
            <FormControl fullWidth size="small" sx={{ mt: 2, mb: 2 }}>
                <InputLabel>Filter by Status</InputLabel>
                <Select
                    multiple
                    value={selectedStatuses}
                    onChange={handleStatusChange}
                    label="Filter by Status"
                    sx={{ backgroundColor: "white" }}
                >
                    {statusOptions.map((status) => (
                        <MenuItem key={status.value} value={status.value}>
                            {status.label}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            {/* Document Selection */}
            <Autocomplete
                value={options.find((option) => option.id === selectedDocumentId) ?? null}
                options={options}
                getOptionLabel={(option) => option.label}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                onChange={handleDocumentChange}
                loading={isLoading}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        placeholder="Search documents..."
                        size="small"
                        sx={{ width: "100%", backgroundColor: "white" }}
                    />
                )}
                sx={{ mt: 2 }}
            />
        </div>
    )
}