"use client";

import React, { useEffect } from "react";
import { Provider, useDispatch } from "react-redux";
import store from "../../lib/store";
import { TwoColumnDrag } from "./TwoColumnDrag";
import { DocumentFilter } from "./DocumentFilter";
import { OrgProvider } from "../providers/OrgProvider";
import { setMode as setAddInMode, setHostType, WORD_HOST_TYPE } from "../../lib/features/addInSlice";
import FullHeightContainer from "~/components/virgil/FullHeightContainer";
import MicrosoftOfficeProvider from "~/components/virgil/OfficeAddIn/MicrosoftOfficeProvider";
import MSOfficeAddInContainer from "~/components/virgil/OfficeAddIn/OfficeAddInContainer";

// Component to set the mode to web
function WebModeSetter() {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setAddInMode("web"));
    dispatch(setHostType(localStorage.getItem('hostType') as "word" | "excel" | null || WORD_HOST_TYPE));
  }, [dispatch]);

  return null;
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <WebModeSetter />
      <OrgProvider>
        <TwoColumnDrag
          leftChildren={
            <DocumentFilter />
          }
          rightChildren={
            <MSOfficeAddInContainer>
              <MicrosoftOfficeProvider mode="web">
                {children}
              </MicrosoftOfficeProvider>
            </MSOfficeAddInContainer>
          } />
      </OrgProvider>
    </Provider>
  );
}
