"use client";

import React, { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import TablePreview from "~/components/virgil/OfficeAddIn/Excel/TablePreview/TablePreview";
import { useDispatch } from "react-redux";
import { setSelectedTableId } from "~/lib/features/documentDDQSlice";
import { setActiveSheet, setDocumentId } from "~/lib/features/addInSlice";

const SetStoreData = ({ documentId, sheetName, tableId }: { documentId: string, sheetName: string, tableId: string }) => {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setSelectedTableId(tableId));
    dispatch(setActiveSheet(sheetName));
    dispatch(setDocumentId(documentId));
  }, [dispatch, tableId, sheetName, documentId]);

  return null;
}

export default function Page() {
  // Get table id from URL
  const tableId = useSearchParams().get("tableId");
  const documentId = useSearchParams().get("documentId");
  const sheetName = useSearchParams().get("sheetName");

  if (!tableId) {
    return <div>No table ID provided</div>;
  }
  if (!documentId) {
    return <div>No document ID provided</div>;
  }
  if (!sheetName) {
    return <div>No sheet name provided</div>;
  }
  return <>
    <SetStoreData documentId={documentId} sheetName={sheetName} tableId={tableId} />
    <TablePreview />
  </>
}
