"use client";

import React from "react";
import { ChatView } from "../../../views/chat/view/chat-view";
import { getLocalStorageTagsIds } from "../../../components/virgil/OfficeAddIn/localStorageClient";
import { getLocalStorageCategoryIds } from "../../../components/virgil/OfficeAddIn/localStorageClient";
import { getLocalStorageFundIds } from "../../../components/virgil/OfficeAddIn/localStorageClient";

export default function Page() {
  let initialTagIds: string[] = [];
  let initialFundIds: string[] = [];
  let initialCategoryIds: string[] = [];
  try {
    initialTagIds = getLocalStorageTagsIds();
    initialFundIds = getLocalStorageFundIds();
    initialCategoryIds = getLocalStorageCategoryIds();
  } catch (error) {
    console.error(error);
  }
  return (
    <ChatView
      initialTagIds={initialTagIds}
      initialFundIds={initialFundIds}
      initialCategoryIds={initialCategoryIds}
    />
  );
}
