"use client";

import React from "react";
import { ViewSharepointDocument } from "~/v2/components/composit/ViewSharepointDocument";
import { useSearchParams } from "next/navigation";

export default function Page() {
  // Get documentId from URL
  const documentId = useSearchParams().get("documentId");

  if (!documentId) {
    return <div>No document ID provided</div>;
  }

  return <ViewSharepointDocument documentId={documentId} modal={false} />;
}
