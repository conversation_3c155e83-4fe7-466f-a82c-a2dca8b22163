"use client";

import React from "react";
import { Provider } from "react-redux";
import store from "../../lib/store";
import MicrosoftOfficeProvider from "~/components/virgil/OfficeAddIn/MicrosoftOfficeProvider";


export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <MicrosoftOfficeProvider mode="dialog">{children}</MicrosoftOfficeProvider>
    </Provider>
  );
}
