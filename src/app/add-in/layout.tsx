"use client";

import React from "react";
import { Provider } from "react-redux";
import store from "../../../src/lib/store";
import MicrosoftOfficeProvider from "../../components/virgil/OfficeAddIn/MicrosoftOfficeProvider";
import MSOfficeAddInContainer from "../../components/virgil/OfficeAddIn/OfficeAddInContainer";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <MSOfficeAddInContainer>
        <MicrosoftOfficeProvider mode="addIn">{children}</MicrosoftOfficeProvider>
      </MSOfficeAddInContainer>
    </Provider>
  );
}
