import { PrismaClientType } from "~/server/db";
import { Category, CategoryStatus, Prisma } from "@prisma/client";

export class CategoryManager {
  static async createCategory({
    db,
    data,
  }: {
    db: PrismaClientType;
    data: Omit<Category, "id" | "createdAt" | "updatedAt">;
  }): Promise<Category> {
    data.status ??= CategoryStatus.ACTIVE;
    return db.category.create({
      data,
    });
  }

  static async getCategoryById({
    db,
    id,
    orgId,
  }: {
    db: PrismaClientType;
    id: string;
    orgId: string;
  }): Promise<Category | null> {
    return db.category.findFirst({
      where: { id, orgId },
    });
  }

  static async getAllCategories({
    db,
    orgId,
    status,
  }: {
    db: PrismaClientType;
    orgId: string;
    status?: CategoryStatus;
  }): Promise<Category[]> {

    const where: Prisma.CategoryWhereInput = { orgId };
    if (status) {
      where.status = status;
    }
    return db.category.findMany({
      where,
      orderBy: { name: "asc" },
    });
  }

  static async updateCategory({
    db,
    id,
    orgId,
    data,
  }: {
    db: PrismaClientType;
    id: string;
    orgId: string;
    data: Partial<Omit<Category, "id" | "createdAt" | "updatedAt">>;
  }): Promise<Category> {
    return db.category.update({
      where: { id },
      data: { ...data, orgId },
    });
  }

  static async deleteCategory({
    db,
    id,
    orgId,
  }: {
    db: PrismaClientType;
    id: string;
    orgId: string;
  }): Promise<void> {
    await db.category.delete({
      where: { id },
    });
  }

  static async getByDocumentId({
    db,
    documentId,
    orgId,
  }: {
    db: PrismaClientType;
    documentId: string;
    orgId: string;
  }): Promise<Category[]> {
    return db.category.findMany({
      where: {
        orgId,
        documents: {
          some: { id: documentId },
        },
      },
      orderBy: { name: "asc" },
    });
  }

  // Returns a tree of categories with children and all documents for each category and its descendants
  static async getCategoryTree({
    db,
    orgId,
    filterMode,
  }: {
    db: PrismaClientType;
    orgId: string;
    filterMode?: "MISSING_ONLY" | "ALL" | "ACTIVE_ONLY";
  }): Promise<Array<any>> {
    // Get all categories with children and documents
    let childWhere: any = {};

    if (filterMode === "MISSING_ONLY") {
      // only categories with no documents
      childWhere = {
        // documents: { none: {} }, Filter in frontend
        status: {
          not: {
            in: [CategoryStatus.INACTIVE],
          },
        },
      };
    } else if (filterMode === "ACTIVE_ONLY") {
      childWhere = {
        status: {
          not: {
            in: [CategoryStatus.INACTIVE],
          },
        },
      };
    }

    const categories = await db.category.findMany({
      where: {
        orgId,
        parentId: null,
      },
      include: {
        children: {
          include: {
            _count: { select: { documents: true } },
            children: true, // one level deeper for now
          },
          where: childWhere,
        },
        documents: true,
      },
      orderBy: { name: "asc" },
    });

    // Attach allDocuments to each parent category
    return categories;
  }

  // Export categories to Excel format
  static async exportCategoriesToExcel({
    db,
    orgId,
  }: {
    db: PrismaClientType;
    orgId: string;
  }): Promise<
    Array<{
      seed_id: string;
      parent_seed_id: string;
      name: string;
      description: string;
      stale_after_days: number;
      status: string;
      allocated: string;
    }>
  > {
    // Get category tree using existing method
    const categoryTree = await this.getCategoryTree({
      db,
      orgId,
    });

    // Helper function to flatten the tree structure in order: parent -> children
    const flattenTree = (
      categories: any[],
    ): Array<{
      seed_id: string;
      parent_seed_id: string;
      name: string;
      description: string;
      stale_after_days: number;
      status: string;
      allocated: string;
    }> => {
      const result: Array<{
        seed_id: string;
        parent_seed_id: string;
        name: string;
        description: string;
        stale_after_days: number;
        status: string;
        allocated: string;
      }> = [];

      for (const parent of categories) {
        // Add parent category first
        const parentSeedId = parent.seedId || parent.id;
        result.push({
          seed_id: parentSeedId,
          parent_seed_id: "", // Parent has no parent
          name: parent.name,
          description: parent.description || "",
          stale_after_days: parent.staleAfterDays || 3650,
          status: parent.status,
          allocated: "",
        });

        // Then add all children of this parent
        if (parent.children?.length) {
          for (const child of parent.children) {
            const childSeedId = child.seedId || child.id;
            const childStatus = child._count.documents > 0 ? "CLASSIFIED" : "";
            result.push({
              seed_id: childSeedId,
              parent_seed_id: parentSeedId,
              name: child.name,
              description: child.description || "",
              stale_after_days: child.staleAfterDays || 3650,
              status:
                child.status !== CategoryStatus.INACTIVE
                  ? "ACTIVE"
                  : "INACTIVE",
              allocated: childStatus,
            });
          }
        }
      }

      return result;
    };

    // Flatten the tree and return
    return flattenTree(categoryTree);
  }

  static async countStatus({
    db,
    orgId,
    categoryId
  }: {
    db: PrismaClientType;
    orgId: string;
    categoryId?: string;
  }): Promise<{ total: number; linked: number }> {
    const totalSubCategory = await db.category.count({
      where: {
        orgId,
        parentId: categoryId ?? {
          not: null,
        },
        status: {
          not: CategoryStatus.INACTIVE,
        },
      },
    });

    const totalLinkedSubCategory = await db.category.count({
      where: {
        orgId,
        parentId: categoryId ?? {
          not: null,
        },
        documents: {
          some: {},
        },
        status: {
          not: CategoryStatus.INACTIVE,
        },
      },
    });

    return {
      total: totalSubCategory,
      linked: totalLinkedSubCategory,
    };
  }
}
