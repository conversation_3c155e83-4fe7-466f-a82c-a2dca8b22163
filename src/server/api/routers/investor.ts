import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";

export const investorRouter = createTRPCRouter({
  getInvestors: protectedProcedure
    .input(
      z.object({
        searchTerm: z.string().optional(),
        geography: z.string().optional(),
        type: z.string().optional(),
        aum: z
          .object({
            from: z.number().optional(),
            to: z.number().optional(),
          })
          .optional(),
        statuses: z.array(z.string()).optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const orgId = ctx.org.id ?? "";
      return [];
    }),
});
