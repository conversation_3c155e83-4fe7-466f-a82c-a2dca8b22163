import React, { useCallback, useState } from "react";
import { AnswerGenerationType, ResponseStatus } from "@prisma/client";
import { cn } from "~/v2/lib/utils";
import { Typography } from "~/v2/components/ui/Typography";
import { SourceResponseLabel } from "../OfficeAddIn/IndexView/SingleDDQWithData/SourceResponseLabel";
import { toast } from "~/components/snackbar";
import { TooltipWithLogo } from "../OfficeAddIn/TooltipWithLogo";
import { Markdown } from "../OfficeAddIn/Markdown";
import { NoResponseLabel } from "../Labels/NoResponseLabel";
import { StatusResponseLabel } from "../OfficeAddIn/IndexView/SingleDDQWithData/StatusResponseLabel";
import { InsufficientDataLabel } from "~/components/virgil/Labels/InsufficientDataLabel";
import { AnsweredLabel } from "~/components/virgil/Labels/AnsweredLabel";
import { isResponseValid } from "../OfficeAddIn/isResponseValid";
import { SimilarityScoreLabel } from "../Labels/SimilarityScoreLabel";

type QuestionDetailsProps = {
    responseReason: string;
    responseText?: string | null;
    insufficientData: boolean;
    isAnswered: boolean;
    isGenerating: boolean;
    responseStatus?: ResponseStatus;
    answerGenerationType?: AnswerGenerationType;
    maxHeight?: number;
    displayAnsweredStatus?: boolean;
    topSimilarityScore?: number;
    showStandardResponses?: boolean;
    responseSource?: string;
    topSimilarityQuestionText?: string;
}

export const ResponseDetails = ({
    responseReason,
    responseStatus,
    responseText,
    answerGenerationType,
    insufficientData,
    isAnswered,
    isGenerating,
    maxHeight = 300,
    displayAnsweredStatus = false,
    topSimilarityScore,
    topSimilarityQuestionText,
    showStandardResponses,
    responseSource = ""
}: QuestionDetailsProps) => {
    const [isMarkdownHovered, setIsMarkdownHovered] = useState(false);
    const onMouseEnter = useCallback((e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        setIsMarkdownHovered(true);
    }, [setIsMarkdownHovered]);
    const onMouseLeave = useCallback((e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        setIsMarkdownHovered(false);
    }, [setIsMarkdownHovered]);
    const onMarkdownClick = useCallback((e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        if (responseText) {
            navigator.clipboard.writeText(responseText);
        }
        toast.success("Copied to clipboard", {
            position: "bottom-right",
            duration: 1000,
        });
    }, [responseText]);
    return <div className="space-y-[8px]">
        <div className="flex items-center space-x-2">
            {isAnswered && displayAnsweredStatus && (
                <AnsweredLabel />
            )}
            {insufficientData && (
                <InsufficientDataLabel />
            )}
            {!isResponseValid(responseText) && (
                <NoResponseLabel label={showStandardResponses ? "No similar questions found" : "No response generated"} />
            )}
            {isResponseValid(responseText) && !isAnswered && (
                <>
                    {topSimilarityScore && showStandardResponses ? (
                        <TooltipWithLogo position="bottom" header={`Source file: ${responseSource}`} text={`Source question: ${topSimilarityQuestionText ?? ""}`}>
                            <SourceResponseLabel answerGenerationType={answerGenerationType} />
                        </TooltipWithLogo>
                    ) : (
                        <TooltipWithLogo position="bottom" text={responseReason} header="Virgil Reasoning">
                            <SourceResponseLabel answerGenerationType={answerGenerationType} />
                        </TooltipWithLogo>
                    )}

                    <StatusResponseLabel status={responseStatus} />
                    {topSimilarityScore && showStandardResponses && <SimilarityScoreLabel similarity={topSimilarityScore} />}
                    <Typography
                        variant="caption"
                        onClick={onMarkdownClick}
                        className={cn(
                            "cursor-pointer",
                            isMarkdownHovered ? "opacity-100" : "opacity-0",
                            "transition-opacity duration-500 ease-in-out",
                            "hover:opacity-100 hover:underline",
                            "underline"
                        )}
                    >
                        Click to copy
                    </Typography>
                </>
            )}

        </div>
        <div className="overflow-y-auto" style={{ maxHeight: maxHeight }}>
            <Markdown
                onMouseEnter={onMouseEnter}
                onMouseLeave={onMouseLeave}
                onClick={onMarkdownClick}
                markdown={responseText ?? ""}
                className={`add-in${isGenerating ? " fade" : ""}`}
            />
        </div>
    </div>;
};