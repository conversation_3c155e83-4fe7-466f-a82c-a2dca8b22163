import { debounce } from "@mui/material";

import { Input } from "~/v2/components/ui/Input";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setSearchDebounced, setSearchInput } from "~/lib/features/documentDDQSlice";
import { selectSearchInput } from "~/lib/features/stateSelecrors";
import { Button } from "~/v2/components";
import { SearchIcon, XIcon } from "lucide-react";

const DEBOUNCE_TIME = 300;
export const SearchWithExpand: React.FC = () => {
    const dispatch = useDispatch();
    const [expanded, setExpanded] = useState(false);
    const searchInput = useSelector(selectSearchInput);
    const debouncedSearch = debounce((value: string) => {
        dispatch(setSearchDebounced(value.toLowerCase().trim()));
    }, DEBOUNCE_TIME);

    useEffect(() => {
        return () => {
            debouncedSearch.clear();
        };
    }, []);

    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        dispatch(setSearchInput(event.target.value));
        debouncedSearch(event.target.value);
    };

    const handleClearSearch = (e: React.MouseEvent<HTMLDivElement>) => {
        e.stopPropagation();
        dispatch(setSearchInput(""));
        dispatch(setSearchDebounced(""));
    };

    if (expanded) {
        return (
            <div className="flex w-full items-center absolute right-0 top-0 left-0 bottom-0 p-1">
                <div className="relative w-full flex items-center">
                    <Input
                        value={searchInput}
                        onChange={handleSearchChange}
                        placeholder="Search"
                        className="w-full bg-white"
                    />
                    <Button
                        variant="ghost"
                        onClick={() => setExpanded(false)}
                        className="absolute right-0 top-0 z-10">
                        <XIcon />
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <Button
            variant="ghost"
            onClick={() => setExpanded(true)}>
            <SearchIcon />
        </Button>
    );
}
