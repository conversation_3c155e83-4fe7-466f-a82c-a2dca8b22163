import { AnswerGenerationType, QuestionStatusType } from "@prisma/client";
import { DDQuestionWithFeedbackAndSimilarQuestions } from "~/server/api/managers/questionManager";

export default function parseQuestionData(
  question: DDQuestionWithFeedbackAndSimilarQuestions,
  showStandardResponses = false,
) {
  const topSimilarQuestion = question.similarQuestions?.sort(
    (a, b) => (b.similarity ?? 0) - (a.similarity ?? 0),
  )[0];

  const topSimilarityScore = topSimilarQuestion?.similarity;

  const { text: questionText, id: questionId, response } = question;

  const originalAnswerGenerationType =
    response?.responseContents?.[0]?.answerGenerationType;

  // Show top matched response only if original answer is AI generated
  const shouldUseTopMatchedStandardResponse =
    originalAnswerGenerationType === AnswerGenerationType.GENERATED &&
    showStandardResponses &&
    topSimilarQuestion;

  const responseContents = shouldUseTopMatchedStandardResponse
    ? topSimilarQuestion?.responseContents
    : response?.responseContents;

  // Conditionally override
  const answerGenerationType = shouldUseTopMatchedStandardResponse
    ? AnswerGenerationType.EXTRACTED
    : responseContents?.[0]?.answerGenerationType;
  const responseStatus = shouldUseTopMatchedStandardResponse
    ? topSimilarQuestion?.responseContents?.[0]?.status
    : response?.status;
  const responseCanonicalId = shouldUseTopMatchedStandardResponse
    ? (topSimilarQuestion.responseId ?? "")
    : (response?.id ?? "");
  const responseId = shouldUseTopMatchedStandardResponse
    ? (responseContents?.[0]?.id ?? "")
    : (response?.responseContents?.[0]?.id ?? "");
  const responseSource = shouldUseTopMatchedStandardResponse
    ? topSimilarQuestion?.documentName
    : null;

  const responseContent = responseContents?.[0]?.content as
    | { reason?: string; text?: string; insufficientData?: boolean }
    | undefined;
  const responseReason = responseContent?.reason ?? "";
  const responseText = responseContent?.text ?? "";

  const documentId = response?.documents[0]?.documentId ?? "";
  const insufficientData = responseContent?.insufficientData ?? false;
  let topSimilarityQuestionText = "";

  try {
    topSimilarityQuestionText = (
      topSimilarQuestion?.questionContents[0]?.content as { text: string }
    ).text;
  } catch (e) {
    console.error(e);
  }

  return {
    questionText,
    questionId,
    answerGenerationType,
    responseReason,
    responseStatus,
    responseText,
    responseId,
    responseCanonicalId,
    documentId,
    insufficientData,
    questionStatus: question.status,
    isAnswered: question.status === QuestionStatusType.ANSWERED,
    topSimilarityScore,
    responseSource,
    topSimilarQuestion,
    topSimilarityQuestionText,
  };
}
