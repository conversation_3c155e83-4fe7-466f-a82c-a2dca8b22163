import { Search } from "./Search";
import { QuestionStatusFilter } from "../QuestionStatusFilter";
import { SearchWithExpand } from "./SearchWithExpand";
import { StandardResponseSwitch } from "./StandardResponseSwitch";

export const ActionsBar: React.FC = () => {
    return (
        <div style={{
            display: 'flex',
            position: 'relative',
            justifyContent: 'space-between',
            gap: 1,
            alignItems: 'center',
            width: '100%',
            padding: 'var(--spacing-2)',
            borderBottom: '1px solid #e0e0e0',
            borderTop: '1px solid #e0e0e0',
            backgroundColor: '#fff',
            height: '50px',
        }}>
            <StandardResponseSwitch />
            <div className="flex items-center justify-end gap-1">
                <SearchWithExpand />
                <QuestionStatusFilter />
            </div>
        </div>
    )
}