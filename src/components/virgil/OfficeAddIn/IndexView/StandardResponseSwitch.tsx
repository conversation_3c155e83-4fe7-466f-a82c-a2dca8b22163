import { useDispatch, useSelector } from "react-redux";
import { selectShowStandardResponses } from "~/lib/features/stateSelecrors";
import { Typography } from "~/v2/components";
import { setShowStandardResponses } from "~/lib/features/addInSlice";
import { Switch } from "~/v2/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/v2/components/ui/Tooltip";

export const StandardResponseSwitch: React.FC = () => {
  const dispatch = useDispatch();
  const showStandardResponses = useSelector(selectShowStandardResponses);

  return (
    <Tooltip>
      <TooltipTrigger>
        <div className="flex justify-between items-center">
          <Switch
            checked={showStandardResponses}
            onCheckedChange={(checked) => {
              dispatch(setShowStandardResponses(checked));
            }}
          />
          <Typography
            variant="boldBody"
            className="ml-2"
            style={{ cursor: "pointer" }}
            onClick={() => {
              dispatch(setShowStandardResponses(!showStandardResponses));
            }}
          >
            Show standard responses
          </Typography>
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <Typography variant="body" className="w-[200px]">
          Prioritize standard content for similar questions
        </Typography>
      </TooltipContent>
    </Tooltip>
  );
};
