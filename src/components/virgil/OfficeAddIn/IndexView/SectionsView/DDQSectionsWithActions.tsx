import React, { useCallback, useEffect, useMemo } from "react";
import { DDQuestionWithFeedbackAndSimilarQuestions } from "~/server/api/managers/questionManager";
import { eventBus } from "~/lib/eventBus";
import { Searcher } from "fast-fuzzy";
import parseQuestionData from "../SingleDDQWithData/parseQuestionData";
import { DDQSections } from "./DDQSections";
import { useDDQuestionsInfinite } from "~/components/virgil/hooks/useDDQuestionsInfinite";
import { scrollToTextFuzzy } from "../../addInUtils/word/word";
import InfoBox from "../InfoBox";

const DDQSectionsWithActions: React.FC = () => {
  const { questions, questionsBySection, isLoading, refetch } =
    useDDQuestionsInfinite();
  const questionsSearcherMemo = useMemo(() => {
    return new Searcher(questions, { keySelector: (obj) => obj.text });
  }, [questions]);

  const wordSelectionChanged = useCallback(
    ({ paragraph }: { paragraph: string }) => {
      // user is in the word document and not in the plugin
      if (paragraph && !document.hasFocus()) {
        const matchingQuestion = questionsSearcherMemo.search(paragraph)?.[0];
        if (matchingQuestion) {
          eventBus.emit("scrollToQuestion", {
            questionId: matchingQuestion.id,
            sectionId: matchingQuestion.response?.documentSection?.id ?? "",
          });
        }
      }
    },
    [questionsSearcherMemo],
  );

  const dialogMessageReceived = useCallback(
    ({ message }: { message: string }) => {
      try {
        const data = JSON.parse(message);
        if (data.type === "response-updated") {
          console.log("Response updated received", data);
          refetch(); // TODO: refetch only the question that was updated
        }
      } catch (error) {
        console.error("Error parsing dialog message:", error);
      }
    },
    [],
  );

  // this runs after expanded item was rendered and animation is complete
  const onExpandedItemChanged = useCallback(
    async (item: DDQuestionWithFeedbackAndSimilarQuestions) => {
      if (document.hasFocus()) {
        const { questionText } = parseQuestionData(item);
        await scrollToTextFuzzy(questionText);
      }
    },
    [],
  );

  useEffect(() => {
    eventBus.on("wordSelectionChanged", wordSelectionChanged);
    return () => {
      eventBus.off("wordSelectionChanged", wordSelectionChanged);
    };
  }, [wordSelectionChanged]);

  useEffect(() => {
    eventBus.on("dialogMessageReceived", dialogMessageReceived);
    return () => {
      eventBus.off("dialogMessageReceived", dialogMessageReceived);
    };
  }, [dialogMessageReceived]);

  if (isLoading || !questionsBySection) {
    return <InfoBox text="Loading questions..." />;
  }

  if (questions.length === 0) {
    return (
      <InfoBox text="No diligence questions found, please re-process document in the Data Room" />
    );
  }

  return (
    <DDQSections
      questionsBySection={questionsBySection}
      questions={questions}
      onExpand={onExpandedItemChanged}
    />
  );
};

export default DDQSectionsWithActions;
