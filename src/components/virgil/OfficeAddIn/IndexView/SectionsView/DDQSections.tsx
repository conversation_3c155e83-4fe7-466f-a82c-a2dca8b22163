import { AccordionRoot, AccordionContent, AccordionHeader, AccordionItem, AccordionTrigger } from "~/v2/components/ui/Accordion/Accordion";
import { Typography } from "~/v2/components/ui/Typography";
import { DDQSectionWithQuestions, DDQuestionWithIndexAndFeedback } from "~/server/api/managers/questionManager";
import { CollapsedContent } from "../SingleDDQWithData/CollapsedContent";
import { useCallback, useEffect, useRef } from "react";
import { useState } from "react";
import { ExpandedContent } from "../SingleDDQWithData/ExpandedContent";
import { useDDQSelect } from "~/components/virgil/contexts/DDQSelectContext";
import { Checkbox } from "~/v2/components";
import { eventBus, EventBusEvents } from "~/lib/eventBus";

export const DDQSections = ({
    questionsBySection,
    questions,
    onExpand,
}: {
    questionsBySection: DDQSectionWithQuestions;
    questions: DDQuestionWithIndexAndFeedback[];
    onExpand: (item: DDQuestionWithIndexAndFeedback) => void;
}) => {
    const [expandedSectionId, setExpandedSectionId] = useState<string>("");
    const [expandedQuestionId, setExpandedQuestionId] = useState<string>("");
    const [activeItem, setActiveItem] = useState<string>("");
    const shouldScrollToQuestionRef = useRef<boolean>(false);
    const { selectedIds: selectedQuestionIds, handleSelectSingle: handleSelectSingleQuestion, handleSelectSection, selectedSectionIds, selectedPerSection } = useDDQSelect();

    const handleToggleQuestion = useCallback((item: DDQuestionWithIndexAndFeedback) => {
        setExpandedQuestionId(expandedQuestionId === item.id ? "" : item.id);
        setActiveItem(item.id);
        onExpand(item);
    }, [expandedSectionId, onExpand]);

    const handleScrollToQuestion = useCallback((event: EventBusEvents["scrollToQuestion"]) => {
        const question = questions.find((question) => question.id === event.questionId);
        if (question) {
            setExpandedQuestionId(question.id);
            setActiveItem(question.id);
            setExpandedSectionId(question.response?.documentSection?.id ?? "");
            shouldScrollToQuestionRef.current = true;
        }
    }, [questions, setExpandedQuestionId, setActiveItem, setExpandedSectionId]);

    useEffect(() => {
        eventBus.on("scrollToQuestion", handleScrollToQuestion);
        return () => {
            eventBus.off("scrollToQuestion", handleScrollToQuestion);
        };
    }, [handleScrollToQuestion]);

    useEffect(() => {
        if (shouldScrollToQuestionRef.current && expandedQuestionId) {
            document.querySelector(`[data-question-id="${expandedQuestionId}"]`)?.scrollIntoView();
            shouldScrollToQuestionRef.current = false;
        }
    }, [expandedQuestionId]);

    return (
        <AccordionRoot type="single" collapsible value={expandedSectionId} onValueChange={setExpandedSectionId}>
            {Object.values(questionsBySection).map((section) => (
                <AccordionItem key={section.sectionId} value={section.sectionId}>
                    <AccordionTrigger className="flex items-center gap-x-2">
                        <Checkbox
                            checked={selectedSectionIds.has(section.sectionId)}
                            onCheckedChange={() => {
                                handleSelectSection(section.sectionId);
                            }}
                            onClick={(e) => e.stopPropagation()}
                        />
                        <Typography variant="sectionHeader">
                            {section.title} ({selectedPerSection[section.sectionId] ?? 0 > 0 ? `${selectedPerSection[section.sectionId]}/${section.questions.length} selected` : `${section.questions.length}`})
                        </Typography>
                    </AccordionTrigger>
                    <AccordionContent>
                        {section.questions.map((question) => (
                            expandedQuestionId === question.id ?
                                <ExpandedContent
                                    key={question.id}
                                    question={question}
                                    isChecked={selectedQuestionIds.has(question.id)}
                                    setIsChecked={handleSelectSingleQuestion}
                                />
                                :
                                <CollapsedContent
                                    key={question.id}
                                    question={question}
                                    isChecked={selectedQuestionIds.has(question.id)}
                                    setIsChecked={handleSelectSingleQuestion}
                                    handleToggle={handleToggleQuestion}
                                    isActive={question.id === activeItem}
                                />
                        ))}
                    </AccordionContent>
                </AccordionItem>
            ))}
        </AccordionRoot>
    );
};