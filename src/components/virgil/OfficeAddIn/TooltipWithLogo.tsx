import { Markdown } from "./Markdown";
import { Too<PERSON><PERSON>, TooltipContent, TooltipTrigger } from "~/v2/components/ui/Tooltip";
import { Typography } from "~/v2/components";

export const TooltipWithLogo = ({ text, header, children, position = "top" }: { text: string, header: string, children: React.ReactNode, position?: "top" | "bottom" }) => {
    return (
        <Tooltip>
            <TooltipTrigger>
                {children}
            </TooltipTrigger>
            <TooltipContent side={position} className="w-[300px] max-w-full">
                <Typography variant="boldBody" color="text.secondary" className="line-height-1">{header}</Typography>
                <Typography variant="body" color="text.secondary"><Markdown markdown={text} /></Typography>
            </TooltipContent>
        </Tooltip>
    )
}