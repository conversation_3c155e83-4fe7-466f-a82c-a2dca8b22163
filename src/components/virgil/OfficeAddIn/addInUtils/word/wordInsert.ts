import { convertMarkdownToHtml } from "../../markdownUtils/markdownUtils";
import { HtmlNode, parseHtmlToTree } from "../transformerUtils";
export type TextStyles = {
  applyTextStyle?: boolean;
  color: string;
  fontSize: number;
  bold: boolean;
  italic: boolean;
  underline: boolean;
};

function applyTextStyles(range: Word.Range, textStyles: TextStyles) {
  if (textStyles?.applyTextStyle) {
    range.font.color = textStyles.color;
    range.font.size = textStyles.fontSize;
    range.font.bold = textStyles.bold;
    range.font.italic = textStyles.italic;
    range.font.underline = textStyles.underline ? "Single" : "None";
  }
}

async function insertParagraph({
  anchor,
  node,
  textStyles,
}: {
  anchor: Word.Range;
  node: HtmlNode;
  textStyles: TextStyles;
}) {
  const paragraph = anchor.insertParagraph(
    node.text ?? "",
    Word.InsertLocation.after,
  );
  paragraph.styleBuiltIn = "Normal"; // Ensure it's a regular paragraph, not a list item
  const range = paragraph.getRange("Whole");
  applyTextStyles(range, textStyles);
  return insertNodes({
    anchor: range,
    nodes: node.children ?? [],
    textStyles,
  });
}

async function insertText({
  anchor,
  text,
  bold = false,
  italic = false,
  underline = false,
  textStyles,
}: {
  anchor: Word.Range;
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  textStyles: TextStyles;
}) {
  const range = anchor.insertText(text, Word.InsertLocation.after);
  if (bold) {
    range.font.bold = true;
  }
  if (italic) {
    range.font.italic = true;
  }
  if (underline) {
    range.font.underline = "Single";
  }
  applyTextStyles(range, textStyles);
  range.paragraphs.getLast().alignment = "Left";
  return range.getRange("End");
}

async function insertLink({
  anchor,
  text,
  textStyles,
}: {
  anchor: Word.Range;
  text: string;
  textStyles: TextStyles;
}) {
  const range = anchor.insertText(text, Word.InsertLocation.after);
  applyTextStyles(range, textStyles);
  range.font.underline = "Single";
  return range.getRange("End");
}

async function insertTable({
  anchor,
  node,
  textStyles,
}: {
  anchor: Word.Range;
  node: HtmlNode;
  textStyles: TextStyles;
}) {
  if (node.tableData) {
    // TODO: style table
    const table = anchor.insertTable(
      node.tableData.length,
      node.tableData[0]?.length ?? 0,
      Word.InsertLocation.after,
      node.tableData,
    );
    table.getRange().styleBuiltIn = "Normal";
    applyTextStyles(table.getRange("Whole"), textStyles);
    return table.getRange("After");
  }
  return anchor;
}

async function insertListItem({
  list,
  node,
  textStyles,
}: {
  list: Word.List;
  node: HtmlNode;
  textStyles: TextStyles;
}) {
  const listItem = list.insertParagraph(
    node.text ?? "",
    Word.InsertLocation.end,
  );
  applyTextStyles(listItem.getRange("Whole"), textStyles);
  return insertNodes({
    anchor: listItem.getRange("End"),
    nodes: node.children ?? [],
    textStyles,
  });
}

async function insertList({
  anchor,
  node,
  type = "bulleted",
  list,
  listLevel = 0,
  textStyles,
}: {
  anchor: Word.Range;
  node: HtmlNode;
  type: "bulleted" | "numbered";
  list?: Word.List;
  listLevel?: number;
  textStyles: TextStyles;
}) {
  if (list) {
    return insertNodes({
      anchor,
      nodes: node.children ?? [],
      list,
      listLevel,
      textStyles,
    });
  }

  // special treatment for first item in list
  const paragraph = anchor.insertParagraph(
    node.children?.[0]?.text ?? "",
    Word.InsertLocation.after,
  );
  applyTextStyles(paragraph.getRange("Whole"), textStyles);
  const newList = paragraph.startNewList();
  await insertNodes({
    anchor: paragraph.getRange("End"),
    nodes: node.children?.[0]?.children ?? [],
    textStyles,
  });

  return insertNodes({
    anchor: paragraph.getRange("End"),
    nodes: node.children?.slice(1) ?? [], // skip first item in list
    list: newList,
    listLevel,
    textStyles,
  });
}

async function insertSingleNode({
  anchor,
  node,
  list,
  listLevel,
  textStyles,
}: {
  anchor: Word.Range;
  node: HtmlNode;
  list?: Word.List;
  listLevel?: number;
  textStyles: TextStyles;
}): Promise<Word.Range> {
  switch (node.type) {
    case "p":
    case "h1":
    case "h2":
    case "h3":
    case "h4":
    case "h5":
    case "h6":
      return await insertParagraph({
        anchor,
        node,
        textStyles,
      });
    case "text":
      return await insertText({
        anchor,
        text: node.text ?? "",
        textStyles,
      });
    case "a":
    case "link":
      return await insertLink({
        anchor,
        text: node.text ?? "",
        textStyles,
      });
    case "strong":
    case "bold":
      return await insertText({
        anchor,
        text: node.text ?? "",
        bold: true,
        textStyles,
      });
    case "ul":
      return await insertList({
        anchor,
        node,
        type: "bulleted",
        list,
        listLevel,
        textStyles,
      });
    case "ol":
      return await insertList({
        anchor,
        node,
        type: "numbered",
        list,
        listLevel,
        textStyles,
      });
    case "li":
      if (!list) {
        break;
      }
      return await insertListItem({
        list,
        node,
        textStyles,
      });
    case "container":
      if (!node.children) {
        break;
      }
      return await insertNodes({
        anchor,
        nodes: node.children ?? [],
        textStyles,
      });
    case "table":
      return await insertTable({
        anchor,
        node,
        textStyles,
      });
  }

  return anchor;
}

export async function insertNodes({
  anchor,
  nodes,
  list,
  listLevel,
  textStyles,
}: {
  anchor: Word.Range;
  nodes: HtmlNode[];
  list?: Word.List;
  listLevel?: number;
  textStyles: TextStyles;
}) {
  let currentAnchor = anchor;
  for (const node of nodes) {
    currentAnchor = await insertSingleNode({
      anchor: currentAnchor,
      node,
      list,
      listLevel,
      textStyles,
    });
  }

  return currentAnchor;
}

export async function insertMarkdown(
  range: Word.Range,
  markdown: string,
  textStyles: TextStyles,
) {
  const html = convertMarkdownToHtml(markdown);
  const tree = parseHtmlToTree(html);
  await insertNodes({
    anchor: range,
    nodes: tree,
    textStyles,
  });
}
