import { useState } from "react";
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react";
import { Typography } from "~/v2/components";

type TextAccordionProps = {
    text: string;
    header: string;
}

const TextAccordion = ({ text, header }: TextAccordionProps) => {
    const [expanded, setExpanded] = useState(false);

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setExpanded(!expanded);
    };

    return (
        <div>
            <Typography
                onClick={handleClick}
                variant="boldBody"
                className="cursor-pointer text-secondary inline-flex items-center select-none"
            >
                {expanded ? (
                    <ChevronDownIcon className="w-4 h-4 inline-block" />
                ) : (
                    <ChevronRightIcon className="w-4 h-4 inline-block" />
                )}
                <span className="ml-1">{header}</span>
            </Typography>

            {expanded && (
                <Typography
                    variant="body"
                    className="text-secondary font-italic mt-0.5"
                >
                    {text}
                </Typography>
            )}
        </div>
    );
};

export default TextAccordion;
