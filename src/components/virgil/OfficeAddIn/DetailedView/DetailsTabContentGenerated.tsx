import { CitationsAccordionList } from "./CitationsAccordionList";
import { Box, Divider, Stack } from "@mui/material";
import { Citation } from "~/lib/types";
import { ResponseEditsTimelineContainer } from "~/views/response/ResponseEditsTimelineContainer";
import {
  DDQuestionWithFeedbackAndSimilarQuestions,
  DDQuestionWithIndexAndFeedback,
} from "~/server/api/managers/questionManager";
import { QuestionActivityTimelineContainer } from "~/views/response/QuestionActivityTimelineContainer";

type DetailsTabContentProps = {
  question:
    | DDQuestionWithIndexAndFeedback
    | DDQuestionWithFeedbackAndSimilarQuestions;
};
export default function DetailsTabContentGenerated({
  question,
}: DetailsTabContentProps) {
  const citations =
    question?.response?.responseContents
      ?.map((rs) => (rs.content as { citations: Citation[] })?.citations)
      .flat() ?? [];
  return (
    <Box sx={{ overflow: "auto", height: "calc(100vh - 55px - 50px)", p: 2 }}>
      <Stack direction="column" spacing={2}>
        <CitationsAccordionList citations={citations ?? []} />
        <Divider
          sx={{
            my: 2,
          }}
        />
        <QuestionActivityTimelineContainer questionId={question.id} />
      </Stack>
    </Box>
  );
}
