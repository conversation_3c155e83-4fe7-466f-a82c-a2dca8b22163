import { DocumentStatus } from "@prisma/client";
import { useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import {
  selectSearchDebounced,
  selectSelectedQuestionStatusTypes,
  selectSelectedResponseStatuses,
} from "~/lib/features/documentDDQSelector";
import {
  DDQSectionWithQuestions,
  DDQuestionWithIndexAndFeedback,
} from "~/server/api/managers/questionManager";
import { selectDocumentMetadataDocumentStatus } from "~/lib/features/documentMetadataSelectors";
import { selectDocumentId } from "~/lib/features/addInSelectors";
import { api } from "~/trpc/react";

declare global {
  interface Window {
    debug_duplicateQuestions: DDQuestionWithIndexAndFeedback[];
  }
}

export const useDDQuestionsInfinite = () => {
  const documentId = useSelector(selectDocumentId);
  const searchString = useSelector(selectSearchDebounced);
  const documentStatus = useSelector(selectDocumentMetadataDocumentStatus);
  const selectedQuestionStatusTypes = useSelector(
    selectSelectedQuestionStatusTypes,
  );
  const selectedResponseStatuses = useSelector(selectSelectedResponseStatuses);

  const defaultQueryInput = {
    documentId,
    limit: 100,
    questionStatusTypes: selectedQuestionStatusTypes,
    responseStatuses: selectedResponseStatuses,
  };

  const querySettings = {
    enabled:
      !!documentId &&
      (documentStatus === DocumentStatus.READY ||
        documentStatus === DocumentStatus.GENERATING_ANSWERS),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    // refetchInterval: 10000,
  };

  const {
    data,
    isLoading,
    isPending,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = api.question.getAllQuestionsInfinite.useInfiniteQuery(defaultQueryInput, {
    ...querySettings,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
  });

  useEffect(() => {
    if (hasNextPage && !isFetchingNextPage) {
      void fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const allQuestions: DDQuestionWithIndexAndFeedback[] = useMemo(
    () =>
      (data?.pages.flatMap((page) => page.items) ?? []).map((q, index) => ({
        ...q,
        index,
      })),
    [data?.pages],
  );
  // Temp hack to remove duplicate questions
  const { uniqueQuestions, duplicateQuestions } = useMemo(() => {
    const duplicateQuestionsDict: Record<
      string,
      DDQuestionWithIndexAndFeedback
    > = {};
    const questionMap = allQuestions.reduce(
      (acc, q: DDQuestionWithIndexAndFeedback) => {
        if (!acc.has(q.text)) {
          acc.set(q.text, q);
        } else {
          duplicateQuestionsDict[q.text] = q;
        }
        return acc;
      },
      new Map<string, DDQuestionWithIndexAndFeedback>(),
    );

    const uniqueQuestions = [...questionMap.values()].sort(
      (a, b) => a.index - b.index,
    );
    const duplicateQuestions = Object.values(duplicateQuestionsDict);
    console.log("duplicateQuestions:", duplicateQuestions);
    window.debug_duplicateQuestions = duplicateQuestions;
    return { uniqueQuestions, duplicateQuestions };
  }, [allQuestions]);

  const questionsWithSearch: DDQuestionWithIndexAndFeedback[] = useMemo(() => {
    return uniqueQuestions
      .filter((question) =>
        question.text.toLowerCase().includes(searchString?.toLowerCase() ?? ""),
      )
      .map((question, index) => ({
        ...question,
        index,
      }));
  }, [allQuestions, searchString]);

  const questionsBySection: DDQSectionWithQuestions = useMemo(() => {
    return questionsWithSearch.reduce((acc, question) => {
      const sectionId: string | undefined =
        question.response?.documentSection?.id;
      if (sectionId) {
        if (!acc[sectionId]) {
          acc[sectionId] = {
            sectionId,
            title: question.response?.documentSection?.title ?? "",
            index: question.index,
            questions: [question],
          };
        } else {
          acc[sectionId].questions.push(question);
          acc[sectionId].index = Math.min(acc[sectionId].index, question.index);
        }
        return acc;
      }
      return acc;
    }, {} as DDQSectionWithQuestions);
  }, [uniqueQuestions, questionsWithSearch]);

  console.log("questionsBySection:", questionsBySection);

  return {
    questions: questionsWithSearch,
    refetch,
    questionsBySection,
    duplicateQuestions,
    isLoading: isLoading || isPending,
    isFetchingNextPage,
    hasNextPage,
  };
};
