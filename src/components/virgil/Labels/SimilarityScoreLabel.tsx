import { CopyCheck } from "lucide-react";
import { GenericLabel } from "~/components/virgil/Labels/GenericLabel";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/v2/components/ui/Tooltip";
import { Typography } from "~/v2/components/ui/Typography";

export const SimilarityScoreLabel = ({ similarity }: { similarity: number }) => {
    return (
        <Tooltip>
            <TooltipTrigger>
                <GenericLabel label={`${similarity}% Match`} icon={CopyCheck} className="text-cyan-800 bg-cyan-50" />
            </TooltipTrigger>
            <TooltipContent>
                <div className="w-[300px]">
                    <Typography variant="body" color="text.secondary">
                        {`We’ve found a response that is a ${similarity}% match to this question.`}
                    </Typography>
                </div>
            </TooltipContent>
        </Tooltip>
    )
};