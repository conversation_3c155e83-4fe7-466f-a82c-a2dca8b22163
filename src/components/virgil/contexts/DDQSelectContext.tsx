import React, {
  createContext,
  useContext,
  useCallback,
  useEffect,
  useState,
  ReactNode,
  useMemo,
} from "react";
import {
  DDQSectionWithQuestions,
  DDQuestionWithFeedbackAndSimilarQuestions,
} from "~/server/api/managers/questionManager";

interface DDQSelectContextType {
  selectedIds: Set<string>;
  selectedSectionIds: Set<string>;
  setSelectedIds: React.Dispatch<React.SetStateAction<Set<string>>>;
  isAllSelected: boolean;
  handleSelectAll: () => void;
  handleSelectSingle: (id: string) => void;
  handleSelectSection: (sectionId: string) => void;
  totalSelected: number;
  totalCount: number;
  selectedPerSection: Record<string, number>;
}

const DDQSelectContext = createContext<DDQSelectContextType | undefined>(
  undefined,
);

interface DDQSelectProviderProps {
  children: ReactNode;
  questions: DDQuestionWithFeedbackAndSimilarQuestions[];
  questionsBySection: DDQSectionWithQuestions;
}

export const DDQSelectProvider: React.FC<DDQSelectProviderProps> = ({
  children,
  questions,
  questionsBySection,
}) => {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [selectedSectionIds, setSelectedSectionIds] = useState<Set<string>>(
    new Set(),
  );
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [totalSelected, setTotalSelected] = useState(0);

  const handleSelectAll = () => {
    if (questions.every((q) => selectedIds.has(q.id))) {
      setSelectedIds(
        (prev) =>
          new Set(
            [...prev].filter((id) => !questions.map((q) => q.id).includes(id)),
          ),
      );
    } else {
      setSelectedIds(
        (prev) => new Set([...prev, ...questions.map((q) => q.id)]),
      );
    }
  };

  const handleSelectSection = useCallback(
    (sectionId: string) => {
      if (
        questionsBySection[sectionId]?.questions.every((q) =>
          selectedIds.has(q.id),
        )
      ) {
        setSelectedIds(
          (prev) =>
            new Set(
              [...prev].filter(
                (id) =>
                  !questionsBySection[sectionId]?.questions
                    .map((q) => q.id)
                    .includes(id),
              ),
            ),
        );
      } else {
        setSelectedIds(
          (prev) =>
            new Set([
              ...prev,
              ...(questionsBySection[sectionId]?.questions.map((q) => q.id) ??
                []),
            ]),
        );
      }
    },
    [questions, selectedIds, setSelectedIds, setSelectedSectionIds],
  );

  const handleSelectSingle = useCallback(
    (id: string) => {
      setSelectedIds((prev) =>
        prev.has(id)
          ? new Set([...prev].filter((prevId) => prevId !== id))
          : new Set([...prev, id]),
      );
    },
    [setSelectedIds],
  );

  // recalculate selectedSectionIds when selectedIds change
  useEffect(() => {
    const newSelectedSectionIds = new Set<string>();
    Object.values(questionsBySection).forEach((section) => {
      if (section.questions.every((q) => selectedIds.has(q.id))) {
        newSelectedSectionIds.add(section.sectionId);
      }
    });
    setSelectedSectionIds(newSelectedSectionIds);
  }, [selectedIds]);

  // Reset when questions change (page change)
  useEffect(() => {
    setTotalSelected(questions.filter((q) => selectedIds.has(q.id)).length);
    setIsAllSelected(questions.every((q) => selectedIds.has(q.id)));
  }, [questions, selectedIds]);

  const selectedPerSection = useMemo(() => {
    return Object.values(questionsBySection).reduce(
      (acc, section) => {
        acc[section.sectionId] = section.questions.filter((q) =>
          selectedIds.has(q.id),
        ).length;
        return acc;
      },
      {} as Record<string, number>,
    );
  }, [selectedIds, questionsBySection]);

  const value: DDQSelectContextType = useMemo(
    () => ({
      selectedIds,
      setSelectedIds,
      isAllSelected,
      handleSelectAll,
      handleSelectSection,
      handleSelectSingle,
      totalSelected,
      selectedSectionIds,
      selectedPerSection,
      totalCount: questions.length,
    }),
    [
      selectedIds,
      setSelectedIds,
      isAllSelected,
      handleSelectAll,
      handleSelectSection,
      handleSelectSingle,
      totalSelected,
      selectedSectionIds,
      selectedPerSection,
      questions,
    ],
  );

  return (
    <DDQSelectContext.Provider value={value}>
      {children}
    </DDQSelectContext.Provider>
  );
};

export const useDDQSelect = (): DDQSelectContextType => {
  const context = useContext(DDQSelectContext);
  if (context === undefined) {
    throw new Error("useDDQSelect must be used within a DDQSelectProvider");
  }
  return context;
};
