import { DDQStatus, DocumentStatus } from "@prisma/client";
import { GetDocumentsType } from "~/server/api/routers/document";
import {
  _companyNames,
  _countryNames,
  _emails,
  _fullAddress,
  _id,
  _phoneNumbers,
} from "./assets";

// ----------------------------------------------------------------------

export const _investorTypes = [
  "Public Pension",
  "Private Equity",
  "Hedge Fund",
  "Sovereign Wealth Fund",
  "Insurance Company",
  "Endowment",
  "Foundation",
  "Family Office",
  "Asset Manager",
];

export const _investorStatus = [
  "Active",
  "Inactive",
  "Prospective",
  "Under Review",
];

export const _investorRegions = [
  "North America",
  "Europe",
  "Asia Pacific",
  "Latin America",
  "Middle East & Africa",
  "Global",
];

export const _fundSizes = [
  "$100M-$500M",
  "$500M-$1B",
  "$1B-$2B",
  "$2B-$5B",
  "$5B+",
  "Not Disclosed",
];

export const _vintageYears = [
  "2020-2021",
  "2021-2022",
  "2022-2023",
  "2023-2024",
  "2024-2025",
  "2025-2026",
];

export const _sectorFocus = [
  "Technology",
  "Healthcare",
  "Financial Services",
  "Real Estate",
  "Infrastructure",
  "Energy",
  "Consumer Goods",
  "Industrials",
  "Diversified",
];

// Generate mock investor data
export const _investors = [...Array(24)].map((_, index) => ({
  id: _id[index],
  name: _companyNames[index],
  fullName: `${_companyNames[index]}`,
  description: `${_companyNames[index]} is a leading institutional investor`,
  type: _investorTypes[index % _investorTypes.length],
  status: _investorStatus[index % _investorStatus.length],
  geography: _investorRegions[index % _investorRegions.length],
  aum: _fundSizes[index % _fundSizes.length],
  website: `${_companyNames?.[index]?.toLowerCase().replace(/[^a-z0-9]/g, "")}.com`,
  email: _emails[index],
  phone: _phoneNumbers[index],
  address: _fullAddress[index],
  country: _countryNames[index % _countryNames.length],

  // Investment preferences
  fundSizePreference: _fundSizes[index % _fundSizes.length],
  vintageYearFocus: _vintageYears[index % _vintageYears.length],
  sectorFocus: _sectorFocus[index % _sectorFocus.length],

  // Alternative investment details
  alternativeInvestmentTarget: `${Math.floor(Math.random() * 15) + 5}%`,

  // Contact info
  lastContact: new Date(
    Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000,
  ),

  // Additional metadata
  createdAt: new Date(
    Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000,
  ),
  updatedAt: new Date(
    Date.now() - Math.floor(Math.random() * 7) * 24 * 60 * 60 * 1000,
  ),
}));

// Documents for each investor
export const _investorDocuments: Partial<GetDocumentsType>[] = [
  {
    id: "doc-1",
    name: "Brief 1",
    status: DocumentStatus.READY,
    updatedAt: new Date(
      Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000,
    ),
    ddqStatus: null,
  },
  {
    id: "doc-2",
    name: "Brief 1.1",
    status: DocumentStatus.READY,
    updatedAt: new Date(
      Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000,
    ),
    ddqStatus: DDQStatus.NEW,
  },
  {
    id: "doc-3",
    name: "Brief 1.2",
    status: DocumentStatus.READY,
    updatedAt: new Date(
      Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000,
    ),
    ddqStatus: DDQStatus.APPROVED,
  },
];

// Communications/Activity timeline
export const _investorCommunications = [
  {
    id: "comm-1",
    date: "January 15, 2025",
    title: "DDQ submitted for Fund IV",
    description: "View document",
    type: "document_submission",
  },
  {
    id: "comm-2",
    date: "December 18, 2024",
    title: "ESG Questionnaire completed",
    description: "View document",
    type: "document_completion",
  },
  {
    id: "comm-3",
    date: "September 10, 2024",
    title: "RFP response for Fund III follow-on",
    description: "View document",
    type: "rfp_response",
  },
  {
    id: "comm-4",
    date: "June 5, 2025",
    title: "Initial Fund Due Diligence Package",
    description: "View document",
    type: "due_diligence",
  },
];
