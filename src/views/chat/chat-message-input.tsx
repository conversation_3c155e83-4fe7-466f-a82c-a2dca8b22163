import {
  type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  use<PERSON><PERSON><PERSON>,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import IconButton from "@mui/material/IconButton";
import InputBase from "@mui/material/InputBase";

import { useRouter } from "src/routes/hooks";

import { fSub, today } from "src/utils/format-time";
import { uuidv4 } from "src/utils/uuidv4";

import CircularProgress from "@mui/material/CircularProgress";
import {
  ChatMessageStatus,
  type ChatParticipant,
  type User,
} from "@prisma/client";
import * as Sentry from "@sentry/react";
import {
  type ChatMessageGetType,
  type ChatParticipantGetType,
} from "~/server/api/routers/chat";
import { api } from "~/trpc/react";
import { statusMap } from "./chat-message-list";
import { type ModelParameters } from "./ChatModelParametersSelector";
// ----------------------------------------------------------------------

type Props = {
  disabled: boolean;
  recipients: ChatParticipant[];
  messages: ChatMessageGetType[];
  setMessages: (messages: ChatMessageGetType[]) => void;
  setNewMessage: (message: ChatMessageGetType) => void;
  selectedConversationId: string;
  onAddRecipients: (recipients: ChatParticipantGetType[]) => void;
  handleOnFocus: FocusEventHandler;
  userDetails: User | undefined;
  status: ChatMessageStatus;
  tagIds: string[];
  categoryIds: string[];
  fundIds: string[];
  modelParameters: ModelParameters;
  setModelParameters: (modelParameters: ModelParameters) => void;
};

export function ChatMessageInput({
  disabled,
  recipients,
  messages,
  setMessages,
  setNewMessage,
  onAddRecipients,
  selectedConversationId,
  handleOnFocus,
  userDetails,
  status,
  tagIds,
  categoryIds,
  fundIds,
  modelParameters,
  setModelParameters,
}: Props) {
  const router = useRouter();

  const fileRef = useRef<HTMLInputElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [inputStatusMessage, setInputStatusMessage] = useState<string>(
    statusMap[ChatMessageStatus.GENERATING_ANSWER],
  );
  const [hasBeenFocused, setHasBeenFocused] = useState(false);
  const org = api.organization.get.useQuery({});

  // Custom hook to handle focus restoration
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && inputRef.current && !disabled) {
        // Small delay to ensure the page is fully visible
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 100);
      }
    };

    const handleFocus = () => {
      if (inputRef.current && !disabled && !loading) {
        inputRef.current.focus();
      }
    };

    // Focus when page becomes visible
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Focus when window gains focus
    window.addEventListener("focus", handleFocus);

    // Focus when user clicks anywhere on the page (if input is not already focused)
    document.addEventListener("click", handleFocus);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleFocus);
      document.removeEventListener("click", handleFocus);
    };
  }, [disabled, loading]);

  // Show initial focus cue when component mounts
  useEffect(() => {
    if (!disabled && inputRef.current) {
      // Add a subtle glow effect to indicate the input is ready
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          // Trigger the focus animation
          setHasBeenFocused(true);
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [disabled]);

  const myContact = useMemo(
    () => ({
      id: `${userDetails?.id}`,
      role: "user",
      email: `${userDetails?.email}`,
      address: "",
      name: `${userDetails?.name}`,
      lastActivity: today(),
      avatarUrl: `${userDetails?.image}`,
      phoneNumber: "",
      status: "online" as "online" | "offline" | "alway" | "busy",
    }),
    [userDetails],
  );

  const messageData = useMemo(
    () => ({
      id: uuidv4(),
      attachments: [],
      body: message,
      contentType: "text",
      createdAt: fSub({ minutes: 1 }),
      senderId: myContact.id,
    }),
    [message, myContact.id],
  );

  const conversationData = useMemo(
    () => ({
      id: uuidv4(),
      messages: [messageData],
      participants: [...recipients, myContact],
      type: recipients.length > 1 ? "GROUP" : "ONE_TO_ONE",
      unreadCount: 0,
      createdById: userDetails?.id ?? "",
      createdAt: new Date(),
      updatedAt: new Date(),
      orgId: org.data?.id ?? "",
    }),
    [messageData, myContact, recipients, org.data],
  );

  const handleAttach = useCallback(() => {
    if (fileRef.current) {
      fileRef.current.click();
    }
  }, []);

  const handleChangeMessage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setMessage(event.target.value);
    },
    [],
  );

  const send = api.chat.send.useMutation();
  const utils = api.useUtils();

  // Focus the input field when component mounts and after sending messages
  useEffect(() => {
    if (inputRef.current && !loading) {
      inputRef.current.focus();
    }
  }, [loading]);

  // Focus the input field after any message state changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (inputRef.current && !loading) {
        inputRef.current.focus();
      }
    }, 100); // Small delay to ensure DOM updates are complete

    return () => clearTimeout(timer);
  }, [messages.length, loading]);

  // Enhanced focus logic for page load - more robust approach
  useEffect(() => {
    // Immediate focus attempt
    if (inputRef.current) {
      inputRef.current.focus();
    }

    // Focus with a small delay to ensure component is fully mounted
    const immediateTimer = setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 50);

    // Focus with a longer delay as a fallback for slower rendering
    const fallbackTimer = setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 200);

    // Additional focus attempt after a longer delay for edge cases
    const finalTimer = setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 500);

    return () => {
      clearTimeout(immediateTimer);
      clearTimeout(fallbackTimer);
      clearTimeout(finalTimer);
    };
  }, []);

  // Focus when the component becomes enabled (when conversation is selected)
  useEffect(() => {
    if (!disabled && inputRef.current) {
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [disabled]);

  // Focus when conversation changes
  useEffect(() => {
    if (selectedConversationId && inputRef.current && !disabled) {
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [selectedConversationId, disabled]);

  const handleSendMessage = async (
    event: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    try {
      if (event.key === "Enter") {
        await Sentry.startSpan(
          {
            name: "Chat Message [Input]",
            op: "ai.chat",
            attributes: {
              // Initial metrics available at request time
              "input.char_count": message.length,
              "input.language": "en",
              "input.type": "text",
            },
          },
          async (span) => {
            let timeToFirstToken: number;
            let duration: number;
            if (message) {
              setLoading(true);

              if (selectedConversationId && userDetails) {
                const newMessageId = uuidv4();
                setNewMessage({
                  id: newMessageId,
                  body: message,
                  contentType: "text",
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  createdById: userDetails.id,
                  conversationId: selectedConversationId,
                  orgId: org.data?.id ?? "",
                  seq: messages.length + 1,
                  status: ChatMessageStatus.READY,
                  attachments: [],
                  feedback: null,
                  metadata: {
                    modelParameters: modelParameters,
                  },
                });

                span.setAttribute(
                  "input.char_count",
                  message.length.toString(),
                );
                span.setAttribute("message.id", newMessageId);
                span.setAttribute("conversation.id", selectedConversationId);
                span.setAttribute("user.id", userDetails.id);
                span.setAttribute(
                  "model.parameters",
                  JSON.stringify(modelParameters),
                );

                const startTime = new Date();

                try {
                  send.mutate(
                    {
                      conversationId: selectedConversationId,
                      message: message,
                      messageHistory: messages.map((message) => ({
                        role:
                          message.createdById === userDetails.id
                            ? "user"
                            : "assistant",
                        content: message.body,
                      })),
                      tagIds: tagIds,
                      categoryIds: categoryIds,
                      fundIds: fundIds,
                      modelParameters: modelParameters,
                    },
                    {
                      onSuccess: async (data) => {
                        const messageSeq = messages.length + 1;

                        let buffer = null;

                        let token = 0;
                        try {
                          setInputStatusMessage(
                            statusMap[ChatMessageStatus.GENERATING_ANSWER],
                          );

                          for await (const val of data) {
                            console.log("status", val.debug?.status);

                            if (token === 0) {
                              const now = new Date();
                              timeToFirstToken =
                                now.getTime() - startTime.getTime();
                            }

                            // If we have a previous value in the buffer, process it
                            // (knowing it's not the last element)
                            if (buffer !== null) {
                              setNewMessage({
                                id: buffer.chatMessageId ?? "",
                                status:
                                  val.debug?.status ??
                                  ChatMessageStatus.GENERATING_ANSWER,
                                orgId: org.data?.id ?? "",
                                createdAt: new Date(),
                                updatedAt: new Date(),
                                createdById: buffer.createdById ?? "",
                                conversationId: selectedConversationId,
                                seq: messageSeq,
                                body: buffer.response?.answer ?? "",
                                contentType: "text",
                                attachments: [],
                                feedback: null,
                                metadata: {
                                  modelParameters: modelParameters,
                                  ttft: timeToFirstToken,
                                  traceId: val.response?.traceId,
                                },
                              });
                            }

                            // Store current value in buffer
                            buffer = val;
                            token++;

                            if (
                              val.debug?.status === ChatMessageStatus.READY ||
                              val.debug?.status ===
                                ChatMessageStatus.GENERATING_CITATIONS
                            ) {
                              setInputStatusMessage(
                                statusMap[
                                  val.debug?.status as ChatMessageStatus
                                ],
                              );
                              setLoading(false);
                            }
                          }
                        } catch (err) {
                          console.error(err);
                          setLoading(false);
                        }

                        // Process the last element (if any)
                        if (buffer !== null) {
                          const endTime = new Date();
                          duration = endTime.getTime() - startTime.getTime();
                          console.log("Duration:", duration);

                          setNewMessage({
                            id: buffer.chatMessageId ?? "",
                            status: ChatMessageStatus.READY, // This is the last element
                            orgId: org.data?.id ?? "",
                            createdAt: new Date(),
                            updatedAt: new Date(),
                            createdById: buffer.createdById ?? "",
                            conversationId: selectedConversationId,
                            seq: messageSeq,
                            body: JSON.stringify(buffer.response),
                            contentType: "text",
                            attachments: [],
                            feedback: null,
                            metadata: {
                              modelParameters: modelParameters,
                              duration: duration,
                              ttft: timeToFirstToken,
                              traceId: buffer.response?.traceId ?? "",
                            },
                          });
                          setLoading(false);

                          // Force focus on the input when sending message
                          if (inputRef.current) {
                            console.log("focusing on input");
                            inputRef.current.focus();
                          }

                          // Create a new child span to track the time to first token and duration
                          await Sentry.startSpan(
                            {
                              name: "Chat Message [Summary]",
                              op: "ai.chat",
                            },
                            async (span) => {
                              span.setAttribute("message.id", newMessageId);
                              span.setAttribute(
                                "conversation.id",
                                selectedConversationId,
                              );
                              span.setAttribute("user.id", userDetails.id);

                              span.setAttribute(
                                "ttft",
                                timeToFirstToken.toString(),
                              );
                              span.setAttribute(
                                "duration",
                                duration.toString(),
                              );
                            },
                          );
                        }
                      },

                      onError: (error) => {
                        console.error(error);
                        span.setAttribute("error", error.message);
                        Sentry.captureException(error);

                        setLoading(false);
                      },
                    },
                  );
                } catch (e) {
                  console.error(e);
                  setLoading(false);
                }
              } else {
                // const res = await createConversation(conversationData);

                // router.push(`${paths.dashboard.chat}?id=${res.conversation.id}`);

                onAddRecipients([]);
              }
            }
            setMessage("");

            // Restore focus after clearing the message
            if (inputRef.current) {
              inputRef.current.focus();
            }
          },
        );
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <InputBase
        ref={inputRef}
        name="chat-message"
        id="chat-message-input"
        value={message}
        onKeyUp={handleSendMessage}
        onChange={handleChangeMessage}
        onFocus={(event) => {
          if (!hasBeenFocused) {
            setHasBeenFocused(true);
          }
          handleOnFocus(event);
        }}
        onBlur={(event) => {
          console.log("blur event", event);
          handleOnFocus(event);
        }}
        placeholder={loading ? inputStatusMessage : "Type a message"}
        disabled={loading}
        autoFocus
        startAdornment={
          <IconButton>
            {loading ? <CircularProgress size={20} /> : <></>}
          </IconButton>
        }
        // endAdornment={
        //   <Stack direction="row" sx={{ flexShrink: 0 }}>
        //     <IconButton onClick={handleAttach}>
        //       <Iconify icon="solar:gallery-add-bold" />
        //     </IconButton>
        //     <IconButton onClick={handleAttach}>
        //       <Iconify icon="eva:attach-2-fill" />
        //     </IconButton>
        //     <IconButton>
        //       <Iconify icon="solar:microphone-bold" />
        //     </IconButton>
        //   </Stack>
        // }
        sx={{
          px: 1,
          height: 56,
          flexShrink: 0,
          borderTop: loading
            ? "none"
            : (theme) => `solid 1px ${theme.vars.palette.divider}`,
          animation: loading ? "pulseBorder 1.5s ease-in-out infinite" : "none",
          transition: "all 0.2s ease-in-out",
          "&:focus-within": {
            borderTop: (theme) =>
              `2px solid ${theme.vars.palette.primary.main}`,
            boxShadow: (theme) =>
              `0 0 0 2px ${theme.vars.palette.primary.lighter}`,
            backgroundColor: (theme) => theme.vars.palette.background.paper,
          },
          ...(hasBeenFocused && {
            animation: "focusPulse 0.6s ease-out",
          }),
          "@keyframes pulseBorder": {
            "0%": {
              borderTop: (theme) =>
                `2px solid ${theme.vars.palette.primary.lighter}`,
            },
            "50%": {
              borderTop: (theme) =>
                `2px solid ${theme.vars.palette.primary.main}`,
            },
            "100%": {
              borderTop: (theme) =>
                `2px solid ${theme.vars.palette.primary.lighter}`,
            },
          },
          "@keyframes focusPulse": {
            "0%": {
              transform: "scale(1)",
              boxShadow: (theme) =>
                `0 0 0 0px ${theme.vars.palette.primary.main}`,
            },
            "50%": {
              transform: "scale(1.02)",
              boxShadow: (theme) =>
                `0 0 0 4px ${theme.vars.palette.primary.lighter}`,
            },
            "100%": {
              transform: "scale(1)",
              boxShadow: (theme) =>
                `0 0 0 0px ${theme.vars.palette.primary.main}`,
            },
          },
        }}
      />

      <input type="file" ref={fileRef} style={{ display: "none" }} />
    </>
  );
}
