import { Collapse } from "@mui/material";
import { useState } from "react";
import Select, { type MultiValue } from "react-select";
import { useBoolean } from "src/hooks/use-boolean";
import { api } from "~/trpc/react";
import { CollapseButton } from "./styles";
import { CategoryStatus } from "@prisma/client";
type Props = {
  categoryIds: string[];
  setCategoryIds: (categoryIds: string[]) => void;
};

export function Categories({ categoryIds, setCategoryIds }: Props) {
  const {
    data: categories,
    isLoading,
    error,
  } = api.category.getAllCategories.useQuery({
    status: CategoryStatus.ACTIVE,
  });
  const options =
    categories
      ?.filter((c) => !!c.parentId)
      .filter((c) => c.status === CategoryStatus.ACTIVE)
      .map((category) => ({
        value: category.id,
        label: category.name,
      })) || [];
  const [value, setValue] = useState<string[]>(categoryIds ?? []);
  const collapse = useBoolean(true);

  const handleChange = (
    newValue: MultiValue<{ value: string; label: string }>,
  ) => {
    const selectedCategoryIds: string[] = newValue?.map((v) => v.value) || [];
    setValue(selectedCategoryIds);
    setCategoryIds(selectedCategoryIds);
  };

  return (
    <>
      <CollapseButton selected={collapse.value} onClick={collapse.onToggle}>
        {`Filter by Categories`}
      </CollapseButton>

      <Collapse in={collapse.value}>
        <Select
          value={options.filter((option) => value.includes(option.value))}
          isMulti
          isClearable={false}
          placeholder="Select categories"
          isLoading={isLoading}
          name="categories"
          className="basic-multi-select"
          classNamePrefix="select"
          onChange={handleChange}
          options={options}
          styles={{
            container: (base) => ({
              ...base,
              padding: 2,
              borderRadius: 2,
              marginTop: 2,
            }),
          }}
        />
      </Collapse>
    </>
  );
}
