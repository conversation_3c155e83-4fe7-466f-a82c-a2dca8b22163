"use client";

import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import Table from "@mui/material/Table";
import TableHead from "@mui/material/TableHead";
import TableBody from "@mui/material/TableBody";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";

import { fToNow } from "src/utils/format-time";

import { Iconify } from "src/components/iconify";

import {
  Button,
  CircularProgress,
  ClickAwayListener,
  Divider,
  Fade,
  IconButton,
  Modal,
  Paper,
  Popper,
  TextField,
  Tooltip,
  tooltipClasses,
  TooltipProps,
} from "@mui/material";
import { styled, useTheme } from "@mui/material/styles";
import {
  ChatMessageFeedbackType,
  ChatMessageStatus,
  type User,
} from "@prisma/client";
import PopupState, { bindPopper, bindToggle } from "material-ui-popup-state";
import { useEffect, useMemo, useRef, useState } from "react";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

import { toast } from "sonner";
import { FileThumbnail } from "~/components/file-thumbnail";
import {
  ChatModel,
  type Citation,
  type QuestionContentType,
  type RAGResponseWithCitationsAndSimilarQuestions,
  type ResponseContentType,
} from "~/lib/types";
import {
  type ChatMessageGetType,
  type ChatParticipantGetType,
} from "~/server/api/routers/chat";
import { type SimilaritySearchType } from "~/server/api/routers/question";
import { api } from "~/trpc/react";
import { isValidJSON } from "~/utils/json";
import CitationAccordion from "./CitationAccordion";
import { useMessage } from "./hooks/use-message";
import SimilarQuestionsAccordion from "./SimilarQuestionsAccordion";

import { type JsonObject } from "@prisma/client/runtime/library";
import * as Sentry from "@sentry/react";
import { Document as PDFDocument, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/Page/AnnotationLayer.css";
import "react-pdf/dist/Page/TextLayer.css";
import { Label } from "~/components/label";
import { getRuntime } from "~/lib/runtime";
import { type ModelParameters } from "./ChatModelParametersSelector";
import { AgenticResponse, UsageMetadata } from "~/lib/types-from-pydantic.zod";
import { jsonrepair } from "jsonrepair";
import React from "react";
import { HtmlTooltip } from "~/components/virgil/HtmlTooltip";
import { ViewSharepointDocument } from "~/v2/components/composit/ViewSharepointDocument";
import { copyToClipboardClickHandler } from "~/lib/copyToClipboard";
import { getFilename } from "~/v2/lib/utils";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url,
).toString();

type Props = {
  message: ChatMessageGetType;
  participants: ChatParticipantGetType[];
  onOpenLightbox: (value: string) => void;
  userDetails: User | undefined;
  feedback: boolean;
};

const ViewSourceDocument = ({
  citation,
  open,
  handleClose,
}: {
  citation: Citation;
  open: boolean;
  handleClose: () => void;
}) => {
  const [documentId, setDocumentId] = useState<string | undefined>(undefined);
  const [pdfData, setPdfData] = useState<{ data: Uint8Array } | null>(null);
  const [height, setHeight] = useState(0);
  const [width, setWidth] = useState(0);

  const document = api.document.getDocumentByFilename.useQuery(
    {
      filename: citation.fileName ?? "",
    },
    {
      enabled: open,
    },
  );

  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (document.data) {
      setDocumentId(document.data.id);
    }
  }, [document.data]);

  const { isLoading, data: documentContents } =
    api.document.getDocumentContents.useQuery(
      {
        id: documentId ?? "",
        // pageNumber: citation.metadata?.page_number,
        citation: citation.quote,
      },
      {
        enabled: open,
        retry: false,
      },
    );

  useEffect(() => {
    if (documentContents?.body) {
      setPdfData({ data: new Uint8Array(documentContents?.body) });
    }
  }, [isLoading, open]);

  useEffect(() => {
    if (ref.current?.clientHeight && ref.current?.clientHeight > 0) {
      setHeight(ref.current?.clientHeight ?? 0);
      setWidth(ref.current?.clientWidth ?? 0);
    }
  }, [ref.current?.clientHeight]);

  const style = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "80%",
    height: "90%",
    bgcolor: "background.paper",
    border: "1px solid rgba(0, 0, 0, 0.1)",
    borderRadius: 1,
    boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    p: 4,
  };

  let quote = citation.quote;

  const highlightPattern = (text: string, pattern: string) => {
    const subStr = quote.substring(0, text.length);

    if (text.length > 0 && text !== " " && subStr === text) {
      quote = quote.substring(text.length).trimStart();

      return text.replace(text, (value) => `<mark>${value}</mark>`);
    }

    return text;
  };

  const textRenderer = (textItem: any) => {
    return highlightPattern(textItem.str as string, quote);
  };
  // useCallback(
  //   (textItem: any) => highlightPattern(textItem.str as string, quote),
  //   [quote],
  // );

  const renderPdf = useMemo(() => {
    if (pdfData && height > 0) {
      return (
        <div style={{ width: "100%", height: "100%", overflow: "auto" }}>
          <PDFDocument file={pdfData}>
            <Page
              pageNumber={1}
              height={height}
              width={width}
              customTextRenderer={textRenderer}
            />
          </PDFDocument>
        </div>
      );
    }
  }, [pdfData, height]);

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box sx={style} ref={ref}>
        {isLoading ? (
          <Stack
            direction="row"
            justifyContent="center"
            alignItems="center"
            height="100%"
            gap={2}
          >
            <CircularProgress size={24} />
            <Typography id="modal-modal-title" variant="h6" component="h2">
              Loading document and citation...
            </Typography>
          </Stack>
        ) : documentContents?.body === null ? (
          <Typography id="modal-modal-title" variant="h6" component="h2">
            Unable to find citation in document.
          </Typography>
        ) : documentContents === null ? (
          <Typography id="modal-modal-title" variant="h6" component="h2">
            Error retrieving source document.
          </Typography>
        ) : (
          renderPdf
        )}
      </Box>
    </Modal>
  );
};

const ShowCitation = ({
  citation,
  idx,
}: {
  citation: Citation;
  idx: number;
}) => {
  const theme = useTheme();
  const pageOrAddressText = citation.metadata?.page_name
    ? `sheet ${citation.metadata?.page_name}`
    : citation.metadata?.page_number
      ? `page ${citation.metadata?.page_number}`
      : "";

  const fileName = citation.fileName
    ? getFilename(citation.fileName)
    : undefined;
  const [open, setOpen] = useState(false);
  const handleClose = () => setOpen(false);

  return (
    <Stack
      sx={{
        display: "flex",
        flexDirection: "row",
        gap: 1,
        width: "100%",
      }}
    >
      <FileThumbnail file={fileName!} sx={{ mt: 1, width: 23, height: 23 }} />
      <Stack sx={{ mt: 1, gap: 0, width: "100%" }} key={idx}>
        <Typography variant="caption" sx={{ fontWeight: "600", fontSize: 14 }}>
          {fileName}
        </Typography>

        <Typography
          key={citation.sourceId}
          variant="caption"
          sx={{
            fontSize: 14,
            fontWeight: "light",
            color: theme.palette.text.secondary,
            pt: 2,
          }}
        >
          {citation.quote}
        </Typography>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            variant="caption"
            sx={{
              fontWeight: "light",
              fontSize: 14,
              pt: 2,
              color: theme.palette.text.secondary,
              alignSelf: "center",
            }}
          >
            {pageOrAddressText}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              cursor: "pointer",
              pt: 2,
              fontWeight: "600",
              pr: 2,
            }}
            onClick={() => setOpen(true)}
          >
            View Source
          </Typography>
        </Box>
      </Stack>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        aria-labelledby="preview-modal-title"
        aria-describedby="preview-modal-description"
      >
        <ViewSharepointDocument
          documentId={citation.documentId ?? ""}
          modal={true}
        />
      </Modal>
    </Stack>
  );
};

const ShowQuestion = ({
  question,
  idx,
}: {
  question: SimilaritySearchType[number];
  idx: number;
}) => {
  const theme = useTheme();

  const responseContent = question.response?.responseContents[0]
    ?.content as ResponseContentType;

  const strResponseContent = (() => {
    if (typeof responseContent.text === "object") {
      // Format JSON object into readable text
      return Object.entries(responseContent.text)
        .map(([key, value]) => `**${key}**: ${value}`)
        .join("\n\n");
    }
    return responseContent.text;
  })();

  return (
    <Stack
      sx={{
        display: "flex",
        flexDirection: "row",
        gap: 1,
        width: "100%",
      }}
    >
      <Stack sx={{ mt: 1, gap: 0, width: "100%" }} key={idx}>
        <Typography
          key={question.id}
          variant="caption"
          sx={{
            fontSize: 14,
            fontWeight: "light",
            color: theme.palette.text.secondary,
            pt: 2,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              fontWeight: "600",
              fontSize: 14,
              color: theme.palette.text.primary,
            }}
          >
            {
              (question.questionContents[0]?.content as QuestionContentType)
                .text
            }
          </Typography>
        </Typography>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            variant="caption"
            sx={{
              fontWeight: "light",
              fontSize: 14,
              pt: 2,
              color: theme.palette.text.secondary,
              alignSelf: "center",
            }}
          >
            <Typography
              variant="caption"
              sx={{ fontWeight: "light", fontSize: 14 }}
              className="markdown-body"
            >
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {strResponseContent}
              </ReactMarkdown>
            </Typography>
          </Typography>
        </Box>
      </Stack>
    </Stack>
  );
};

const FeedbackButton = ({
  message,
  type,
  feedbackSubmitted,
  setFeedbackSubmitted,
  activeFeedback,
  setActiveFeedback,
  fullButtonWidth,
}: {
  message: ChatMessageGetType;
  type: ChatMessageFeedbackType;
  feedbackSubmitted: ChatMessageFeedbackType | null;
  activeFeedback: ChatMessageFeedbackType | null;
  setFeedbackSubmitted: (value: ChatMessageFeedbackType | null) => void;
  setActiveFeedback: (value: ChatMessageFeedbackType | null) => void;
  fullButtonWidth: boolean;
}) => {
  const feedback = api.chat.feedback.useMutation();
  const [feedbackText, setFeedbackText] = useState("");
  const [loading, setLoading] = useState(false);

  const ref = useRef<HTMLDivElement>(null);

  return (
    <PopupState variant="popper" popupId="feedback-popup-popper">
      {(popupState) => (
        <ClickAwayListener
          onClickAway={(event: MouseEvent | TouchEvent) => {
            popupState.close();
          }}
        >
          <div>
            <Button
              disabled={
                feedbackSubmitted !== null ||
                (activeFeedback !== null && activeFeedback !== type)
              }
              variant={feedbackSubmitted === type ? "contained" : "outlined"}
              color="primary"
              startIcon={
                <Iconify
                  icon={
                    type == ChatMessageFeedbackType.GOOD
                      ? "tabler:thumb-up-filled"
                      : "tabler:thumb-down-filled"
                  }
                  width={16}
                />
              }
              size="small"
              {...bindToggle(popupState)}
              onClick={(e) => {
                setActiveFeedback(type);
                bindToggle(popupState).onClick(e);
              }}
            >
              {fullButtonWidth ? (
                <>
                  {type == ChatMessageFeedbackType.GOOD
                    ? "Good answer"
                    : "Bad answer"}
                </>
              ) : (
                <></>
              )}
            </Button>
            <Popper
              {...bindPopper(popupState)}
              open={activeFeedback === type}
              transition
              placement="top"
              modifiers={[
                {
                  name: "offset",
                  options: {
                    offset: [0, 10],
                  },
                },
              ]}
              ref={ref}
            >
              {({ TransitionProps }) => (
                <Fade {...TransitionProps} timeout={350}>
                  <Paper
                    sx={{
                      width: 400,
                      gap: 1,
                      boxShadow: "0 0 5px rgba(0, 0, 0, 0.2)",
                    }}
                  >
                    <Stack direction="row" justifyContent="space-between">
                      <Typography sx={{ p: 2 }} variant="h6">
                        Additional Feedback (Optional)
                      </Typography>
                      <IconButton
                        onClick={() => {
                          setActiveFeedback(null);
                          popupState.close();
                        }}
                      >
                        <Iconify
                          icon="material-symbols:close-small"
                          width={24}
                          height={24}
                          color="text.secondary"
                        />
                      </IconButton>
                    </Stack>
                    <TextField
                      value={feedbackText}
                      onChange={(e) => setFeedbackText(e.target.value)}
                      sx={{ p: 2 }}
                      fullWidth
                      multiline
                      rows={4}
                      placeholder={
                        type == ChatMessageFeedbackType.GOOD
                          ? "How did the answer help you?"
                          : "What was wrong with the answer?"
                      }
                    />
                    <Stack
                      direction="row"
                      justifyContent="flex-end"
                      sx={{ p: 2 }}
                    >
                      <Button
                        variant="contained"
                        color="primary"
                        disabled={feedbackText.length === 0}
                        loading={loading}
                        onClick={() => {
                          setLoading(true);
                          feedback.mutate(
                            {
                              chatMessageId: message.id,
                              feedback: feedbackText,
                              type,
                              traceId: (message.metadata as JsonObject)
                                ?.traceId as string,
                            },
                            {
                              onSuccess: async () => {
                                await Sentry.startSpan(
                                  {
                                    name: "Chat Message [Feedback]",
                                    op: "ai.chat",
                                  },
                                  async (span) => {
                                    span.setAttribute("message.id", message.id);

                                    span.setAttribute(
                                      "user.id",
                                      message.createdById,
                                    );

                                    span.setAttribute("type", type);
                                    span.setAttribute("feedback", feedbackText);
                                  },
                                );

                                setActiveFeedback(null);
                                popupState.close();
                                setLoading(false);
                                setFeedbackSubmitted(type);
                              },
                              onError: () => {
                                setLoading(false);
                              },
                            },
                          );
                        }}
                      >
                        Submit
                      </Button>
                    </Stack>
                  </Paper>
                </Fade>
              )}
            </Popper>
          </div>
        </ClickAwayListener>
      )}
    </PopupState>
  );
};

const AgenticResponseDebugTooltip = ({
  agenticResponseDebug,
}: {
  agenticResponseDebug: AgenticResponse;
}) => {
  return (
    <React.Fragment>
      <div
        style={{
          maxHeight: "350px",
          overflowY: "auto",
          backgroundColor: "white",
          padding: "10px",
          borderRadius: "5px",
          border: "1px solid #e0e0e0",
          color: "black",
        }}
      >
        <Table size="small" stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell sx={{ backgroundColor: "background.paper" }}>
                Step
              </TableCell>
              <TableCell sx={{ backgroundColor: "background.paper" }}>
                Input Tokens
              </TableCell>
              <TableCell sx={{ backgroundColor: "background.paper" }}>
                Output Tokens
              </TableCell>
              <TableCell sx={{ backgroundColor: "background.paper" }}>
                Total Tokens
              </TableCell>
              <TableCell sx={{ backgroundColor: "background.paper" }}>
                Cache Creation
              </TableCell>
              <TableCell sx={{ backgroundColor: "background.paper" }}>
                Cache Read
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {agenticResponseDebug.details?.map((detail, index) => {
              const usageMetadata = detail.usage_metadata
                ? (JSON.parse(
                    jsonrepair(detail.usage_metadata),
                  ) as UsageMetadata)
                : null;
              if (!usageMetadata) return null;
              return (
                <TableRow
                  key={index}
                  sx={{
                    "&:nth-of-type(even)": {
                      backgroundColor: "action.hover",
                    },
                  }}
                >
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{usageMetadata.input_tokens}</TableCell>
                  <TableCell>{usageMetadata.output_tokens}</TableCell>
                  <TableCell>{usageMetadata.total_tokens}</TableCell>
                  <TableCell>
                    {usageMetadata.input_token_details.cache_creation}
                  </TableCell>
                  <TableCell>
                    {usageMetadata.input_token_details.cache_read}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </React.Fragment>
  );
};

export function ChatMessageItem({
  message,
  participants,
  onOpenLightbox,
  userDetails,
  feedback,
}: Props) {
  const { me, senderDetails, hasImage } = useMessage({
    message,
    participants,
    currentUserId: `${userDetails?.id}`,
  });

  const [feedbackSubmitted, setFeedbackSubmitted] =
    useState<ChatMessageFeedbackType | null>(null);
  const [activeFeedback, setActiveFeedback] =
    useState<ChatMessageFeedbackType | null>(null);

  const { firstName, avatarUrl } = senderDetails;

  const { body, createdAt } = message;

  const ref = useRef<HTMLDivElement>(null);
  const [fullButtonWidth, setFullButtonWidth] = useState(true);

  useEffect(() => {
    if (!ref.current) return;
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setFullButtonWidth(entry.contentRect.width > 590);
      }
    });

    resizeObserver.observe(ref.current);
  }, [ref.current]);

  // If the body is a valid JSON, it means it was generated by the RAG
  // Otherwise, it's just a plain text input from the user
  // @ts-ignore
  const { answer, citations, similarQuestions, status, agenticResponseDebug } =
    isValidJSON(body)
      ? (JSON.parse(body) as RAGResponseWithCitationsAndSimilarQuestions)
      : {
          answer: body,
          citations: [],
          similarQuestions: [],
          agenticResponseDebug: null,
        };

  const metadata = message.metadata as JsonObject;
  const modelParameters =
    metadata && metadata.modelParameters !== undefined
      ? (metadata.modelParameters as ModelParameters)
      : {
          model: ChatModel.GEMINI_25_FLASH,
          temperature: 0,
          topP: 1,
          thinking_mode: false,
        };

  const { model, temperature, topP, thinking_mode } = modelParameters;

  const duration =
    metadata && metadata.duration !== undefined
      ? (metadata.duration as number)
      : 0;

  const ttft =
    metadata && metadata.ttft !== undefined ? (metadata.ttft as number) : 0;

  const renderInfo = (
    <Typography
      noWrap
      variant="caption"
      sx={{ mb: 1, color: "text.disabled", ...(!me && { mr: "auto" }) }}
    >
      {`${firstName}, `}

      {fToNow(createdAt)}
    </Typography>
  );

  const renderStatus = (
    <Typography
      noWrap
      variant="body2"
      sx={{ mb: 1, color: "text.disabled", ...(!me && { mr: "auto" }) }}
    >
      {status}
    </Typography>
  );

  const renderBody = (
    <Stack
      sx={{
        p: 1.5,
        minWidth: 48,
        maxWidth: "90%",
        borderRadius: 1,
        typography: "body2",
        bgcolor: "background.neutral",
        ...(me && { color: "grey.800", bgcolor: "primary.lighter" }),
        ...(hasImage && { p: 0, bgcolor: "transparent" }),
        animation: "fadeIn 1s ease-in-out",
        boxShadow: "0 0 5px rgba(0, 0, 0, 0.2)",
      }}
    >
      {hasImage ? (
        <Box
          component="img"
          alt="attachment"
          src={answer}
          onClick={() => onOpenLightbox(answer ?? "")}
          sx={{
            width: 400,
            height: "auto",
            borderRadius: 1.5,
            cursor: "pointer",
            objectFit: "cover",
            aspectRatio: "16/11",
            "&:hover": { opacity: 0.9 },
          }}
        />
      ) : (
        <div className="markdown-body">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
          >
            {answer}
          </ReactMarkdown>
          {status === ChatMessageStatus.GENERATING_CITATIONS && (
            <Typography variant="caption" sx={{ color: "text.secondary" }}>
              Generating citations...
            </Typography>
          )}
          {citations && citations.length > 0 && !me && (
            <>
              <CitationAccordion title="View Citations">
                {citations.map((citation, idx) => {
                  return (
                    <ShowCitation key={idx} citation={citation} idx={idx} />
                  );
                })}
              </CitationAccordion>
            </>
          )}
          {similarQuestions && similarQuestions.length > 0 && (
            <>
              <Divider sx={{ width: "100%", opacity: 0.1, height: "0.5px" }} />

              <SimilarQuestionsAccordion>
                {similarQuestions.map((question, idx) => {
                  return (
                    <ShowQuestion key={idx} question={question} idx={idx} />
                  );
                })}
              </SimilarQuestionsAccordion>
            </>
          )}
          {getRuntime() === "dev" && !me && (
            <Stack direction="row" gap={1}>
              <Label variant="outlined" color="primary">
                {model}
              </Label>
              <Label
                variant="outlined"
                color="secondary"
                sx={{
                  gap: 0.2,
                }}
              >
                <Iconify icon="oui:temperature" width={14} />
                {temperature}
              </Label>
              {ttft > 0 && (
                <Label
                  variant="soft"
                  color="secondary"
                  sx={{
                    gap: 0.2,
                  }}
                  width={100}
                >
                  <Iconify icon="ic:outline-token" width={14} />
                  {ttft / 1000}s
                </Label>
              )}
              {duration > 0 && (
                <Label
                  variant="filled"
                  color="default"
                  sx={{
                    gap: 0.2,
                    width: 100,
                  }}
                >
                  <Iconify icon="ri:time-line" width={14} />
                  {duration / 1000}s
                </Label>
              )}
              {agenticResponseDebug && (
                <HtmlTooltip
                  title={
                    <AgenticResponseDebugTooltip
                      agenticResponseDebug={agenticResponseDebug}
                    />
                  }
                >
                  <Label variant="soft" color="warning">
                    Agentic
                  </Label>
                </HtmlTooltip>
              )}
            </Stack>
          )}
        </div>
      )}
    </Stack>
  );

  const renderActions = (
    <Stack
      direction="row"
      className="message-actions"
      sx={{
        pt: 1.5,
        left: 0,
        // opacity: 0,
        top: "100%",
        width: "100%",
        position: "absolute",
        transition: (theme) =>
          theme.transitions.create(["opacity"], {
            duration: theme.transitions.duration.shorter,
          }),
        ...(me && { right: 0, left: "unset" }),
      }}
    >
      {!me && feedback && message.status == ChatMessageStatus.READY && (
        <Stack direction="column" gap={1} sx={{ mb: 1 }}>
          <Stack direction="row" gap={1} sx={{ alignItems: "center" }}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<Iconify icon="solar:copy-bold" width={16} />}
              size="small"
              onClick={copyToClipboardClickHandler(
                answer ?? "",
                2000,
                true,
                "Copied answer to clipboard",
              )}
            >
              {fullButtonWidth ? "Copy answer" : ""}
            </Button>
            <FeedbackButton
              message={message}
              type={ChatMessageFeedbackType.GOOD}
              feedbackSubmitted={feedbackSubmitted}
              setFeedbackSubmitted={setFeedbackSubmitted}
              activeFeedback={activeFeedback}
              setActiveFeedback={setActiveFeedback}
              fullButtonWidth={fullButtonWidth}
            />
            <FeedbackButton
              message={message}
              type={ChatMessageFeedbackType.BAD}
              feedbackSubmitted={feedbackSubmitted}
              setFeedbackSubmitted={setFeedbackSubmitted}
              activeFeedback={activeFeedback}
              setActiveFeedback={setActiveFeedback}
              fullButtonWidth={fullButtonWidth}
            />
            <Typography variant="caption" sx={{ color: "text.secondary" }}>
              All feedback is reviewed by the team.
            </Typography>
          </Stack>
        </Stack>
      )}
    </Stack>
  );

  return (
    <Stack
      direction="row"
      display="flex"
      justifyContent={me ? "flex-end" : "unset"}
      sx={{ mb: 5 }}
      ref={ref}
    >
      <Stack
        direction="column"
        display="flex"
        alignItems={me ? "flex-end" : "flex-start"}
        sx={{
          width: "100%",
          justifyContent: "center",
        }}
      >
        {renderInfo}

        {status ? (
          renderStatus
        ) : (
          <Stack
            sx={{
              position: "relative",
              width: "100%",
            }}
          >
            <Stack
              direction="row"
              alignItems="center"
              justifyContent={me ? "flex-end" : "unset"}
              sx={{
                position: "relative",
                width: "100%",
              }}
            >
              {renderBody}
            </Stack>
            {message.status == ChatMessageStatus.READY && renderActions}
          </Stack>
        )}
      </Stack>
    </Stack>
  );
}
