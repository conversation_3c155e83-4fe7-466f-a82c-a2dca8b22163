"use client";

import { m } from "framer-motion";
import { useEffect, useState } from "react";
import { cn } from "~/v2/lib/utils";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";
import { LogoMini } from "~/v2/components/icons";

export const ThinkingMessage = () => {
  const [dotCount, setDotCount] = useState(0);
  const thinkingStates = ["Thinking", "Thinking.", "Thinking..", "Thinking..."];

  useEffect(() => {
    const interval = setInterval(() => {
      setDotCount((prev) => (prev + 1) % thinkingStates.length);
    }, 500); // Change every 500ms for a nice rhythm

    return () => clearInterval(interval);
  }, [thinkingStates.length]);

  return (
    <m.div
      data-testid="message-assistant-loading"
      className={cn(overrideMuiTheme, "w-full mx-auto max-w-3xl px-4 group/message")}
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
    >
      <div className="flex gap-4 w-full">
        <div className="size-8 flex items-center rounded-full justify-center shrink-0 bg-background">
          <div className="translate-y-px">
            <LogoMini />
          </div>
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            <span className="font-mono">
              {thinkingStates[dotCount]}
            </span>
          </div>
        </div>
      </div>
    </m.div>
  );
};
