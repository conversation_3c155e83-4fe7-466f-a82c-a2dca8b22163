"use client";

import { AnimatePresence, m } from "framer-motion";
import { memo, useState, useMemo, useEffect, useRef } from "react";
import { type User } from "@prisma/client";
import { type ChatMessageGetType } from "~/server/api/routers/chat";
import { Markdown } from "~/components/markdown";

import { cn } from "~/v2/lib/utils";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";
import equal from "fast-deep-equal";
import { MessageActions } from "./message-actions";
import { LogoMini } from "~/v2/components/icons";
import { AccordionRoot, AccordionItem, AccordionTrigger, AccordionContent } from "~/v2/components/ui/Accordion/Accordion";
import { fileThumb, fileNameByUrl } from "~/components/file-thumbnail/utils";
import Image from "next/image";
import { Dialog, DialogContent } from "~/v2/components/ui/Dialog";
import { Button } from "~/v2/components/ui/Button";
import { api } from "~/trpc/react";
import { DocumentSource } from "@prisma/client";
import { ViewSharepointDocument } from "~/v2/components/composit/ViewSharepointDocument";

interface Citation {
  sourceId?: number;
  documentId?: string;
  fileName?: string;
  quote?: string;
  source?: string;
  title?: string;
  namedEntityMentioned?: boolean;
  metadata?: {
    page_number?: number;
    page_name?: string;
    [key: string]: unknown;
  };
}

interface MessageProps {
  message: ChatMessageGetType;
  isLoading: boolean;
  userDetails: User | undefined;
  onContentUpdate?: () => void;
}

// Helper function to get file type icon
function getFileTypeIcon(fileName: string) {
  try {
    return fileThumb(fileName);
  } catch {
    return fileThumb(""); // fallback to default file icon
  }
}

// Simplified document viewer that uses the shared ViewSharepointDocument component
function DocumentViewer({
  citation,
  open,
  handleClose,
}: {
  citation: Citation;
  open: boolean;
  handleClose: () => void;
}) {
  const [documentId, setDocumentId] = useState<string | undefined>(undefined);

  const document = api.document.getDocumentByFilename.useQuery(
    {
      filename: citation.fileName ?? "",
    },
    {
      enabled: open,
    },
  );

  useEffect(() => {
    if (document.data) {
      setDocumentId(document.data.id);
    }
  }, [document.data]);

  if (!document.data) {
    return null;
  }

  // For Sharepoint documents, use the shared component
  if (document.data.source === DocumentSource.SHAREPOINT && documentId) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="w-[80%] h-[90%] max-w-none max-h-none !max-w-[80%] !max-h-[90%]">
          <ViewSharepointDocument documentId={documentId} modal={true} />
        </DialogContent>
      </Dialog>
    );
  }

  // For non-Sharepoint documents, show appropriate message
  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-[80%] h-[90%] max-w-none max-h-none !max-w-[80%] !max-h-[90%]">
        <div className="flex flex-col items-center justify-center h-full p-8">
          <div className="flex flex-col items-center space-y-6">
            <h2 className="text-xl font-semibold text-foreground">
              {document.data.name}
            </h2>
            <p className="text-sm text-muted-foreground text-center">
              Document preview is not available for this document type and source.
            </p>
            <p className="text-xs text-muted-foreground/60 text-center">
              Source: {document.data.source}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Citation item component with modal functionality
function CitationItem({ citation, index }: { citation: Citation; index: number }) {
  const [open, setOpen] = useState(false);
  
  const fileName = citation.fileName ?? citation.source ?? `Source ${index + 1}`;
  const displayName = fileNameByUrl(fileName);
  const fileIconUrl = getFileTypeIcon(fileName);
  
  // Extract page information from metadata
  const pageOrAddressText = citation.metadata?.page_name
    ? `sheet ${citation.metadata.page_name}`
    : citation.metadata?.page_number
      ? `page ${citation.metadata.page_number}`
      : "";
  
  return (
    <>
      <div className="flex gap-3 p-3">
        <div className="flex-shrink-0">
          <Image 
            src={fileIconUrl} 
            alt="File type" 
            width={24}
            height={24}
            className="w-6 h-6" 
          />
        </div>
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm text-foreground mb-1">
            {displayName}
          </div>
          {citation.quote && (
            <div className="text-xs text-muted-foreground bg-background/50 p-2 rounded border-l-2 border-primary/20 mb-2">
              {citation.quote}
            </div>
          )}
          <div className="flex items-center gap-3">
            {pageOrAddressText && (
              <span className="text-xs text-muted-foreground">
                {pageOrAddressText}
              </span>
            )}
            {citation.documentId && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs h-auto p-1 text-primary hover:bg-primary/10"
                onClick={() => setOpen(true)}
              >
                View Source
              </Button>
            )}
          </div>
        </div>
      </div>
      
      {citation.documentId && (
        <DocumentViewer
          citation={citation}
          open={open}
          handleClose={() => setOpen(false)}
        />
      )}
    </>
  );
}

// Custom hook for typewriter effect
function useTypewriter(text: string, shouldStart: boolean, speed: number = 7, onUpdate?: () => void) {
  const [displayedText, setDisplayedText] = useState("");
  const [isComplete, setIsComplete] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const prevTextRef = useRef(text);
  const isInitialRender = useRef(true);

  useEffect(() => {
    // On initial render, if we shouldn't start typewriter, show full text immediately
    if (isInitialRender.current && !shouldStart) {
      setDisplayedText(text);
      setIsComplete(true);
      isInitialRender.current = false;
      return;
    }
    
    isInitialRender.current = false;

    // Start typewriter if requested and not already started
    if (shouldStart && !hasStarted) {
      setHasStarted(true);
      setDisplayedText("");
      setIsComplete(false);
      prevTextRef.current = text;
    }

    // If never started, show full text immediately
    if (!hasStarted && !shouldStart) {
      setDisplayedText(text);
      setIsComplete(true);
      return;
    }

    // If text changed (new chunk) and we're typing, continue from current position
    if (prevTextRef.current !== text && hasStarted) {
      if (text.length > prevTextRef.current.length && displayedText.length > 0) {
        // Text was appended, continue typing from current position
        prevTextRef.current = text;
      } else {
        // Completely new text, restart
        setDisplayedText("");
        setIsComplete(false);
        prevTextRef.current = text;
      }
    }

    // If we've finished typing, mark as complete
    if (displayedText.length >= text.length) {
      setIsComplete(true);
      return;
    }

    // Continue typing if we've started (regardless of shouldStart value)
    if (hasStarted && !isComplete) {
      const timer = setTimeout(() => {
        setDisplayedText(text.slice(0, displayedText.length + 1));
        onUpdate?.();
      }, speed);

      return () => clearTimeout(timer);
    }
  }, [text, displayedText, shouldStart, hasStarted, isComplete, speed, onUpdate]);

  return { displayedText, isComplete };
}

function PureMessage({ message, isLoading, userDetails, onContentUpdate }: MessageProps) {
  // const [mode, setMode] = useState<'view' | 'edit'>('view');

  const isUserMessage = message.createdById === userDetails?.id;
  
  // Parse assistant message content from JSON if it exists
  const fullMessageContent = useMemo(() => {
    if (isUserMessage) {
      return message.body;
    }

    try {
      const parsed = JSON.parse(message.body) as { answer?: string };
      return parsed.answer ?? message.body;
    } catch {
      return message.body;
    }
  }, [message.body, isUserMessage]);

  // Start typewriter for assistant messages when they begin loading
  // Only start typewriter for new streaming messages, not existing ones
  const shouldStartTypewriter = !isUserMessage && isLoading;
  const { displayedText, isComplete } = useTypewriter(fullMessageContent, shouldStartTypewriter, 4, onContentUpdate);
  
  // Use typewriter text if it has started, otherwise show full content
  const messageContent = !isUserMessage && !isComplete ? displayedText : fullMessageContent;

  // Get citations if available
  const citations = useMemo((): Citation[] => {
    if (isUserMessage) return [];
    
    try {
      const parsed = JSON.parse(message.body) as { citations?: Citation[] };
      return parsed.citations ?? [];
    } catch {
      return [];
    }
  }, [message.body, isUserMessage]);

  return (
    <AnimatePresence>
      <m.div
        data-testid={`message-${isUserMessage ? 'user' : 'assistant'}`}
        className={cn(overrideMuiTheme, "w-full mx-auto max-w-3xl px-4 group/message")}
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={isUserMessage ? 'user' : 'assistant'}
      >
        <div
          className={cn(
            'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
            // {
            //   'w-full': mode === 'edit',
            //   'group-data-[role=user]/message:w-fit': mode !== 'edit',
            // },
          )}
        >
          {!isUserMessage && (
            <div className="size-8 flex items-center rounded-full justify-center shrink-0 bg-background">
              <div className="translate-y-px">
                <LogoMini />
              </div>
            </div>
          )}

          <div className="flex flex-col gap-4 w-full">
            <div className="flex flex-row gap-2 items-start">
              {/* {isUserMessage && mode === 'view' && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      data-testid="message-edit-button"
                      variant="ghost"
                      size="sm"
                      className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                      onClick={() => {
                        setMode('edit');
                      }}
                    >
                      <Edit />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Edit message</TooltipContent>
                </Tooltip>
              )} */}

              <div
                data-testid="message-content"
                className={cn('flex flex-col gap-4', {
                  'bg-primary text-primary-foreground px-3 py-2 rounded-xl':
                    isUserMessage,
                })}
              >
                <Markdown>{messageContent}</Markdown>
                
                {citations.length > 0 && (
                  <div className="mt-4">
                    <AccordionRoot type="single" className="p-0" collapsible>
                      <AccordionItem value="sources">
                        <AccordionTrigger className="p-4 hover:no-underline">
                          {citations.length === 1 ? "View Source" : `View Sources (${citations.length})`}
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="divide-y divide-border/50 [&>*:first-child]:pt-0">
                            {citations.map((citation: Citation, index: number) => (
                              <CitationItem 
                                key={citation.sourceId ?? index} 
                                citation={citation} 
                                index={index} 
                              />
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </AccordionRoot>
                  </div>
                )}
              </div>
            </div>

            {/* Message Actions for Assistant Messages */}
            {!isUserMessage && (
              <MessageActions
                message={message}
                userDetails={userDetails}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      </m.div>
    </AnimatePresence>
  );
}

export const Message = memo(
  PureMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message, nextProps.message)) return false;
    if (prevProps.onContentUpdate !== nextProps.onContentUpdate) return false;

    return false;
  },
);
