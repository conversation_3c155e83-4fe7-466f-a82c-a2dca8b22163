"use client";

import { memo, useRef, useEffect, useState } from "react";
import { m } from "framer-motion";
import equal from "fast-deep-equal";
import { type User } from "@prisma/client";
import { type ChatMessageGetType } from "~/server/api/routers/chat";
import { Message } from "./message";
import { Greeting } from "./greeting";
import { ThinkingMessage } from "./thinking-message";
import { cn } from "~/v2/lib/utils";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";

interface MessagesProps {
  selectedConversationId: string;
  messages: ChatMessageGetType[];
  status: 'streaming' | 'ready';
  isLoading: boolean;
  userDetails: User | undefined;
}

function PureMessages({
  selectedConversationId: _selectedConversationId,
  messages,
  status,
  isLoading,
  userDetails,
}: MessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const userScrolledUp = useRef(false);
  const prevScrollTop = useRef(0);

  const scrollToBottom = () => {
    if (!userScrolledUp.current && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Handle scroll to detect if user manually scrolled up
  const handleScroll = () => {
    if (!scrollContainerRef.current) return;
    
    const currentScrollTop = scrollContainerRef.current.scrollTop;
    const scrolledUp = currentScrollTop < prevScrollTop.current;
    
    if (scrolledUp) {
      userScrolledUp.current = true;
    }
    
    // Check if user scrolled back to bottom to re-enable auto-scroll
    const { scrollHeight, clientHeight } = scrollContainerRef.current;
    const atBottom = scrollHeight - currentScrollTop - clientHeight < 10;
    
    if (atBottom) {
      userScrolledUp.current = false;
    }
    
    prevScrollTop.current = currentScrollTop;
  };

  // Scroll to bottom when messages change or when streaming
  useEffect(() => {
    scrollToBottom();
  }, [messages, status]);

  // Scroll to bottom on initial load
  useEffect(() => {
    if (messages.length > 0 && !isLoading) {
      // Add a small delay to ensure DOM content is fully rendered
      const timeoutId = setTimeout(() => {
        scrollToBottom();
        // Double-check after a bit more time in case content is still shifting
        setTimeout(() => {
          scrollToBottom();
        }, 50);
      }, 150);
      
      return () => clearTimeout(timeoutId);
    }
  }, [isLoading, messages.length]);

  // Add scroll listener
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    scrollContainer.addEventListener('scroll', handleScroll);
    return () => scrollContainer.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div 
      ref={scrollContainerRef}
      className={cn(overrideMuiTheme, "flex flex-col min-w-0 gap-6 flex-1 overflow-y-scroll pt-4 relative")}
    >
      {messages.length === 0 && !isLoading && (
        <div className="flex-1 flex items-center justify-center">
          <Greeting />
        </div>
      )}

      {messages.map((message, index) => (
        <Message
          key={message.id}
          message={message}
          isLoading={status === 'streaming' && messages.length - 1 === index}
          userDetails={userDetails}
          onContentUpdate={scrollToBottom}
        />
      ))}

      {status === 'streaming' &&
        messages.length > 0 &&
        messages[messages.length - 1]?.createdById === userDetails?.id && (
          <ThinkingMessage />
        )}

      <m.div className="shrink-0 min-w-[24px] min-h-[24px]" />
      <div ref={messagesEndRef} />
    </div>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.status !== nextProps.status) return false;
  if (prevProps.isLoading !== nextProps.isLoading) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.messages, nextProps.messages)) return false;

  return false;
});
