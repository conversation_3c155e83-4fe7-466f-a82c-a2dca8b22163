"use client";

import { useUser } from "@clerk/nextjs";
import { useCallback, useEffect, useState, useRef } from "react";
import { api } from "~/trpc/react";
import { 
  type ChatMessageGetType,
  type ConversationGetType,
} from "~/server/api/routers/chat";
import { ChatModel, ResponseStyle } from "~/lib/types";
import { env } from "~/env";
import { type ModelParameters } from "~/views/chat/ChatModelParametersSelector";
import { ChatMessageStatus } from "@prisma/client";
import { Messages } from "../messages";
import { MultimodalInput } from "../multimodal-input";
import { ChatHeader } from "../chat-header";
import { SuggestedInputs } from "../suggested-inputs";
import Loading from "~/app/dashboard/loading";
import { cn } from "~/v2/lib/utils";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";

// ----------------------------------------------------------------------
type ChatV2ViewProps = {
  initialTagIds?: string[];
  initialFundIds?: string[];
  initialCategoryIds?: string[];
};

export function ChatV2View({
  initialTagIds,
  initialFundIds,
  initialCategoryIds,
}: ChatV2ViewProps) {
  const { user } = useUser();
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const [selectedConversationId, setSelectedConversationId] = useState<string>("");
  const [messages, setMessages] = useState<ChatMessageGetType[]>([]);
  const [input, setInput] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [justCleared, setJustCleared] = useState<boolean>(false);
  const [tagIds, setTagIds] = useState<string[]>(initialTagIds ?? []);
  const [categoryIds, setCategoryIds] = useState<string[]>(
    initialCategoryIds ?? [],
  );
  const [fundIds, setFundIds] = useState<string[]>(initialFundIds ?? []);

  const [modelParameters, setModelParameters] = useState<ModelParameters>({
    model: ChatModel.CLAUDE_40_LATEST,
    responseStyle: ResponseStyle.NORMAL,
    temperature: 0,
    topP: 0,
    thinking_mode: false,
    citation_verification_mode: false,
    answer_reflexion_mode: false,
    agentic_mode_langgraph: env.NEXT_PUBLIC_AGENTIC_MODE_LANGGRAPH === "true",
  });

  const { data: conversations, isLoading: conversationsLoading } =
    api.chat.getAll.useQuery();

  const { data: conversation, isLoading: conversationLoading } =
    api.chat.get.useQuery(
      {
        conversationId: selectedConversationId,
      },
      {
        enabled: !!selectedConversationId,
      },
    );

  const { data: userDetails } = api.user.get.useQuery();
  const utils = api.useUtils();
  const clearHistory = api.chat.clear.useMutation();

  const handleClearChatHistory = useCallback(() => {
    setMessages([]);
    setJustCleared(true);
    clearHistory.mutate(
      { conversationId: selectedConversationId },
      {
        onSuccess: async () => {
          // Invalidate queries to refetch updated data
          await utils.chat.getAll.invalidate();
          await utils.chat.get.invalidate({ conversationId: selectedConversationId });
        },
      },
    );
  }, [selectedConversationId, clearHistory, utils.chat.getAll, utils.chat.get]);

  // Set messages when conversation loads
  useEffect(() => {
    if (conversation && messages.length === 0 && !justCleared) {
      setMessages(
        conversation.messages.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
        ) ?? [],
      );
    }
    // Reset justCleared flag after conversation data is refreshed
    if (justCleared && conversation && conversation.messages.length === 0) {
      setJustCleared(false);
    }
  }, [conversation, messages.length, justCleared]);

  // Set selected conversation to first available
  useEffect(() => {
    if (conversations && conversations[0] && !selectedConversationId) {
      setSelectedConversationId(conversations[0].id);
    }
  }, [conversations, selectedConversationId]);

  // Handler for sending suggested messages
  const handleSendSuggestedMessage = useCallback((message: string) => {
    if (!selectedConversationId || loading) return;
    
    // Set the input value
    setInput(message);
    
    // Trigger form submission after input is set
    setTimeout(() => {
      if (inputRef.current) {
        // Simulate Enter key press to trigger form submission
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          bubbles: true,
          cancelable: true
        });
        inputRef.current.dispatchEvent(enterEvent);
      }
    }, 0);
  }, [selectedConversationId, loading]);

  if (conversationsLoading) {
    return <Loading />;
  }

  const status = loading ? 'streaming' : 'ready' as const;

  return (
    <div className={cn(overrideMuiTheme, "flex flex-col min-w-0 h-dvh bg-background")}>
      <ChatHeader
        selectedConversationId={selectedConversationId}
        conversations={conversations as ConversationGetType[]}
        setSelectedConversationId={setSelectedConversationId}
        onClearHistory={handleClearChatHistory}
      />

      <Messages
        selectedConversationId={selectedConversationId}
        messages={messages}
        status={status}
        isLoading={conversationLoading}
        userDetails={userDetails}
      />

      <SuggestedInputs
        onSendMessage={handleSendSuggestedMessage}
        isVisible={messages.length === 0 && !conversationLoading}
      />

      <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
        <MultimodalInput
          selectedConversationId={selectedConversationId}
          input={input}
          setInput={setInput}
          status={status}
          messages={messages}
          setMessages={setMessages}
          userDetails={userDetails}
          tagIds={tagIds}
          categoryIds={categoryIds}
          fundIds={fundIds}
          modelParameters={modelParameters}
          setLoading={setLoading}
          ref={inputRef}
        />
      </form>
    </div>
  );
}