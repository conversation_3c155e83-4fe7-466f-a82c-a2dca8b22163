"use client";

import { cn } from "~/v2/lib/utils";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";

interface SuggestedInputsProps {
  onSendMessage: (message: string) => void;
  isVisible: boolean;
}

const suggestedInputs = [
  {
    title: "Create talking points",
    subtitle: "about our recent fund performance."
  },
  {
    title: "Draft our investment strategy overview",
    subtitle: "for new investors."
  },
  {
    title: "What questions should I expect",
    subtitle: "about our track record?"
  },
  {
    title: "Summarize key terms",
    subtitle: "from our partnership agreements."
  }
];

export function SuggestedInputs({ onSendMessage, isVisible }: SuggestedInputsProps) {
  if (!isVisible) return null;

  return (
    <div className={cn(overrideMuiTheme, "w-full max-w-3xl mx-auto px-4 mb-4")}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {suggestedInputs.map((suggestion, index) => (
          <button
            key={index}
            onClick={() => onSendMessage(`${suggestion.title} ${suggestion.subtitle}`)}
            className="flex flex-col items-start p-4 rounded-xl border border-border bg-card hover:bg-accent hover:text-accent-foreground transition-colors text-left group cursor-pointer"
          >
            <div className="text-sm font-medium text-foreground">
              {suggestion.title}
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              {suggestion.subtitle}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}
