"use client";

import { cn } from "~/v2/lib/utils";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";
import { useUser } from "@clerk/clerk-react";

function getTimeBasedGreeting(): string {
  const hour = new Date().getHours();
  
  if (hour >= 5 && hour < 12) {
    return "Good morning";
  } else if (hour >= 12 && hour < 17) {
    return "Good afternoon";
  } else {
    return "Good evening";
  }
}

export function Greeting() {
  const user = useUser();
  const greeting = getTimeBasedGreeting();
  
  return (
    <div className={cn(overrideMuiTheme, "mx-auto max-w-3xl px-4")}>
      <div className="p-8">
        <h1 className="text-2xl font-semibold mb-2">
          {greeting}, {user.user?.firstName}.
        </h1>
        <p className="text-muted-foreground">
          How can I help you today?
        </p>
      </div>
    </div>
  );
}
