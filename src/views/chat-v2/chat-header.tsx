"use client";

import { memo } from "react";
import { But<PERSON> } from "~/v2/components/ui/Button";
import { Plus } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/v2/components/ui/Tooltip";
import { type ConversationGetType } from "~/server/api/routers/chat";
import { cn } from "~/v2/lib/utils";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";

interface ChatHeaderProps {
  selectedConversationId: string;
  conversations: ConversationGetType[];
  setSelectedConversationId: (id: string) => void;
  onClearHistory: () => void;
}

function PureChatHeader({
  selectedConversationId,
  conversations,
  setSelectedConversationId,
  onClearHistory,
}: ChatHeaderProps) {
  return (
    <header className={cn(overrideMuiTheme, "flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 border-b")}>
      {/* <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            className="px-2 h-fit"
            onClick={() => {
              // TODO: Create new conversation
              console.log('Create new conversation');
            }}
          >
            <Plus />
            <span className="sr-only">New Chat</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>New Chat</TooltipContent>
      </Tooltip> */}

      {conversations.length > 1 && (
        <select
          value={selectedConversationId}
          onChange={(e) => setSelectedConversationId(e.target.value)}
          className="px-3 py-1 rounded-md border bg-background"
        >
          {conversations.map((conversation) => (
            <option key={conversation.id} value={conversation.id}>
              {conversation.messages.length > 0
                ? conversation.messages[0]?.body.slice(0, 50) + "..."
                : "New conversation"
              }
            </option>
          ))}
        </select>
      )}

      <div className="ml-auto">
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearHistory}
          disabled={!selectedConversationId}
        >
          Clear Conversation
        </Button>
      </div>
    </header>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  return (
    prevProps.selectedConversationId === nextProps.selectedConversationId &&
    prevProps.conversations.length === nextProps.conversations.length
  );
});
