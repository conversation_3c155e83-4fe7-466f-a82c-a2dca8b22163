"use client";

import {
  useRef,
  useEffect,
  useCallback,
  type Dispatch,
  type SetStateAction,
  memo,
  forwardRef,
} from "react";
import { type User } from "@prisma/client";
import { ArrowUp, Paperclip, Square } from "lucide-react";
import { Button } from "~/v2/components/ui/Button";
import { Textarea } from "~/v2/components/ui/Textarea";
import { api } from "~/trpc/react";
import { type ChatMessageGetType } from "~/server/api/routers/chat";
import { type ModelParameters } from "~/views/chat/ChatModelParametersSelector";
import { ChatMessageStatus } from "@prisma/client";
import { cn } from "~/v2/lib/utils";
import { overrideMuiTheme } from "~/v2/lib/mui-theme-overrides";
import { uuidv4 } from "~/utils/uuidv4";
import * as Sentry from "@sentry/react";

interface MultimodalInputProps {
  selectedConversationId: string;
  input: string;
  setInput: Dispatch<SetStateAction<string>>;
  status: 'streaming' | 'ready';
  messages: ChatMessageGetType[];
  setMessages: Dispatch<SetStateAction<ChatMessageGetType[]>>;
  userDetails: User | undefined;
  tagIds: string[];
  categoryIds: string[];
  fundIds: string[];
  modelParameters: ModelParameters;
  setLoading: Dispatch<SetStateAction<boolean>>;
}

const PureMultimodalInput = forwardRef<HTMLTextAreaElement, MultimodalInputProps>(function PureMultimodalInput({
  selectedConversationId,
  input,
  setInput,
  status,
  messages,
  setMessages,
  userDetails,
  tagIds,
  categoryIds,
  fundIds,
  modelParameters,
  setLoading,
}: MultimodalInputProps, ref) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const send = api.chat.send.useMutation();
  const org = api.organization.get.useQuery({});

  useEffect(() => {
    if (textareaRef.current) {
      adjustHeight();
    }
  }, [input]);

  const adjustHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
    }
  };

  const resetHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = '60px';
    }
  };

  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(event.target.value);
  };

  const submitForm = useCallback(async () => {
    if (!input.trim() || !selectedConversationId || !userDetails || status === 'streaming') {
      return;
    }

    try {
      await Sentry.startSpan(
        {
          name: "Chat Message [New Input]",
          op: "ai.chat",
          attributes: {
            "input.char_count": input.length,
            "input.language": "en",
            "input.type": "text",
          },
        },
        async (span) => {
          setLoading(true);

          const newMessageId = uuidv4();
          const userMessage: ChatMessageGetType = {
            id: newMessageId,
            body: input,
            contentType: "text",
            createdAt: new Date(),
            updatedAt: new Date(),
            createdById: userDetails.id,
            conversationId: selectedConversationId,
            orgId: org.data?.id ?? "",
            seq: messages.length + 1,
            status: ChatMessageStatus.READY,
            attachments: [],
            feedback: null,
            metadata: {
              modelParameters: modelParameters,
            },
          };

          // Add user message immediately
          setMessages((prev) => [...prev, userMessage]);

          span.setAttribute("input.char_count", input.length.toString());
          span.setAttribute("message.id", newMessageId);
          span.setAttribute("conversation.id", selectedConversationId);
          span.setAttribute("user.id", userDetails.id);
          
          const messageToSend = input;
          setInput('');
          resetHeight();

          try {
            send.mutate(
              {
                conversationId: selectedConversationId,
                message: messageToSend,
                messageHistory: messages.map((message) => ({
                  role:
                    message.createdById === userDetails.id
                      ? "user"
                      : "assistant",
                  content: message.body,
                })),
                tagIds: tagIds,
                categoryIds: categoryIds,
                fundIds: fundIds,
                modelParameters: modelParameters,
              },
              {
                onSuccess: async (data) => {
                  let assistantMessage: ChatMessageGetType | null = null;

                  try {
                    for await (const val of data) {
                      if (val.response?.answer) {
                        const messageContent = JSON.stringify({
                          answer: val.response.answer,
                          citations: val.response.citations || [],
                          similarQuestions: val.response.similarQuestions || [],
                        });

                        assistantMessage = {
                          id: val.chatMessageId ?? uuidv4(),
                          body: messageContent,
                          contentType: "text",
                          createdAt: new Date(),
                          updatedAt: new Date(),
                          createdById: val.createdById ?? "",
                          conversationId: selectedConversationId,
                          orgId: org.data?.id ?? "",
                          seq: messages.length + 2,
                          status: ChatMessageStatus.READY,
                          attachments: [],
                          feedback: null,
                          metadata: {},
                        };

                        // Update or add assistant message
                        setMessages((prev) => {
                          const existing = prev.find(m => m.id === assistantMessage?.id);
                          if (existing) {
                            return prev.map(m => m.id === assistantMessage?.id ? assistantMessage! : m);
                          } else {
                            return [...prev, assistantMessage!];
                          }
                        });
                      }
                    }
                  } catch (error) {
                    console.error("Error processing stream:", error);
                  } finally {
                    setLoading(false);
                  }
                },
                onError: (error) => {
                  console.error("Error sending message:", error);
                  setLoading(false);
                  // Remove the user message on error
                  setMessages((prev) => prev.filter(m => m.id !== newMessageId));
                  // Restore the input
                  setInput(messageToSend);
                },
              }
            );
          } catch (error) {
            console.error("Error in message sending:", error);
            setLoading(false);
            setMessages((prev) => prev.filter(m => m.id !== newMessageId));
            setInput(messageToSend);
          }
        }
      );
    } catch (error) {
      console.error("Error in Sentry span:", error);
      setLoading(false);
    }
  }, [
    input,
    selectedConversationId,
    userDetails,
    status,
    messages,
    tagIds,
    categoryIds,
    fundIds,
    modelParameters,
    send,
    org.data?.id,
    setLoading,
    setMessages,
    setInput,
  ]);

  const stop = useCallback(() => {
    setLoading(false);
    // TODO: Implement actual stream stopping
  }, [setLoading]);

  return (
    <div className={cn(overrideMuiTheme, "relative w-full flex flex-col gap-4")}>
      <input
        type="file"
        className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
        ref={fileInputRef}
        multiple
        tabIndex={-1}
      />

      <Textarea
        data-testid="multimodal-input"
        ref={(element) => {
          textareaRef.current = element;
          if (typeof ref === 'function') {
            ref(element);
          } else if (ref) {
            ref.current = element;
          }
        }}
        placeholder="Send a message..."
        value={input}
        onChange={handleInput}
        className={cn(
          'min-h-[90px] max-h-[calc(75dvh)] overflow-hidden resize-none rounded-2xl !text-base bg-muted dark:border-zinc-700',
        )}
        rows={2}
        autoFocus
        onKeyDown={(event) => {
          if (
            event.key === 'Enter' &&
            !event.shiftKey &&
            !event.nativeEvent.isComposing
          ) {
            event.preventDefault();

            if (status !== 'ready') {
              // TODO: Show toast error
              console.log('Please wait for the model to finish its response!');
            } else {
              submitForm();
            }
          }
        }}
      />

      <div className="absolute bottom-0 p-2 w-fit flex flex-row justify-start">
        <Button
          data-testid="attachments-button"
          className="rounded-md rounded-bl-lg relative -left-[4px] p-[7px] h-fit dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200"
          onClick={(event) => {
            event.preventDefault();
            fileInputRef.current?.click();
          }}
          disabled={status !== 'ready'}
          variant="ghost"
        >
          <Paperclip size={14} />
        </Button>
      </div>

      <div className="absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end">
        {status === 'streaming' ? (
          <Button
            data-testid="stop-button"
            className="rounded-full p-1.5 h-fit border dark:border-zinc-600"
            onClick={(event) => {
              event.preventDefault();
              stop();
            }}
          >
            <Square size={14} />
          </Button>
        ) : (
          <Button
            data-testid="send-button"
            className="rounded-full p-1.5 h-fit border dark:border-zinc-600"
            onClick={(event) => {
              event.preventDefault();
              submitForm();
            }}
            disabled={input.length === 0}
          >
            <ArrowUp size={14} />
          </Button>
        )}
      </div>
    </div>
  );
});

export const MultimodalInput = memo(
  PureMultimodalInput,
  (prevProps, nextProps) => {
    if (prevProps.input !== nextProps.input) return false;
    if (prevProps.status !== nextProps.status) return false;
    if (prevProps.selectedConversationId !== nextProps.selectedConversationId) return false;

    return true;
  },
);
