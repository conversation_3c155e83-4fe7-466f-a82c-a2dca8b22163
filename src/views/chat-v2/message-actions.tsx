"use client";

import { memo, useState, useEffect } from "react";
import { toast } from "sonner";
import { type ChatMessageGetType } from "~/server/api/routers/chat";
import { type User } from "@prisma/client";
import { Button } from "~/v2/components/ui/Button";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "~/v2/components/ui/Tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "~/v2/components/ui/Popover";
import { Textarea } from "~/v2/components/ui/Textarea";
import { Copy, ThumbsUp, ThumbsDown, Send } from "lucide-react";
import { api } from "~/trpc/react";
import { ChatMessageFeedbackType } from "@prisma/client";
import equal from "fast-deep-equal";
import { cn } from "~/v2/lib/utils";

interface MessageActionsProps {
  message: ChatMessageGetType;
  userDetails: User | undefined;
  isLoading?: boolean;
}

function PureMessageActions({
  message,
  userDetails,
  isLoading = false,
}: MessageActionsProps) {
  const [feedbackSubmitted, setFeedbackSubmitted] = useState<ChatMessageFeedbackType | null>(null);
  const [feedbackText, setFeedbackText] = useState("");
  const [isUpvotePopoverOpen, setIsUpvotePopoverOpen] = useState(false);
  const [isDownvotePopoverOpen, setIsDownvotePopoverOpen] = useState(false);
  const [pendingFeedbackType, setPendingFeedbackType] = useState<ChatMessageFeedbackType | null>(null);
  const feedback = api.chat.feedback.useMutation();

  // Initialize feedback state from existing feedback
  useEffect(() => {
    if (message.feedback && userDetails && message.feedback.createdById === userDetails.id) {
      setFeedbackSubmitted(message.feedback.type);
    }
  }, [message.feedback, userDetails]);

  // Don't show actions for user messages or while loading
  if (isLoading || message.createdById === userDetails?.id) {
    return null;
  }

  // Parse the message content to get the actual answer text
  const getMessageText = () => {
    try {
      const parsed = JSON.parse(message.body);
      return parsed.answer ?? message.body;
    } catch {
      return message.body;
    }
  };

  const handleCopy = async () => {
    const messageText = getMessageText();
    if (!messageText?.trim()) {
      toast.error("There's nothing to copy!");
      return;
    }

    try {
      await navigator.clipboard.writeText(messageText);
      toast.success("Copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy to clipboard");
    }
  };

  const handleFeedbackClick = (type: ChatMessageFeedbackType) => {
    if (feedbackSubmitted === type) {
      return;
    }

    setPendingFeedbackType(type);
    setFeedbackText("");
    
    if (type === ChatMessageFeedbackType.GOOD) {
      setIsUpvotePopoverOpen(true);
    } else {
      setIsDownvotePopoverOpen(true);
    }
  };

  const handleFeedbackSubmit = async () => {
    if (!pendingFeedbackType) return;

    setFeedbackSubmitted(pendingFeedbackType);

    feedback.mutate(
      {
        chatMessageId: message.id,
        feedback: feedbackText.trim() || null,
        type: pendingFeedbackType,
        ...(message.metadata && (message.metadata as any)?.traceId ? {
          traceId: (message.metadata as any).traceId as string,
        } : {}),
      },
      {
        onSuccess: () => {
          toast.success(
            pendingFeedbackType === ChatMessageFeedbackType.GOOD
              ? "Thanks for the positive feedback!"
              : "Thanks for the feedback, we'll use it to improve."
          );
          setIsUpvotePopoverOpen(false);
          setIsDownvotePopoverOpen(false);
          setPendingFeedbackType(null);
          setFeedbackText("");
        },
        onError: () => {
          setFeedbackSubmitted(null);
          toast.error("Failed to submit feedback. Please try again.");
        },
      }
    );
  };

  const handleUpvotePopoverOpenChange = (open: boolean) => {
    if (!open) {
      setIsUpvotePopoverOpen(false);
      setPendingFeedbackType(null);
      setFeedbackText("");
    }
  };

  const handleDownvotePopoverOpenChange = (open: boolean) => {
    if (!open) {
      setIsDownvotePopoverOpen(false);
      setPendingFeedbackType(null);
      setFeedbackText("");
    }
  };

  return (
    <TooltipProvider delayDuration={300}>
      <div className="flex flex-row gap-2 opacity-0 group-hover/message:opacity-100 transition-opacity duration-200 pointer-events-none group-hover/message:pointer-events-auto">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="py-1 px-2 h-fit text-muted-foreground"
              onClick={handleCopy}
            >
              <Copy size={14} />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Copy</TooltipContent>
        </Tooltip>

        <Popover open={isUpvotePopoverOpen} onOpenChange={handleUpvotePopoverOpenChange}>
          <PopoverTrigger asChild>
            <Button
              data-testid="message-upvote"
              variant={feedbackSubmitted === ChatMessageFeedbackType.GOOD ? "default" : "outline"}
              size="sm"
              className={cn(
                "py-1 px-2 h-fit",
                feedbackSubmitted === ChatMessageFeedbackType.GOOD 
                  ? "text-primary-foreground" 
                  : "text-muted-foreground"
              )}
              disabled={feedbackSubmitted !== null}
              onClick={() => handleFeedbackClick(ChatMessageFeedbackType.GOOD)}
            >
              <ThumbsUp size={14} />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-4" align="start">
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-sm">What was helpful about this response?</h4>
                <p className="text-xs text-muted-foreground mt-1">
                  Your feedback helps us improve our AI responses.
                </p>
              </div>
              <Textarea
                placeholder="Tell us what was helpful (optional)..."
                value={feedbackText}
                onChange={(e) => setFeedbackText(e.target.value)}
                className="min-h-[80px] resize-none"
                maxLength={500}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleFeedbackSubmit();
                  } else if (e.key === 'Escape') {
                    setIsUpvotePopoverOpen(false);
                    setPendingFeedbackType(null);
                    setFeedbackText("");
                  }
                }}
              />
              <div className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">
                  {feedbackText.length}/500 characters
                </span>
                <Button
                  size="sm"
                  onClick={handleFeedbackSubmit}
                  disabled={feedback.isPending}
                  className="flex items-center gap-2"
                >
                  <Send size={14} />
                  Submit
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <Popover open={isDownvotePopoverOpen} onOpenChange={handleDownvotePopoverOpenChange}>
          <PopoverTrigger asChild>
            <Button
              data-testid="message-downvote"
              variant={feedbackSubmitted === ChatMessageFeedbackType.BAD ? "default" : "outline"}
              size="sm"
              className={cn(
                "py-1 px-2 h-fit",
                feedbackSubmitted === ChatMessageFeedbackType.BAD 
                  ? "text-primary-foreground" 
                  : "text-muted-foreground"
              )}
              disabled={feedbackSubmitted !== null}
              onClick={() => handleFeedbackClick(ChatMessageFeedbackType.BAD)}
            >
              <ThumbsDown size={14} />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-4" align="start">
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-sm">What could be improved about this response?</h4>
                <p className="text-xs text-muted-foreground mt-1">
                  Your feedback helps us improve our AI responses.
                </p>
              </div>
              <Textarea
                placeholder="Tell us what could be improved (optional)..."
                value={feedbackText}
                onChange={(e) => setFeedbackText(e.target.value)}
                className="min-h-[80px] resize-none"
                maxLength={500}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleFeedbackSubmit();
                  } else if (e.key === 'Escape') {
                    setIsDownvotePopoverOpen(false);
                    setPendingFeedbackType(null);
                    setFeedbackText("");
                  }
                }}
              />
              <div className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">
                  {feedbackText.length}/500 characters
                </span>
                <Button
                  size="sm"
                  onClick={handleFeedbackSubmit}
                  disabled={feedback.isPending}
                  className="flex items-center gap-2"
                >
                  <Send size={14} />
                  Submit
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </TooltipProvider>
  );
}

export const MessageActions = memo(
  PureMessageActions,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message, nextProps.message)) return false;
    if (!equal(prevProps.userDetails, nextProps.userDetails)) return false;

    return true;
  },
);
