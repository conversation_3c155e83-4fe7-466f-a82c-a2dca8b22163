"use client";

import { type Org } from "@prisma/client";
import { Bell, BringToFront, Earth, HandCoins, Link2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback } from "react";
import {
  _investorCommunications,
  _investorDocuments,
  _investors,
} from "~/_mock";
import { paths } from "~/routes/paths";
import { Avatar } from "~/v2/components/ui/Avatar";
import { Button } from "~/v2/components/ui/Button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "~/v2/components/ui/Card";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "~/v2/components/ui/Tabs";
import {
  InvestorDocuments,
  InvestorOverview,
  InvestorTag,
} from "../components";

type Props = {
  org: Org;
  investorId: string;
};

const DetailTabs = [
  { value: "Overview", label: "Overview" },
  { value: "Documents", label: "Documents" },
];

export function InvestorDetailView({ org: _org, investorId }: Props) {
  const router = useRouter();

  // Find investor by ID (in real app this would be from API)
  const investor = _investors.find((inv) => inv.id === investorId);

  const handleBack = useCallback(() => {
    router.push(paths.dashboard.investor.root);
  }, [router]);

  if (!investor) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Investor not found</h2>
          <Button onClick={handleBack} className="mt-4">
            Back to Investor Database
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-2">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Avatar className="w-6 h-6">
          <div className="text-2xl font-bold">{investor?.name?.charAt(0)}</div>
        </Avatar>
        <div className="flex-1">
          <div className="flex items-center gap-4">
            <div>
              <span className="flex gap-2 items-center">
                <h1 className="text-2xl font-bold">{investor.name}</h1>
                <InvestorTag status={investor?.status}>
                  {investor?.status}
                </InvestorTag>
              </span>
              <p className="text-gray-600">{investor.description}</p>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          {/* <Button variant="outline">
            <Bell className="w-4 h-4 mr-2" />
            Turn on Alerts
          </Button> */}
          <Button>Generate Brief</Button>
        </div>
      </div>

      {/* Investor Info Bar */}
      <div className="flex gap-4">
        <div className="flex items-center gap-1">
          <Earth />
          <div>
            <div className="text-sm font-semibold">{investor.geography}</div>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <BringToFront />
          <div className="text-sm font-semibold">{investor.type}</div>
        </div>
        <div className="flex items-center gap-1">
          <HandCoins />
          <div className="text-sm font-semibold">{investor.aum}</div>
        </div>
        <div className="flex items-center gap-1">
          <Link2 />
          <a
            href={`https://${investor.website}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm font-semibold underline"
          >
            {investor.website}
          </a>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="Overview" className="w-full">
        <TabsList className="w-96 mb-4">
          {DetailTabs.map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="Overview">
          <InvestorOverview investor={investor} />
        </TabsContent>

        <TabsContent value="Documents">
          <InvestorDocuments documents={_investorDocuments} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
