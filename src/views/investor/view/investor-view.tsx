"use client";

import { type Org } from "@prisma/client";
import { Calendar, DollarSign, Globe } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import { _investors } from "~/_mock";
import Loading from "~/app/loading";
import { useDebounce } from "~/hooks/use-debounce";
import { DashboardContent, DashboardHeader } from "~/layouts/Dashboard";
import { paths } from "~/routes/paths";
import { api } from "~/trpc/react";
import { fCurrency } from "~/utils/format-number";
import { Typography } from "~/v2/components";
import {
  MultiSelect,
  MultiSelectClear,
  MultiSelectContent,
  MultiSelectItem,
  MultiSelectList,
  MultiSelectTrigger,
  MultiSelectValues,
} from "~/v2/components/composit/MultiSelect/MultiSelect";
import { Button } from "~/v2/components/ui/Button";
import { CardContent } from "~/v2/components/ui/Card";
import { Input } from "~/v2/components/ui/Input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/v2/components/ui/Popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/v2/components/ui/Select";
import { InvestorCard } from "../components";
import LookupInvestorDialog from "../components/LookupInvestorDialog";

type Props = {
  org: Org;
};

export function InvestorView({ org: _org }: Props) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterGeography, setFilterGeography] = useState<string | undefined>(
    undefined,
  );
  const [filterType, setFilterType] = useState<string | undefined>(undefined);
  const [filterAumFrom, setFilterAumFrom] = useState<number | undefined>(
    undefined,
  );
  const [filterAumTo, setFilterAumTo] = useState<number | undefined>(undefined);
  const [filterStatuses, setFilterStatuses] = useState<string[]>([]);

  const [open, setOpen] = useState(false);

  const geographies = [
    { value: "US", label: "US" },
    { value: "Asia", label: "Asia" },
    { value: "Europe", label: "Europe" },
  ];

  const types = [
    { value: "Public Pension", label: "Public Pension" },
    { value: "Private Pension", label: "Private Pension" },
  ];

  const statusOptions = [
    { value: "Active", label: "Active" },
    { value: "Inactive", label: "Inactive" },
    { value: "Pending", label: "Pending" },
    { value: "Closed", label: "Closed" },
  ];

  const debouncedSsearchTermFilter = useDebounce(searchTerm, 1000);
  const debouncedAumFromFilter = useDebounce(
    filterAumFrom ? `${filterAumFrom}` : "",
    1000,
  );
  const debouncedAumToFilter = useDebounce(
    filterAumTo ? `${filterAumTo}` : "",
    1000,
  );

  const {
    data: investors,
    isLoading,
    error,
  } = api.investor.getInvestors.useQuery({
    searchTerm: debouncedSsearchTermFilter,
    geography: filterGeography,
    type: filterType,
    aum: {
      from: debouncedAumFromFilter ? Number(debouncedAumFromFilter) : undefined,
      to: debouncedAumToFilter ? Number(debouncedAumToFilter) : undefined,
    },
    statuses: filterStatuses,
  });

  console.log("Investors query", investors);

  const handleInvestorClick = useCallback(
    (investorId: string) => {
      router.push(paths.dashboard.investor.details(investorId));
    },
    [router],
  );

  const filteredInvestors = _investors.filter((investor) => {
    const matchesSearch =
      searchTerm === "" ||
      (investor.name?.toLowerCase().includes(searchTerm.toLowerCase()) ??
        false) ||
      (investor.description?.toLowerCase().includes(searchTerm.toLowerCase()) ??
        false);
    return matchesSearch;
  });

  const renderSearchAndFilters = (
    <div className="space-y-4 mt-2 w-full">
      {/* Search Bar */}
      <Input
        placeholder="Search"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="w-full"
      />

      <div className="flex flex-wrap gap-3">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" className="bg-neutral-100" size="sm">
              <Globe className="w-4 h-4" />
              All geographies
            </Button>
          </PopoverTrigger>
          <PopoverContent>
            <CardContent className="p-4 space-y-4">
              <div className="space-y-2">
                <Typography variant="h6" className="font-semibold">
                  {filterGeography || "All geographies"}
                </Typography>
                <Select
                  value={filterGeography || ""}
                  onValueChange={setFilterGeography}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a geography" />
                  </SelectTrigger>
                  <SelectContent>
                    {geographies?.map(
                      ({ value, label }: { value: string; label: string }) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ),
                    )}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </PopoverContent>
        </Popover>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" className="bg-neutral-100" size="sm">
              <Globe className="w-4 h-4" />
              {filterType || "All types"}
            </Button>
          </PopoverTrigger>
          <PopoverContent>
            <CardContent className="p-4 space-y-4">
              <div className="space-y-2">
                <Typography variant="h6" className="font-semibold">
                  Type
                </Typography>
                <Select value={filterType || ""} onValueChange={setFilterType}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a type" />
                  </SelectTrigger>
                  <SelectContent>
                    {types?.map(
                      ({ value, label }: { value: string; label: string }) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ),
                    )}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </PopoverContent>
        </Popover>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" className="bg-neutral-100" size="sm">
              <DollarSign className="w-4 h-4" />
              {filterAumFrom || filterAumTo
                ? `${fCurrency(filterAumFrom)} - ${fCurrency(filterAumTo)}`
                : "All AUM"}
            </Button>
          </PopoverTrigger>
          <PopoverContent>
            <CardContent className="p-4 space-y-4">
              <div className="space-y-2">
                <Typography variant="h6" className="font-semibold">
                  AUM
                </Typography>

                <Input
                  placeholder="Enter AUM From"
                  value={filterAumFrom}
                  onChange={(e) => setFilterAumFrom(Number(e.target.value))}
                />
                <Input
                  placeholder="Enter AUM To"
                  value={filterAumTo}
                  onChange={(e) => setFilterAumTo(Number(e.target.value))}
                />
              </div>
            </CardContent>
          </PopoverContent>
        </Popover>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" className="bg-neutral-100" size="sm">
              <Calendar className="w-4 h-4" />
              {filterStatuses.length > 0
                ? statusOptions
                    .filter((option) => filterStatuses.includes(option.value))
                    .map((option) => option.label)
                    .join(", ")
                : "All statuses"}
            </Button>
          </PopoverTrigger>
          <PopoverContent>
            <CardContent className="p-4 space-y-4">
              <div className="space-y-2">
                <Typography variant="h6" className="font-semibold">
                  Status
                </Typography>

                <MultiSelect
                  options={statusOptions}
                  selected={filterStatuses}
                  onClose={(selected) => setFilterStatuses(selected)}
                >
                  <MultiSelectTrigger>
                    <MultiSelectValues placeholder="Select response statuses" />
                  </MultiSelectTrigger>
                  <MultiSelectContent>
                    <MultiSelectList>
                      {statusOptions.map((option) => (
                        <MultiSelectItem
                          key={option.value}
                          value={option.value}
                        >
                          {option.label}
                        </MultiSelectItem>
                      ))}
                      <MultiSelectClear />
                    </MultiSelectList>
                  </MultiSelectContent>
                </MultiSelect>
              </div>
            </CardContent>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );

  const renderInvestorGrid = () => {
    if (isLoading) {
      return <Loading />;
    }

    if (filteredInvestors.length === 0) {
      return (
        <div className="text-center py-10">
          <p className="text-gray-500">No investors found</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
        {filteredInvestors.map((investor) => (
          <InvestorCard
            key={investor.id}
            investor={investor}
            onClick={() => handleInvestorClick(investor.id!)}
          />
        ))}
      </div>
    );
  };

  const onAddInvestor = (form: { ids: string[] }) => {
    setOpen(false);
    // TODO: add investor
  };

  return (
    <>
      <DashboardHeader
        title="Investor Database"
        actions={[
          <Button
            key="generate-investor-profile"
            variant="default"
            size="sm"
            onClick={() => {
              setOpen(true);
            }}
          >
            Generate Investor Profile
          </Button>,
        ]}
      >
        {renderSearchAndFilters}
      </DashboardHeader>
      <DashboardContent>{renderInvestorGrid()}</DashboardContent>
      <LookupInvestorDialog
        open={open}
        setOpen={setOpen}
        onSubmit={onAddInvestor}
        loading={false}
        error={null}
      />
    </>
  );
}
