"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MoreHorizontal } from "lucide-react";
import { _investors } from "~/_mock";
import { Button } from "~/v2/components/ui/Button";
import { Card, CardContent } from "~/v2/components/ui/Card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/v2/components/ui/DropdownMenu";
import { InvestorTag } from "../components";
type InvestorType = (typeof _investors)[0];

export function InvestorCard({
  investor,
  onClick,
}: {
  investor: InvestorType;
  onClick: () => void;
}) {


  const formatLastContact = (date: Date) => {
    const days = Math.floor(
      (Date.now() - date.getTime()) / (1000 * 60 * 60 * 24),
    );
    return `${days} day${days !== 1 ? "s" : ""} ago`;
  };

  return (
    <Card
      className="p-0 cursor-pointer hover:shadow-sm transition-shadow border rounded-lg bg-white"
      onClick={onClick}
    >
      <CardContent className="px-6 py-4">
        <div className="flex justify-between items-start pb-3 mb-4 border-b">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {investor.name}
            </h3>
            <p className="text-sm text-gray-600 mb-2">{investor.description}</p>
            <a
              href={`https://${investor.website}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-neutral-500 underline hover:no-underline"
              onClick={(e) => e.stopPropagation()}
            >
              {investor.website}
            </a>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <span role="img" aria-label="actions">
                  <MoreHorizontal className="w-4 h-4" />
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => {
                  onClick();
                }}
              >
                Generate Brief
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  onClick();
                }}
              >
                View Documents
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="grid grid-cols-3 gap-4 text-sm pb-3 mb-4 border-b">
          <div>
            <div className="font-medium mb-1">Type</div>
            <div className="text-gray-500">{investor.type}</div>
          </div>
          <div>
            <div className="font-medium mb-1">AUM</div>
            <div className="text-gray-500">{investor.aum}</div>
          </div>
          <div>
            <div className="font-medium mb-1">Geography</div>
            <div className="text-gray-500">{investor.geography}</div>
          </div>
        </div>

        <div className="pb-3 mb-4 border-b">
          <div className="flex items-center justify-between w-full gap-2 mb-2">
            <span className="text-sm text-gray-600">Market Intelligence</span>
            <ChevronLeft className="w-4 h-4 text-gray-400" />
          </div>
          <div className="flex items-center justify-between w-full gap-2">
            <span className="text-sm text-gray-600">Documents</span>
            <ChevronLeft className="w-4 h-4 text-gray-400" />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <InvestorTag status={investor?.status}>
            {investor?.status}
          </InvestorTag>
          <span className="text-sm text-gray-500">
            Last contact: {formatLastContact(investor.lastContact)}
          </span>
        </div>
      </CardContent>
    </Card>
  );
}
