import { _investorCommunications } from "~/_mock";
import { Button } from "~/v2/components/ui/Button";

export const DocumentHistory = () => {
  return (
    <div className="space-y-4  bg-neutral-100 py-2 px-4 rounded-sm">
      <div className="flex flex-col">
        <h3 className="text-lg font-semibold">Document History</h3>
        <div className="text-sm text-gray-500">4 submissions</div>
      </div>
      {_investorCommunications.map((item) => (
        <div key={item.id} className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="w-3 h-3 bg-success rounded-full mt-1"></div>
            <div className="border-l border-neutral-200 h-12 ml-1 mt-1" />
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium">{item.date}</div>
            <div className="text-sm text-gray-600 mt-1">{item.title}</div>
            <Button variant="link" className="h-auto text-sm">
              {item.description}
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};
