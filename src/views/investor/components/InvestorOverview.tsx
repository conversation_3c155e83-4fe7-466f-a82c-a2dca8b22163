"use client";

import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "~/v2/components/ui/Card";

export const InvestorOverviewCard = ({
  title,
  value,
  description,
}: {
  title: string;
  value: string | number;
  description?: string;
}) => {
  return (
    <Card className="gap-1 py-4 border-t-success border-t-5 rounded-sm bg-neutral-100">
      <CardHeader>
        <CardTitle className="text-md font-semibold text-neutral-500">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="text-3xl font-bold">{value}</div>
        {description && (
          <div className="text-md text-neutral-500">{description}</div>
        )}
      </CardContent>
    </Card>
  );
};

export function InvestorOverview({ investor }: { investor: any }) {
  return (
    <div className="space-y-6">
      {/* Main Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <InvestorOverviewCard
          title="Alternative Investment Target"
          value={investor.alternativeInvestmentTarget}
          description="Allocation"
        />

        <InvestorOverviewCard
          title="Fund Size Preference"
          value={investor.fundSizePreference}
        />

        <InvestorOverviewCard
          title="Vintage Year Focus"
          value={investor.vintageYearFocus}
        />

        <InvestorOverviewCard
          title="Sector Focus"
          value={investor.sectorFocus}
        />

        <InvestorOverviewCard
          title="Geographic Mandate"
          value={investor.geography}
          description="Emphasis on North America and Developed Market"
        />
      </div>
    </div>
  );
}
