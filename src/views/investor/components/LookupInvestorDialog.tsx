import { DataGridPremium, GridColDef } from "@mui/x-data-grid-premium";
import React, { useEffect, useMemo, useState } from "react";
import { _investors } from "~/_mock";
import { Button } from "~/v2/components/ui";
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "~/v2/components/ui/Dialog";
import { Input } from "~/v2/components/ui/Input";

interface LookupInvestorDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  parentOptions?: { id: string; name: string };
  onSubmit: (data: { ids: string[] }) => void;
  loading: boolean;
  error: string | null;
}

const LookupInvestorDialog: React.FC<LookupInvestorDialogProps> = ({
  open,
  setOpen,
  parentOptions,
  onSubmit,
  loading,
  error,
}) => {
  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  useEffect(() => {
    if (!open) {
      setSelectedRows([]);
    }
  }, [open]);

  const handleInput = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {};

  const onSearchInvestor = (e: React.ChangeEvent<HTMLInputElement>) => {};

  const columns: GridColDef[] = useMemo(() => {
    const cols: GridColDef[] = [
      {
        field: "id",
      },
      {
        field: "name",
        headerName: "Name",
        flex: 0.3,
        renderCell: (params) => {
          return <div className="!text-[10px]">{params.row.name}</div>;
        },
      },
      {
        field: "website",
        headerName: "Website",
        flex: 0.3,
        renderCell: (params) => {
          return <div className="!text-[10px]">{params.row.website}</div>;
        },
      },
    ];
    return cols;
  }, []);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Generate Investor Profile</DialogTitle>
        </DialogHeader>
        <div>
          <Input
            name="query"
            onChange={onSearchInvestor}
            required
            disabled={loading}
          />
        </div>
        <DataGridPremium
          columnHeaderHeight={38}
          rowHeight={35}
          disableColumnSorting
          disableColumnFilter
          disableAggregation
          disableRowGrouping
          checkboxSelection
          rows={_investors}
          columns={columns}
          hideFooterRowCount
          onRowSelectionModelChange={(selectedRows) => {
            setSelectedRows(selectedRows as string[]);
          }}
          pagination
          initialState={{
            pagination: {
              paginationModel: {
                pageSize: 10,
              },
            },
            rowGrouping: { model: ["name"] },
            columns: {
              columnVisibilityModel: {
                id: false,
              },
            },
          }}
          sx={{
            "& .MuiCheckbox-root.Mui-disabled": {
              display: "none",
            },
            "--DataGrid-cellOffsetMultiplier": 0.1,
          }}
        />
        <DialogFooter>
          <Button disabled={loading} onClick={() => onSubmit({ids: selectedRows})}>
            {loading ? "Saving..." : "Save"}
          </Button>
          <DialogClose asChild>
            <Button type="button" variant="outline" disabled={loading}>
              Cancel
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LookupInvestorDialog;
