"use client";

import {
  DataGridPremium,
  type GridColDef,
  type GridRowsProp
} from "@mui/x-data-grid-premium";
import { useEffect, useMemo, useState } from "react";
import { _investorDocuments } from "~/_mock";
import { type GetDocumentsType } from "~/server/api/routers/document";
import { fDateTime } from "~/utils/format-time";
import { DocumentStatusComponent } from "~/views/data-room/FileExplorer";
import { DocumentHistory } from "./DocumentHistory";

type Props = {
  documents?: Partial<GetDocumentsType>[];
};
export function InvestorDocuments({ documents }: Props) {
  const [rows, setRows] = useState<GridRowsProp>([]);

  useEffect(() => {
    if (documents) {
      setRows(documents);
    }
  }, [documents]);

  const columns: GridColDef[] = useMemo(() => {
    const cols: GridColDef[] = [
      {
        field: "id",
      },
      {
        field: "name",
        headerName: "Files",
        flex: 0.3,
        renderCell: (params) => {
          return <div className="!text-[10px]">{params.row.name}</div>;
        },
      },
      {
        field: "type",
        headerName: "DDQ Status",
        width: 80,
        flex: 0.1,
        renderCell: (params) => {
          if(params.row.ddqStatus === null){
            return <div className="!text-[10px]"></div>;
          }
          return <div className="!text-[10px]">DDQ</div>;
        },
      },
      {
        field: "status",
        headerName: "Status",
        width: 80,
        flex: 0.1,
        renderCell: (params) => {
          return (
            <DocumentStatusComponent
              documentName={params.row.name}
              documentType={params.row.type}
              documentStatus={params.row.status}
              sections={[]}
              worksheetTables={[]}
            />
          );
        },
      },
      {
        field: "updatedAt",
        headerName: "Modified",
        type: "date",
        width: 150,
        renderCell: (params) => {
          return (
            params.row.updatedAt && (
              <div className="!text-[10px]">
                {fDateTime(params.row.updatedAt)}
              </div>
            )
          );
        },
      },
    ];
    return cols;
  }, []);

  // Create columns with handlers
  const columnsWithHandlers = useMemo(() => {
    return columns.map((col) => {
      return col;
    });
  }, []);

  return (
    <div className="flex gap-4">
      {/* Timeline */}
      <div className="w-1/4">
        <DocumentHistory />
      </div>

      {/* Documents Table */}
      <DataGridPremium
        columnHeaderHeight={38}
        rowHeight={35}
        disableColumnSorting
        disableColumnFilter
        disableAggregation
        disableRowGrouping
        checkboxSelection
        rows={_investorDocuments}
        columns={columnsWithHandlers}
        hideFooterRowCount
        initialState={{
          rowGrouping: { model: ["name"] },
          columns: {
            columnVisibilityModel: {
              id: false,
            },
          },
        }}
        sx={{
          "& .MuiCheckbox-root.Mui-disabled": {
            display: "none",
          },
          "--DataGrid-cellOffsetMultiplier": 0.1,
          border: "1px solid grey.200",
          backgroundColor: "white",
          // maxWidth: "100%",
          flex: 1,
          "& .MuiDataGrid-cell:focus": {
            outline: "none",
          },
          "& .MuiDataGrid-cell": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
          },
          "& .MuiDataGrid-columnHeader": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
            fontSize: "12px",
          },
          "& .MuiDataGrid-row": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
          },
          "& .MuiDataGrid-cell:focus-within": {
            outline: "none",
          },
        }}
        // slots={{ noRowsOverlay: EmptyContent }}
        // processRowUpdate={processRowUpdate}
        // slotProps={{
        //   row: {
        //     onContextMenu: handleContextMenu,
        //   },
        // }}
      ></DataGridPremium>
    </div>
  );
}
