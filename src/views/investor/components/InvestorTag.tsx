import { cn } from "~/v2/lib/utils";

const getStatusColor = (status?: string) => {
  switch (status) {
    case "Active":
      return "bg-success text-white";
    case "Prospective":
      return "bg-blue-100 text-blue-800";
    case "Under Review":
      return "bg-warning text-white";
    case "Inactive":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const InvestorTag: React.FC<
  React.PropsWithChildren & { status?: string }
> = ({ children, status }) => {
  return (
    <span
      className={cn(
        "px-3 py-0.5 rounded-xl text-sm font-medium",
        getStatusColor(status),
      )}
    >
      {children}
    </span>
  );
};
