import ArticleIcon from "@mui/icons-material/Article";
import FolderRounded from "@mui/icons-material/FolderRounded";
import GridOnIcon from "@mui/icons-material/GridOn";
import ImageIcon from "@mui/icons-material/Image";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import TableChartIcon from "@mui/icons-material/TableChart";
import {
  Box,
  Checkbox,
  Chip,
  CircularProgress,
  IconButton,
  LinearProgress,
  ListItemText,
  Menu,
  MenuItem,
  Modal,
  Tooltip as MuiTooltip,
  OutlinedInput,
  Stack,
  Typography,
  useTheme,
} from "@mui/material";

import {
  AnswerGenerationType,
  Category,
  CategoryStatus,
  type Document,
  DocumentSectionStatus,
  DocumentSource,
  DocumentStatus,
  DocumentType,
  type Fund,
  type Tag,
  type TagEntityConnection,
  WorkSheetTable,
  WorkSheetTableStatus,
} from "@prisma/client";

import { Select as MuiSelect } from "@mui/material";
import { type SelectChangeEvent } from "@mui/material/Select";
import {
  DataGridPremium,
  type DataGridPremiumProps,
  type GridColDef,
  type GridRenderCellParams,
  type GridRowModel,
  type GridRowSelectionModel,
  type GridRowsProp,
  gridFilteredDescendantCountLookupSelector,
  useGridApiContext,
  useGridApiRef,
  useGridSelector,
} from "@mui/x-data-grid-premium";
import { DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { EmptyContent } from "~/components/empty-content";
import { FileThumbnail } from "~/components/file-thumbnail";
import { Iconify } from "~/components/iconify";
import { Label } from "~/components/label";
import { DriveItem } from "~/lib/integrations/azure/types";
import {
  type GetDocumentsType,
  type SectionsWithResponsesType,
} from "~/server/api/routers/document";
import { api } from "~/trpc/react";
import { getDocumentTypeByExtension } from "~/utils/document";
import { fData } from "~/utils/format-number";
import { fDateTime } from "~/utils/format-time";
import { getFilename } from "~/v2/lib/utils";
import {
  MultiSelect,
  MultiSelectClear,
  MultiSelectCommand,
  MultiSelectCommandItem,
  MultiSelectCommandList,
  MultiSelectContent,
  MultiSelectTrigger,
  MultiSelectValues,
} from "../../v2/components/composit/MultiSelect/MultiSelect";
import { env } from "~/env";
import { CompleteCategory } from "prisma/zod";
import { ViewSharepointDocument } from "~/v2/components/composit/ViewSharepointDocument";

type DocumentWithPath = GetDocumentsType & { path: string[] };
type Checked = DropdownMenuCheckboxItemProps["checked"];

type Props = {
  documents?: DocumentWithPath[];
  count?: number;
  loading?: boolean;
  // fetchNextPage?: () => Promise<void>;
  // hasNextPage?: boolean;
  source?: DocumentSource;
  rowSelectionModel: GridRowSelectionModel;
  setRowSelectionModel: (model: GridRowSelectionModel) => void;
  // paginationModel: GridPaginationModel;
  // handlePaginationModelChange: (
  //   newPaginationModel: GridPaginationModel,
  // ) => Promise<void>;
  currentPath: string;
  setCurrentPath: (path: string) => void;
};

type TagWithConnection = Tag & {
  TagEntityConnection: TagEntityConnection[];
};

export function DocumentStatusComponent(props: {
  documentName: string;
  documentType: DocumentType;
  documentStatus: DocumentStatus;
  sections: SectionsWithResponsesType[];
  worksheetTables: WorkSheetTable[];
}) {
  if (props.sections) {
    const sectionsStatus = props.sections.map((section) => section.status);
    const tableStatus: WorkSheetTableStatus[] =
      props.worksheetTables?.map((table) => table.status) ?? [];
    const responseStatus = props.sections
      .map((section) =>
        section.responses.map((r) => {
          return {
            status: r.responseContents[0]?.answerGenerationType,
            id: r.responseContents[0]?.id,
          };
        }),
      )
      .flat();

    const allSectionsReady = sectionsStatus.every(
      (status) => status === DocumentSectionStatus.READY,
    );
    const allSectionsProcessing = sectionsStatus.every(
      (status) => status === DocumentSectionStatus.PROCESSING,
    );
    const allSectionsExtracted = sectionsStatus.every(
      (status) => status === DocumentSectionStatus.EXTRACTED,
    );
    const allSectionsQuestionsAndAnswersExtracted = sectionsStatus.every(
      (status) =>
        status === DocumentSectionStatus.QUESTIONS_AND_ANSWERS_EXTRACTED,
    );
    const allSectionsGeneratingAnswers = sectionsStatus.every(
      (status) => status === DocumentSectionStatus.GENERATING_ANSWERS,
    );
    const allSectionsError = sectionsStatus.every(
      (status) => status === DocumentSectionStatus.ERROR,
    );

    const sectionsProgress = sectionsStatus.filter(
      (status) =>
        status === DocumentSectionStatus.QUESTIONS_AND_ANSWERS_EXTRACTED,
    ).length;
    const sectionsProgressValue = Math.round(
      (sectionsProgress / sectionsStatus.length) * 100,
    );
    const responseProgress = responseStatus.filter(
      (r) => r.status === AnswerGenerationType.GENERATED,
    ).length;

    const responseProgressValue =
      responseStatus.length > 0
        ? Math.round((responseProgress / responseStatus.length) * 100)
        : 0;

    const tableProgress = tableStatus.filter(
      (status) => status === WorkSheetTableStatus.READY,
    ).length;

    const tableProgressValue = Math.round(
      (tableProgress / tableStatus.length) * 100,
    );

    switch (props.documentStatus) {
      case DocumentStatus.READY:
        return (
          <Label color="success" variant="soft" className="!text-[8px]">
            READY
          </Label>
        );
      case DocumentStatus.PROCESSING:
        if (
          sectionsStatus.length === 0 ||
          props.documentType === DocumentType.XLSX ||
          props.documentType === DocumentType.XLS
        ) {
          return (
            <Label color="warning" variant="soft" className="!text-[8px]">
              PROCESSING
            </Label>
          );
        }

        return (
          <Stack direction="column" gap={1} justifyContent="space-around">
            <Typography variant="caption">
              {`${sectionsProgress} of ${sectionsStatus.length} sections processed`}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={sectionsProgressValue}
              sx={{ width: "100%" }}
            />
          </Stack>
        );
      case DocumentStatus.PENDING:
        return (
          <Label color="default" variant="soft" className="!text-[8px]">
            PENDING
          </Label>
        );
      case DocumentStatus.GENERATING_ANSWERS:
        if (responseStatus.length === 0 && tableStatus.length === 0) {
          return (
            <Label color="secondary" variant="soft" className="!text-[8px]">
              GENERATING ANSWERS
            </Label>
          );
        } else if (tableStatus.length > 0) {
          return (
            <Stack direction="column" gap={1} justifyContent="space-around">
              <Typography variant="caption">
                {`${tableProgress} of ${tableStatus.length} tables generated`}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={tableProgressValue}
                sx={{ width: "100%" }}
              />
            </Stack>
          );
        }

        return (
          <Stack direction="column" gap={1} justifyContent="space-around">
            <Typography variant="caption">
              {`${responseProgress} of ${responseStatus.length} responses generated`}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={responseProgressValue}
              sx={{ width: "100%" }}
            />
          </Stack>
        );
      case DocumentStatus.ERROR:
        return (
          <Label color="error" variant="soft" className="!text-[8px]">
            ERROR
          </Label>
        );
      default:
        return (
          <Label color="default" variant="soft" className="!text-[8px]">
            PENDING
          </Label>
        );
    }
  } else {
    return <></>;
  }
}

export function TagCollection(props: { tags: TagWithConnection[] }) {
  return (
    <Box
      display="flex"
      gap={1}
      justifyContent="flex-start"
      flexDirection="row"
      alignItems="center"
      height="100%"
      width="100%"
      pt={1}
      sx={{
        cursor: "pointer",
      }}
      className="text-[8px]"
    >
      <MuiTooltip
        title={
          <React.Fragment>
            <Box gap={1} display="flex" flexDirection="column">
              <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                {props.tags[0]?.name}
              </Typography>
              {props.tags[0]?.TagEntityConnection
                ? props.tags[0]?.TagEntityConnection[0]?.connectionReason
                : "No connection reason"}
            </Box>
          </React.Fragment>
        }
      >
        {props.tags[0]?.name ? (
          <Chip
            sx={{ width: 170, backgroundColor: props.tags[0]?.color }}
            size="small"
            variant="soft"
            label={props.tags[0]?.name}
          />
        ) : (
          <Typography variant="caption" sx={{ fontWeight: "bold" }}>
            No tags
          </Typography>
        )}
      </MuiTooltip>
      {props.tags.length > 1 && (
        <MuiTooltip
          title={
            <React.Fragment>
              {props.tags.slice(1).map((t: TagWithConnection) => (
                <Box
                  key={t.id}
                  gap={1}
                  display="flex"
                  flexDirection="column"
                  sx={{ mb: 1 }}
                >
                  <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                    {t.name}
                  </Typography>
                  <Typography variant="caption">
                    {t?.TagEntityConnection
                      ? t?.TagEntityConnection[0]?.connectionReason
                      : "No connection reason"}
                  </Typography>
                </Box>
              ))}
            </React.Fragment>
          }
        >
          <Chip
            sx={{ width: 35 }}
            size="small"
            variant="soft"
            label={`+${props.tags.length - 1}`}
          />
        </MuiTooltip>
      )}
    </Box>
  );
}

export function FundCollection(props: { funds: Fund[] }) {
  return (
    <Box
      display="flex"
      gap={1}
      justifyContent="flex-start"
      flexDirection="row"
      alignItems="center"
      height="100%"
      width="100%"
      pt={1}
      sx={{
        cursor: "pointer",
      }}
    >
      <MuiTooltip
        title={
          <React.Fragment>
            <Box gap={1} display="flex" flexDirection="column">
              <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                {props.funds[0]?.name}
              </Typography>
              {props.funds[0]?.description}
            </Box>
          </React.Fragment>
        }
      >
        {props.funds[0]?.name ? (
          <Chip
            sx={{ width: 170, backgroundColor: "primary.main", color: "white" }}
            size="small"
            variant="filled"
            label={props.funds[0]?.name}
          />
        ) : (
          <Typography variant="caption" sx={{ fontWeight: "bold" }}>
            No funds
          </Typography>
        )}
      </MuiTooltip>
      {props.funds.length > 1 && (
        <MuiTooltip
          title={
            <React.Fragment>
              {props.funds.slice(1).map((f: Fund) => (
                <Box
                  key={f.id}
                  gap={1}
                  display="flex"
                  flexDirection="column"
                  sx={{ mb: 1 }}
                >
                  <Typography variant="caption" sx={{ fontWeight: "bold" }}>
                    {f.name}
                  </Typography>
                  <Typography variant="caption">{f.description}</Typography>
                </Box>
              ))}
            </React.Fragment>
          }
        >
          <Chip
            sx={{ width: 35 }}
            size="small"
            variant="soft"
            label={`+${props.funds.length - 1}`}
          />
        </MuiTooltip>
      )}
    </Box>
  );
}



function FundsSelectEditInputCell(props: GridRenderCellParams) {
  const { id, value, field } = props;

  const apiRef = useGridApiContext();
  const theme = useTheme();
  const [selectedFundIds, setSelectedFundIds] = useState<string[]>(
    value.map((f: Fund) => f.id),
  );
  const [options, setOptions] = useState<{ value: string; label: string }[]>(
    [],
  );

  const { data: funds, isLoading } = api.fund.getAllFunds.useQuery();

  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 100,
      },
    },
  };

  useEffect(() => {
    if (!isLoading) {
      setOptions(
        funds?.map((f: Fund) => ({ value: f.id, label: f.name })) ?? [],
      );
    }
  }, [funds]);

  const handleChange = async (
    event: SelectChangeEvent<typeof selectedFundIds>,
  ) => {
    const {
      target: { value },
    } = event;

    if (value !== "") {
      const newSelectedFundIds =
        typeof value === "string"
          ? value.split(",").filter((id) => id !== "")
          : value;

      setSelectedFundIds(newSelectedFundIds);

      const newSelectedFundsWithNameAndDescription = newSelectedFundIds.map(
        (id) => funds?.find((f: Fund) => f.id === id),
      );

      await apiRef.current.setEditCellValue({
        id,
        field,
        value: newSelectedFundsWithNameAndDescription,
      });
    }
  };

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: "95%",
      }}
    >
      <MuiSelect
        value={selectedFundIds}
        multiple
        name="funds"
        onChange={handleChange}
        onClose={() => apiRef.current.stopCellEditMode({ id, field })}
        renderValue={(selected) => {
          if (selected.length === 0) {
            return "Select funds";
          }

          return selected
            .map(
              (fund: string) => funds?.find((f: Fund) => f.id === fund)?.name,
            )
            .join(", ");
        }}
        input={<OutlinedInput label="Funds" />}
        MenuProps={MenuProps}
        sx={{
          backgroundColor: theme.palette.grey[500],
          width: "100%",
          color: "white",
          fontSize: "13px",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: "5px",
          p: 0,
          m: 0,
          ml: 1.5,
          "& .MuiSelect-select": {
            paddingRight: 0,
            paddingLeft: 1,
            paddingTop: 0.5,
            marginLeft: 0.5,
            paddingBottom: 0,
            borderRadius: 0,
            color: "white",
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: "white",
            borderStyle: "none",
            borderRadius: 1,
          },
          "& .MuiSvgIcon-root": {
            color: "white",
          },
          "& .Mui-focused": {
            border: "none",
          },
          "& .MuiSelect-outlined": {
            borderRadius: 1,
          },
        }}
      >
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            <Checkbox checked={selectedFundIds.includes(option.value)} />
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </MuiSelect>
    </div>
  );
}

const renderFundsSelectEditInputCell: GridColDef["renderCell"] = (params) => {
  return <FundsSelectEditInputCell {...params} />;
};

function TagsSelectEditInputCell(props: GridRenderCellParams) {
  const { id, value, field } = props;

  const apiRef = useGridApiContext();
  const theme = useTheme();
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>(
    value.map((f: Tag) => f.id),
  );
  const [options, setOptions] = useState<{ value: string; label: string }[]>(
    [],
  );

  const { data: tags, isLoading } = api.tag.getAllTags.useQuery();

  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 100,
      },
    },
  };

  useEffect(() => {
    if (!isLoading) {
      setOptions(tags?.map((t: Tag) => ({ value: t.id, label: t.name })) ?? []);
    }
  }, [tags]);

  const handleChange = async (
    event: SelectChangeEvent<typeof selectedTagIds>,
  ) => {
    const {
      target: { value },
    } = event;

    if (value !== "") {
      const newSelectedTagIds =
        typeof value === "string"
          ? value.split(",").filter((id) => id !== "")
          : value;

      setSelectedTagIds(newSelectedTagIds);

      const newSelectedTagsWithNameAndDescription = newSelectedTagIds.map(
        (id) => tags?.find((t: Tag) => t.id === id),
      );

      await apiRef.current.setEditCellValue({
        id,
        field,
        value: newSelectedTagsWithNameAndDescription,
      });
    }
  };

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        width: "80%",
      }}
    >
      <MuiSelect
        value={selectedTagIds}
        multiple
        name="tags"
        onChange={handleChange}
        onClose={() => apiRef.current.stopCellEditMode({ id, field })}
        renderValue={(selected) =>
          selected
            .map((tag: string) => tags?.find((t: Tag) => t.id === tag)?.name)
            .join(", ")
        }
        input={<OutlinedInput label="Tags" />}
        MenuProps={MenuProps}
        sx={{
          backgroundColor: theme.palette.grey[500],
          width: "100%",
          color: "white",
          fontSize: "13px",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: "5px",
          p: 0,
          m: 0,
          ml: 1,
          "& .MuiSelect-select": {
            paddingRight: 0,
            paddingLeft: 1,
            paddingTop: 0,
            paddingBottom: 0,
            borderRadius: 0,
            color: "white",
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: "white",
            borderStyle: "none",
            borderRadius: 1,
          },
          "& .MuiSvgIcon-root": {
            color: "white",
          },
          "& .Mui-focused": {
            border: "none",
          },
          "& .MuiSelect-outlined": {
            borderRadius: 1,
          },
        }}
      >
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            <Checkbox checked={selectedTagIds.includes(option.value)} />
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </MuiSelect>
    </div>
  );
}

const renderTagsSelectEditInputCell: GridColDef["renderCell"] = (params) => {
  return <TagsSelectEditInputCell {...params} />;
};

function ActionsCell(
  props: GridRenderCellParams & {
    onOpenDocument?: (rowId: string) => void;
    onPreview?: (rowId: string) => void;
  },
) {
  const { row, onOpenDocument, onPreview } = props;
  const [contextMenu, setContextMenu] = React.useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);

  const handleClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // Only show context menu if the row is a Sharepoint document
    if (row && row.source === DocumentSource.SHAREPOINT) {
      setContextMenu(
        contextMenu === null
          ? { mouseX: event.clientX - 2, mouseY: event.clientY - 4 }
          : null,
      );
    }
  };

  const handleClose = () => {
    setContextMenu(null);
  };

  // Only render the button for Sharepoint documents
  if (row.source !== DocumentSource.SHAREPOINT) {
    return null;
  }

  return (
    <>
      <IconButton size="small" onClick={handleClick} sx={{ padding: 0.5 }}>
        <Iconify icon="eva:more-vertical-fill" width={16} />
      </IconButton>
      <Menu
        open={contextMenu !== null}
        onClose={handleClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
        slotProps={{
          root: {
            onContextMenu: (event: React.MouseEvent) => {
              event.preventDefault();
              handleClose();
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            onOpenDocument?.(row.id);
            handleClose();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Iconify icon="mdi:file-outline" />
            <Typography className="!text-[10px]">Open File</Typography>
          </Box>
        </MenuItem>
        <MenuItem
          onClick={() => {
            onPreview?.(row.id);
            handleClose();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Iconify icon="mdi:magnify" />
            <Typography className="!text-[10px]">Preview</Typography>
          </Box>
        </MenuItem>
      </Menu>
    </>
  );
}

const createActionsCellRenderer = (
  onOpenDocument: (rowId: string) => void,
  onPreview: (rowId: string) => void,
) => {
  return (params: GridRenderCellParams) => {
    return (
      <ActionsCell
        {...params}
        onOpenDocument={onOpenDocument}
        onPreview={onPreview}
      />
    );
  };
};

const getTreeDataPath: DataGridPremiumProps["getTreeDataPath"] = (
  row: Document,
) => {
  const arr = row.name.split("/");
  if (arr.length > 1) {
    arr.shift();
  }
  return arr;
};

const getIconFromFileType = (fileType: DocumentType) => {
  switch (fileType) {
    case DocumentType.IMG:
      return ImageIcon;
    case DocumentType.PDF:
      return PictureAsPdfIcon;
    case DocumentType.DOC:
      return ArticleIcon;
    case DocumentType.DOCX:
      return ArticleIcon;
    case DocumentType.PPTX:
      return ArticleIcon;
    case DocumentType.XLSX:
      return GridOnIcon;
    case DocumentType.XLS:
      return TableChartIcon;

    case DocumentType.FOLDER:
      return FolderRounded;
    // case DocumentType.PINNED:
    //   return FolderOpenIcon;
    // case DocumentType.TRASH:
    //   return DeleteIcon;
    default:
      return ArticleIcon;
  }
};

export const SharepointDocumentHandler = ({
  row,
  openDocument,
  setOpenDocument,
}: {
  row: GridRowModel | null;
  openDocument: boolean;
  setOpenDocument: (open: boolean) => void;
}) => {
  const [metadata, setMetadata] = useState<DriveItem | null>(null);

  const metadataQuery = api.azure.getDocumentMetadata.useQuery({
    documentId: row?.id ?? "",
  });

  useEffect(() => {
    if (metadataQuery.data) {
      const { metadata, originalFileUrl } = metadataQuery.data;
      setMetadata(metadata);

      console.log("metadata", metadata);
      console.log("originalFileUrl", originalFileUrl);

      if (row?.source == DocumentSource.SHAREPOINT) {
        console.log("row.url", row.url);
        console.log("row.type", row.type);
        if (openDocument) {
          console.log("Opening document", row.name);
          const extension = row.name.split(".").pop() ?? "";
          switch (getDocumentTypeByExtension(extension)) {
            case DocumentType.PDF:
              window.open(`${metadata.webUrl}`, "_blank");
              break;
            case DocumentType.DOC:
            case DocumentType.DOCX:
              const url = `ms-word:ofe|u|${originalFileUrl}`;
              console.log("url", url);
              window.open(url, "_blank");
              break;
            case DocumentType.PPTX:
              window.open(`ms-powerpoint:ofe|u|${originalFileUrl}`, "_blank");
              break;
            case DocumentType.XLS:
            case DocumentType.XLSX:
              window.open(`ms-excel:ofe|u|${originalFileUrl}`, "_blank");
              break;
            default:
              console.log(
                "Unknown document type",
                row.name,
                getDocumentTypeByExtension(extension),
              );
              break;
          }
        }

        setOpenDocument(false);
      }
    }
  }, [metadataQuery.data, row?.source, openDocument]);
  return <></>;
};

function CustomGridTreeDataGroupingCell(props: GridRenderCellParams) {
  const { id, field, rowNode } = props;
  const apiRef = useGridApiContext();
  const filteredDescendantCountLookup = useGridSelector(
    apiRef,
    gridFilteredDescendantCountLookupSelector,
  );

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (rowNode.type !== "group") {
      return;
    }

    apiRef.current.setRowChildrenExpansion(id, !rowNode.childrenExpanded);
    apiRef.current.setCellFocus(id, field);
    event.stopPropagation();
  };

  const [openDocument, setOpenDocument] = useState(false);
  const handleClose = () => setOpenDocument(false);

  const [metadata, setMetadata] = useState<DriveItem | null>(null);
  const [originalFileUrl, setOriginalFileUrl] = useState<string | null>(null);
  const [loadMetadata, setLoadMetadata] = useState(false);
  const metadataQuery = api.azure.getDocumentMetadata.useQuery(
    {
      documentId: props.row.id,
    },
    { enabled: loadMetadata },
  );

  useEffect(() => {
    if (metadataQuery.data) {
      const { metadata, originalFileUrl } = metadataQuery.data;
      setMetadata(metadata);
      setOriginalFileUrl(originalFileUrl);

      console.log("metadata", metadata);
      console.log("originalFileUrl", originalFileUrl);

      if (props.row.source == DocumentSource.SHAREPOINT) {
        console.log("props.row.url", props.row.url);
        console.log("props.row.type", props.row.type);
        if (openDocument) {
          switch (props.row.type) {
            case DocumentType.PDF:
              window.open(`${metadata.webUrl}`, "_blank");
              break;
            case DocumentType.DOC:
            case DocumentType.DOCX:
              const url = `ms-word:ofe|u|${originalFileUrl}`;
              console.log("url", url);
              window.open(url, "_blank");
              break;
            case DocumentType.PPTX:
              window.open(`ms-powerpoint:ofe|u|${originalFileUrl}`, "_blank");
              break;
            case DocumentType.XLS:
            case DocumentType.XLSX:
              window.open(`ms-excel:ofe|u|${originalFileUrl}`, "_blank");
              break;
          }
        }

        setOpenDocument(false);
      }
    }
  }, [metadataQuery.data, loadMetadata, props.row.source, open]);

  return (
    <Box
      sx={{ ml: rowNode.depth * 4, cursor: "pointer" }}
      // onClick={handleClick}
    >
      <div>
        {rowNode.type === "group" ? (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <FileThumbnail file="folder" sx={{ width: 18, height: 18 }} />{" "}
            <span
              style={{ marginLeft: 2, marginRight: 8 }}
              className="text-[10px]"
            >
              {/* @ts-ignore */}
              <b>{rowNode.groupingKey}</b>
            </span>
          </Box>
        ) : (
          <MuiTooltip
            title={
              props.row.name.includes(props.row.source) &&
              "Similar filename detected in multiple sources. Consider renaming to avoid confusion."
            }
          >
            <Box
              sx={{ display: "flex", alignItems: "center" }}
              onDoubleClick={(e) => {
                setOpenDocument(true);
                e.stopPropagation();
                setLoadMetadata(true);
              }}
            >
              <FileThumbnail
                file={String(props.row.name)}
                sx={{ width: 20, height: 20 }}
              />
              <span className="ml-[10px] mr-2 overflow-hidden text-ellipsis whitespace-nowrap text-[10px]">
                <b>{getFilename(props.row.name)}</b>
              </span>
              <div className="flex items-center justify-end flex-1 gap-1">
                {props.row.ddqStatus !== null && (
                  <div>
                    <Chip
                      color="primary"
                      variant="soft"
                      size="small"
                      label="DDQ"
                      className="!text-[8px]"
                    />
                  </div>
                )}
                {props.row.name.includes(props.row.source) && (
                  <div className="flex items-center">
                    <Iconify
                      icon="mdi:information-outline"
                      className="mr-[8px]"
                    />
                  </div>
                )}
              </div>
            </Box>
          </MuiTooltip>
        )}
      </div>
    </Box>
  );
}

const groupingColDef: DataGridPremiumProps["groupingColDef"] = {
  headerName: "Files",
  minWidth: 350,
  flex: 1,
  renderCell: (params) => {
    return <CustomGridTreeDataGroupingCell {...params} />;
  },
};

// GenericDropdownMenu: a reusable dropdown for funds, tags, etc.
function GenericDropdownMenu<T extends { id: string; name: string }>(props: {
  options: T[];
  selectedItems: T[];
  handleChange: (items: T[]) => void;
  placeholder?: string;
}) {
  const { options, selectedItems = [], handleChange, placeholder } = props;
  return <div className="flex items-center h-full"></div>;
}

export default function FileExplorer({
  documents,
  count,
  loading,
  rowSelectionModel,
  setRowSelectionModel,
  source,
  currentPath,
  setCurrentPath,
}: Props) {
  const [rows, setRows] = useState<GridRowsProp>([]);
  const apiRef = useGridApiRef();

  // Context menu state
  const [selectedRow, setSelectedRow] = React.useState<string | null>(null);
  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [openDocument, setOpenDocument] = React.useState(false);
  const [contextMenu, setContextMenu] = React.useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);

  // Handlers for the actions column
  const handleOpenDocument = (rowId: string) => {
    setSelectedRow(rowId);
    setOpenDocument(true);
  };

  const handlePreview = (rowId: string) => {
    setSelectedRow(rowId);
    setPreviewOpen(true);
  };
  const { data: funds, isLoading } = api.fund.getAllFunds.useQuery();
  const [fundOptions, setFundOptions] = useState<
    {
      value: string;
      label: string;
    }[]
  >([]);

  // Add this for tags
  const { data: tags, isLoading: isTagsLoading } =
    api.tag.getAllTags.useQuery();

  const { data: categories, isLoading: isCategoriesLoading } =
    api.category.getAllCategories.useQuery({
      status: CategoryStatus.ACTIVE,
    });

  useEffect(() => {
    if (!isLoading) {
      setFundOptions(
        funds?.map((f: Fund) => ({ value: f.id, label: f.name })) ?? [],
      );
    }
  }, [funds]);

  const tagEntityConnections = api.tag.getTagEntityConnections.useQuery(
    {
      documents: documents
        ?.filter((doc) => doc.path.length === 0)
        .map((doc) => doc.id),
    },
    { enabled: !!documents },
  );

  const documentsWithDuplicates = useMemo(
    () =>
      documents?.map((doc) => {
        // if we're only showing a specific source, no need to find duplicates
        if (source) {
          return doc;
        }

        const existingDocs = documents?.filter((d) =>
          d.name.includes(doc.name),
        );

        if (existingDocs.length > 1) {
          // Split filename from the last dot to handle files that might have dots in the name
          const lastDotIndex = doc.name.lastIndexOf(".");
          const name =
            lastDotIndex === -1 ? doc.name : doc.name.slice(0, lastDotIndex);
          const extension =
            lastDotIndex === -1 ? "" : doc.name.slice(lastDotIndex + 1);

          return {
            ...doc,
            name: `${name} [${doc.source}].${extension}`,
          };
        }
        return doc;
      }),
    [documents, source],
  );

  // Initial load of documents
  useEffect(() => {
    if (rows.length === 0) {
      setRows(documentsWithDuplicates ?? []);
    }
  }, [documentsWithDuplicates]);

  useEffect(() => {
    const rowIds = apiRef.current.getAllRowIds();
    apiRef.current.updateRows(rowIds.map((id) => ({ id, _action: "delete" })));
  }, [source]);

  useEffect(() => {
    const rowIds = apiRef.current.getAllRowIds();
    const newRowIds = documentsWithDuplicates?.map((doc) => doc.id);

    const difference = rowIds?.filter(
      (id) => !newRowIds?.includes(id as string),
    );

    try {
      // Remove rows that have been deleted
      if (difference.length > 0) {
        console.log("difference", difference);
        apiRef.current.updateRows(
          difference.map((id) => ({
            id: id,
            _action: "delete",
          })),
        );
      }
    } catch (error) {
      console.error("[Delete] Error updating rows", error);
    }

    // This can be made more efficient by only updating the rows that have changed
    const updatedRows =
      documentsWithDuplicates?.map((doc) => ({
        id: doc.id,
        name: doc.name,
        tags: Array.isArray(doc.tags)
          ? (doc.tags as TagWithConnection[]).map((t) => ({
              ...t,
              TagEntityConnection: tagEntityConnections.data?.filter(
                (c) => c.documentId === doc.id,
              ),
            }))
          : [],
        // tags: doc.tags,
        ddqStatus: doc.ddqStatus,
        funds: doc.funds,
        type: doc.type,
        size: doc.size,
        status: doc.status,
        updatedAt: doc.updatedAt,
        ddqMetadata: doc.DDQMetadata,
        source: doc.source,
        path: doc.path,
        url: doc.url,
        sections: doc.sections,
        Categories: doc.Categories,
        worksheetTables: doc.worksheets
          ?.map((worksheet) => worksheet.tables)
          .flat(),
      })) || [];

    try {
      apiRef.current.updateRows(updatedRows);
      // }
    } catch (error) {
      console.error("[Update] Error updating rows", error);
    }
  }, [documentsWithDuplicates]);

  const updateDocument = api.document.update.useMutation({
    meta: {
      skipInvalidateQueries: false,
    },
  });

  const processRowUpdate = async (
    newRow: GridRowModel,
    oldRow: GridRowModel,
  ) => {
    const documents = [
      ...rowSelectionModel.map((row) => rows.find((r) => r.id === row)),
      newRow,
    ]
      .filter((d) => !d?.name.includes("auto-generated-row-null"))
      .filter((d) => d !== undefined);

    console.log("documents", documents);

    return new Promise((resolve, reject) => {
      updateDocument.mutate(
        {
          documents: documents.map((d) => ({
            id: d?.id ?? "",
            tags: newRow.tags.map((t: Tag) => t.id),
            funds: newRow.funds.map((f: Fund) => f.id),
            categories: newRow.Categories.map((c: Category) => c.id),
            type: d?.type,
            size: d?.size,
            status: d?.status,
            source: d?.source,
          })),
        },

        {
          onSuccess: (data) => {
            resolve({
              ...newRow,
            });
          },
          onError: (error) => {
            console.error(error);
            resolve(oldRow);
          },
        },
      );
    });
  };

  const handleContextMenu = (event: React.MouseEvent) => {
    const dataId = event.currentTarget.getAttribute("data-id");

    console.log("dataId", dataId);
    const row = rows.find((r) => r.id === dataId);
    console.log("row", row);
    event.preventDefault();
    setSelectedRow(row?.id);

    // Only show context menu if the row is a file and not a folder
    // TODO: Implement folder actions as well
    if (row && row.source === DocumentSource.SHAREPOINT) {
      setContextMenu(
        contextMenu === null
          ? { mouseX: event.clientX - 2, mouseY: event.clientY - 4 }
          : null,
      );
    }
  };

  const handleClose = () => {
    setContextMenu(null);
  };

  const columns: GridColDef[] = useMemo(() => {
    const cols: GridColDef[] = [
      {
        field: "id",
      },
      {
        field: "funds",
        headerName: "Funds",
        width: 160,
        renderCell: (params) => {
          const selectedFunds = params.row.funds as Fund[];
          const handleFundsChange = async (newFundsList: Fund[]) => {
            await processRowUpdate(
              {
                ...params.row,
                funds: newFundsList,
              },
              params.row,
            );
          };

          return (
            selectedFunds && (
              <MultiSelect
                options={fundOptions}
                selected={selectedFunds.map((f) => f.id)}
                onClose={async (selected) => {
                  const selectedFunds =
                    selected
                      .map((id) => funds?.find((f) => f.id === id))
                      .filter((fund): fund is Fund => Boolean(fund)) ?? [];
                  await handleFundsChange(selectedFunds);
                }}
                variant="minimal"
              >
                <MultiSelectTrigger>
                  <MultiSelectValues placeholder="No funds" />
                </MultiSelectTrigger>
                <MultiSelectContent>
                  <MultiSelectCommand>
                    <MultiSelectCommandList>
                      {fundOptions.map((option) => (
                        <MultiSelectCommandItem
                          key={option.value}
                          value={option.value}
                        >
                          {option.label}
                        </MultiSelectCommandItem>
                      ))}
                    </MultiSelectCommandList>
                    <MultiSelectClear />
                  </MultiSelectCommand>
                </MultiSelectContent>
              </MultiSelect>
            )
          );
        },
      },
      {
        field: "tags",
        headerName: "Tags",
        width: 160,
        renderCell: (params) => {
          const options =
            tags?.map((t) => ({
              value: t.id,
              label: t.name,
            })) ?? [];
          const row = params.row as GridRowModel;
          const selectedTags = row.tags as Tag[];
          const handleTagsChange = async (newTagsList: Tag[]) => {
            await processRowUpdate(
              {
                ...row,
                tags: newTagsList,
              },
              row,
            );
          };

          return (
            selectedTags && (
              <MultiSelect
                options={options}
                selected={selectedTags.map((t) => t.id)}
                onClose={async (selected) => {
                  const selectedTags =
                    selected
                      .map((id) => tags?.find((t) => t.id === id))
                      .filter((tag): tag is Tag => Boolean(tag)) ?? [];
                  console.log("selectedTags", selected, selectedTags);
                  await handleTagsChange(selectedTags);
                }}
                variant="minimal"
              >
                <MultiSelectTrigger>
                  <MultiSelectValues placeholder="No tags" />
                </MultiSelectTrigger>
                <MultiSelectContent>
                  <MultiSelectCommand>
                    <MultiSelectCommandList>
                      {options.map((option) => (
                        <MultiSelectCommandItem
                          key={option.value}
                          value={option.value}
                        >
                          {option.label}
                        </MultiSelectCommandItem>
                      ))}
                    </MultiSelectCommandList>
                    <MultiSelectClear />
                  </MultiSelectCommand>
                </MultiSelectContent>
              </MultiSelect>
            )
          );
        },
      },
      {
        field: "source",
        headerName: "Source",
        minWidth: 100,
        flex: 0.1,

        renderCell: (params) => {
          return (
            params.row.source && (
              <Label
                className="!text-[8px]"
                color={
                  params.row.source === DocumentSource.LOCAL
                    ? "success"
                    : params.row.source === DocumentSource.EGNYTE
                      ? "info"
                      : params.row.source === DocumentSource.SHAREPOINT
                        ? "primary"
                        : params.row.source === DocumentSource.INTRALINKS
                          ? "warning"
                          : "error"
                }
              >
                {params.row.source}
              </Label>
            )
          );
        },
      },
      {
        field: "size",
        headerName: "Size",
        flex: 0.1,
        renderCell: (params) => {
          return (
            params.row.size && (
              <Box className="!text-[10px]">{fData(params.row.size)}</Box>
            )
          );
        },
      },
      // {
      //   field: "type",
      //   headerName: "Type",
      //   width: 100,
      // },
      {
        field: "status",
        headerName: "Status",
        width: 160,
        flex: 0.3,
        renderCell: (params) => {
          return (
            <Stack
              direction="column"
              gap={1}
              justifyContent="space-around"
              sx={{
                p: 1,
              }}
            >
              <DocumentStatusComponent
                documentName={params.row.name}
                documentType={params.row.type}
                documentStatus={params.row.status}
                sections={params.row.sections}
                worksheetTables={params.row.worksheetTables}
              />
            </Stack>
          );
        },
      },
      {
        field: "modified",
        headerName: "Modified",
        type: "date",
        width: 150,
        renderCell: (params) => {
          return (
            params.row.updatedAt && (
              <Box className="!text-[10px]">
                {fDateTime(params.row.updatedAt)}
              </Box>
            )
          );
        },
      },
      {
        field: "actions",
        headerName: "Actions",
        width: 80,
        sortable: false,
        filterable: false,
        renderCell: createActionsCellRenderer(
          () => {},
          () => {},
        ), // Placeholder - will be overridden in columnsWithHandlers
      },
    ];

    if (env.NEXT_PUBLIC_ENABLE_DILIGENT_CHECKLIST === "true") {
      const categoryColumn: GridColDef = {
        field: "categories",
        headerName: "Categories",
        width: 170,
        renderCell: (params) => {
          const options =
            categories
              ?.filter((c) => c.parentId)
              ?.map((t) => ({
                value: t.id,
                label: t.name,
              })) ?? [];
          const selectedCategories = params.row
            .Categories as CompleteCategory[];
          const handleCategoriesChange = async (
            newCategoryList: CompleteCategory[],
          ) => {
            await processRowUpdate(
              {
                ...params.row,
                Categories: newCategoryList,
              },
              params.row,
            );
          };

          return (
            selectedCategories && (
              <MultiSelect
                options={options}
                selected={selectedCategories.map((t) => t.id)}
                onClose={async (selected) => {
                  const selectedCategories =
                    selected
                      .map((id) =>
                        (categories as CompleteCategory[])?.find(
                          (c) => c.id === id,
                        ),
                      )
                      .filter((category): category is CompleteCategory =>
                        Boolean(category),
                      ) ?? [];
                  await handleCategoriesChange(selectedCategories);
                }}
                variant="minimal"
              >
                <MultiSelectTrigger>
                  <MultiSelectValues placeholder="No categories" />
                </MultiSelectTrigger>
                <MultiSelectContent>
                  <MultiSelectCommand>
                    <MultiSelectCommandList>
                      {options.map((option) => (
                        <MultiSelectCommandItem
                          className="cursor-pointer"
                          key={option.value}
                          value={option.value}
                        >
                          {option.label}
                        </MultiSelectCommandItem>
                      ))}
                    </MultiSelectCommandList>
                    <MultiSelectClear />
                  </MultiSelectCommand>
                </MultiSelectContent>
              </MultiSelect>
            )
          );
        },
      };

      cols.splice(2, 0, categoryColumn);
    }

    return cols;
  }, [rows, currentPath, tags, funds, categories]);

  // Create columns with handlers
  const columnsWithHandlers = useMemo(() => {
    return columns.map((col) => {
      if (col.field === "actions") {
        return {
          ...col,
          renderCell: createActionsCellRenderer(
            handleOpenDocument,
            handlePreview,
          ),
        };
      }
      return col;
    });
  }, [handleOpenDocument, handlePreview]);

  return (
    <>
      <DataGridPremium
        key={rows.length}
        apiRef={apiRef}
        columnHeaderHeight={38}
        rowHeight={35}
        disableColumnSorting
        disableColumnFilter
        disableAggregation
        disableRowGrouping
        checkboxSelection
        disableRowSelectionOnClick
        treeData
        getTreeDataPath={getTreeDataPath}
        rows={rows}
        columns={columnsWithHandlers}
        loading={loading}
        hideFooterRowCount
        initialState={{
          rowGrouping: { model: ["name"] },
          columns: {
            columnVisibilityModel: {
              id: false,
            },
          },
        }}
        groupingColDef={groupingColDef}
        onRowDoubleClick={(params, event, details) => {
          const path = params.id as string;
          if (path.includes("auto-generated-row-null")) {
            setCurrentPath(
              currentPath + path.replace("auto-generated-row-null", ""),
            );
          }
        }}
        onRowSelectionModelChange={(newRowSelectionModel) => {
          console.log("newRowSelectionModel", newRowSelectionModel);

          const directoriesSelected = newRowSelectionModel.filter((id) =>
            id.toString().includes("auto-generated-row-null"),
          );

          console.log("directoriesSelected", directoriesSelected);

          setRowSelectionModel(newRowSelectionModel);
        }}
        rowSelectionModel={rowSelectionModel}
        rowSelectionPropagation={{
          parents: true,
          descendants: true,
        }}
        sx={{
          "& .MuiCheckbox-root.Mui-disabled": {
            display: "none",
          },
          "--DataGrid-cellOffsetMultiplier": 0.1,
          border: "1px solid grey.200",
          backgroundColor: "white",
          // maxWidth: "100%",
          flex: 1,
          "& .MuiDataGrid-cell:focus": {
            outline: "none",
          },
          "& .MuiDataGrid-cell": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
          },
          "& .MuiDataGrid-columnHeader": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
            fontSize: "12px",
          },
          "& .MuiDataGrid-row": {
            userSelect: "none",
            WebkitUserSelect: "none",
            MozUserSelect: "none",
            msUserSelect: "none",
          },
          "& .MuiDataGrid-cell:focus-within": {
            outline: "none",
          },
        }}
        slots={{ noRowsOverlay: EmptyContent }}
        processRowUpdate={processRowUpdate}
        slotProps={{
          row: {
            onContextMenu: handleContextMenu,
          },
        }}
      />
      <Menu
        open={contextMenu !== null}
        onClose={handleClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
        slotProps={{
          root: {
            onContextMenu: (event: React.MouseEvent) => {
              event.preventDefault();
              handleClose();
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            setOpenDocument(true);
            handleClose();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Iconify icon="mdi:file-outline" />
            <Typography className="!text-[10px]">Open File</Typography>
          </Box>
        </MenuItem>
        <MenuItem
          onClick={() => {
            setPreviewOpen(true);
            handleClose();
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Iconify icon="mdi:magnify" />
            <Typography className="!text-[10px]">Preview</Typography>
          </Box>
        </MenuItem>
      </Menu>
      <Modal
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        aria-labelledby="preview-modal-title"
        aria-describedby="preview-modal-description"
      >
        <ViewSharepointDocument documentId={selectedRow ?? ""} modal={true} />
      </Modal>
      {selectedRow && (
        <SharepointDocumentHandler
          row={rows.find((r) => r.id === selectedRow) as GridRowModel}
          openDocument={openDocument}
          setOpenDocument={setOpenDocument}
        />
      )}
    </>
  );
}
