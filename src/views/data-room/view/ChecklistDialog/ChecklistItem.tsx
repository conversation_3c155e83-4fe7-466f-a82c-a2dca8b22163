import { cn } from "~/v2/lib/utils";
import { api } from "~/trpc/react";
import { env } from "~/env";
import { Org } from "@prisma/client";
import { useMemo } from "react";

interface ChecklistItemProps {
  category: any;
  org: Org;
  onClick?: () => void;
  isParentCategory?: boolean;
}

export const ChecklistItem = ({
  category,
  onClick,
  org,
  isParentCategory,
}: ChecklistItemProps) => {
  const { data: checklistCountResponse } = api.category.countStatus.useQuery(
    {
      categoryId: category?.id,
    },
    {
      enabled:
        !!org.id &&
        env.NEXT_PUBLIC_ENABLE_DILIGENT_CHECKLIST === "true" &&
        !!category?.id &&
        !!isParentCategory,
    },
  );

  const checklistProgress = useMemo(() => {
    if (!checklistCountResponse) {
      return 0;
    }
    const progress = checklistCountResponse.total
      ? ((checklistCountResponse.linked ?? 0) / checklistCountResponse.total) *
        100
      : 0;

    return progress;
  }, [checklistCountResponse]);

  const getIcon = () => {
    const checkIcon = "/assets/wysiwyg/icons/circle-check.svg";
    const circleIcon = "/assets/wysiwyg/icons/circle-alert.svg";

    if (isParentCategory) {
      if (checklistProgress === 100) {
        return checkIcon;
      }
      return circleIcon;
    }

    return !!category?._count?.documents ? checkIcon : circleIcon;
  };

  return (
    <div
      onClick={onClick}
      key={category?.id}
      className={cn(
        "bg-gray-200 rounded-sm px-1 py-0.5 shadow font-semibold",
        "flex gap-2 items-center",
      )}
    >
      <img alt="checklist" src={getIcon()} />
      {category?.name}
    </div>
  );
};
