"use client";

import { useIsFetching } from "@tanstack/react-query";
import { getQuery<PERSON>ey } from "@trpc/react-query";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import Button from "@mui/material/Button";

import { useBoolean } from "src/hooks/use-boolean";

import { ConfirmDialog } from "src/components/custom-dialog";
import { FileThumbnail } from "src/components/file-thumbnail";
import { toast } from "src/components/snackbar";

import { Protect } from "@clerk/clerk-react";
import HomeIcon from "@mui/icons-material/Home";
import { Breadcrumbs, Link, Typography } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { type GridRowSelectionModel } from "@mui/x-data-grid-premium";

import {
  CategoryStatus,
  DocumentSource,
  DocumentStatus,
  type Org,
} from "@prisma/client";
import { FILE_TYPE_OPTIONS } from "~/_mock/_files";
import { env } from "~/env";
import { useDebounce } from "~/hooks/use-debounce";
import { DashboardContent } from "~/layouts/Dashboard/DashboardContent";
import { DashboardHeader } from "~/layouts/Dashboard/DashboardHeader";
import { type DeleteInputType } from "~/server/api/routers/document";
import { api } from "~/trpc/react";
import { useNavContext } from "~/v2/contexts/NavContext";
import { cn } from "~/v2/lib/utils";
import FileExplorer from "../FileExplorer";
import { FileManagerFilters } from "../FileManagerFilters";
import { FileManagerNewFolderDialog } from "../FileManagerNewFolderDialog";
import { useFilters } from "../FundsAndTagsFilter/FiltersContext";
import { FundsAndTagsFilterPopup } from "../FundsAndTagsFilter/FundsAndTagsFilterPopup";
import { ProcessActionButtons } from "../ProcessActionButtons";
import { ApplyFundsModal } from "./ApplyFundsModal";
import { ApplyTagsModal } from "./ApplyTagsModal";
import { ChecklistDialog } from "./ChecklistDialog";

type Props = {
  org: Org;
};

const TABS = [
  { value: "all", label: "All" },
  { value: "local", label: "Local" },
  { value: "egnyte", label: "Egnyte" },
  { value: "sharepoint", label: "Sharepoint" },
  { value: "intralinks", label: "Intralinks" },
];

const BreadCrumbs = ({
  currentPath,
  setCurrentPath,
}: {
  currentPath: string;
  setCurrentPath: (path: string) => void;
}) => {
  const utils = api.useUtils();

  const path = currentPath.split("/");
  return (
    <Breadcrumbs
      aria-label="breadcrumb"
      sx={{ fontSize: 10, p: 2, m: 0, color: "text.secondary" }}
      className="!text-[10px]"
    >
      {path.map((p, i) => (
        <Link
          key={i}
          underline="hover"
          className="!text-[10px]"
          sx={{
            display: "flex",
            alignItems: "center",
            cursor: "pointer",
            gap: 1,
          }}
          color="inherit"
          onClick={async () => {
            const newPath = path.slice(0, i + 1).join("/");
            setCurrentPath(newPath);
            await utils.document.getAll.invalidate();
          }}
        >
          {i === 0 && (
            <HomeIcon
              sx={{ mr: 0.2, width: 16, height: 16 }}
              fontSize="inherit"
              className="!text-[10px]"
            />
          )}
          {i > 0 && (
            <FileThumbnail file="folder" sx={{ width: 16, height: 16 }} />
          )}
          {p}
        </Link>
      ))}
    </Breadcrumbs>
  );
};

export function DataRoomView({ org }: Props) {
  const [documentSourceView, setDocumentSourceView] = useState<
    DocumentSource | undefined
  >(undefined);

  const [sharepointRefreshLoading, setSharepointRefreshLoading] =
    useState(false);
  const [sharepointSyncLoading, setSharepointSyncLoading] = useState(false);
  const [processLoading, setProcessLoading] = useState(false);
  const [rowSelectionModel, setRowSelectionModel] =
    useState<GridRowSelectionModel>([]);

  const [currentPath, setCurrentPath] = useState<string>("");
  const [checklistProgress, setChecklistProgress] = useState(0);

  const utils = api.useUtils();
  const theme = useTheme();

  const filters = useFilters();

  const { currentTab } = useNavContext();

  const debouncedNameFilter = useDebounce(filters.state.name, 1000);

  const { data: checklistCountResponse } = api.category.countStatus.useQuery(
    {
      categoryId: undefined,
    },
    {
      enabled: !!org.id && env.NEXT_PUBLIC_ENABLE_DILIGENT_CHECKLIST === "true",
    },
  );

  const {
    data: documents,
    isLoading,
    isSuccess,
  } = api.document.getAll.useQuery(
    {
      status: [
        DocumentStatus.PENDING,
        DocumentStatus.PROCESSING,
        DocumentStatus.READY,
        DocumentStatus.ERROR,
        DocumentStatus.GENERATING_ANSWERS,
      ],
      folder: currentPath,
      documentSource: documentSourceView,
      nameFilter: debouncedNameFilter,
      startDate: filters.state.startDate?.toDate(),
      endDate: filters.state.endDate?.toDate(),
      tagIds: filters.state.tags.map((tag) => tag.id),
      fundIds: filters.state.funds.map((fund) => fund.id),
      categoryIds: filters.state.categories.map((category) => category.id),
      filterResponse: true,
    },
    {
      enabled: !!org.id,
    },
  );

  const refreshInterval = process.env.NODE_ENV === "production" ? 5000 : 60000;

  // Check if the document.getAll query is currently fetching
  const isFetchingDocuments = useIsFetching({
    queryKey: getQueryKey(api.document.getAll, undefined, "query"),
  });

  useEffect(() => {
    // Only set up the interval if the query is successful and enabled
    if (!isSuccess || !org.id) {
      return;
    }

    const intervalId = setInterval(async () => {
      // Skip refresh if the query is currently fetching to avoid race conditions
      if (isFetchingDocuments > 0) {
        console.log("Skipping refresh - query is already fetching");
        return;
      }

      console.log("Refreshing documents...");
      await utils.document.getAll.invalidate(undefined, {
        type: "active",
      });
    }, refreshInterval);

    return () => {
      console.log("Cleaning up refresh interval");
      clearInterval(intervalId);
    };
  }, [
    isSuccess,
    org.id,
    utils.document.getAll,
    refreshInterval,
    isFetchingDocuments,
  ]);

  useEffect(() => {
    if (checklistCountResponse) {
      const progress = checklistCountResponse.total
        ? ((checklistCountResponse.linked ?? 0) /
            checklistCountResponse.total) *
          100
        : 0;
      setChecklistProgress(progress);
    }
  }, [checklistCountResponse]);

  const tags = api.tag.getAllTags.useQuery(undefined, {
    enabled: !!org.id,
  });

  const funds = api.fund.getAllFunds.useQuery(undefined, {
    enabled: !!org.id,
  });

  const categories = api.category.getAllCategories.useQuery(
    { status: CategoryStatus.ACTIVE },
    {
      enabled: !!org.id,
    },
  );

  const deleteDocuments = api.document.delete.useMutation();
  const sharepointRefresh = api.azure.indexAllFiles.useMutation();
  const sharepointSync = api.azure.syncSharepoint.useMutation();

  const vectorizeDocuments = api.document.vectorize.useMutation();
  const generateAnswers = api.document.generateAnswers.useMutation();
  const classifyDocuments = api.document.classifyDocuments.useMutation();
  const openDateRange = useBoolean();

  const confirm = useBoolean();

  const handleDeleteItems = useCallback(() => {
    const toastId = toast.loading("Delete in progress");

    const deleteInput: DeleteInputType = {
      list: rowSelectionModel
        .filter((row) => !(row as string).includes("auto-generated-row"))
        .map((entry) => ({
          id: entry.toString(),
          name: documents?.find((doc) => doc.id === entry)?.name || "",
        })),
    };

    deleteDocuments.mutate(deleteInput, {
      onSuccess: () => {
        toast.success("Delete success!", {
          id: toastId,
        });
      },
      onError: (error) => {
        toast.error("Delete failed!", {
          id: toastId,
        });
      },
    });
  }, [rowSelectionModel]);

  useEffect(() => {
    setDocumentSourceView(
      currentTab === "egnyte"
        ? DocumentSource.EGNYTE
        : currentTab === "intralinks"
          ? DocumentSource.INTRALINKS
          : currentTab === "sharepoint"
            ? DocumentSource.SHAREPOINT
            : currentTab === "local"
              ? DocumentSource.LOCAL
              : undefined,
    );
  }, [currentTab]);

  const handleSharepointRefresh = () => {
    setSharepointRefreshLoading(true);

    sharepointRefresh.mutate(
      { path: "/" },
      {
        onSuccess: () => {
          setSharepointRefreshLoading(false);
        },
        onError: (error) => {
          toast.error("Unable to retrieve Sharepoint files");
          setSharepointRefreshLoading(false);
        },
        onSettled: () => {
          setSharepointRefreshLoading(false);
        },
      },
    );
  };

  const handleSyncSharepoint = (latest: boolean = false) => {
    setSharepointSyncLoading(true);

    sharepointSync.mutate(
      { latest },
      {
        onSuccess: () => {
          setSharepointSyncLoading(false);
        },
        onError: (error) => {
          toast.error("Unable to sync Sharepoint files");
          setSharepointSyncLoading(false);
        },
        onSettled: () => {
          setSharepointSyncLoading(false);
        },
      },
    );
  };

  const selectedDocuments = useMemo(
    () =>
      rowSelectionModel
        .filter((row) => !(row as string).includes("auto-generated-row"))
        .map((id) => {
          const doc = documents?.find((doc) => doc.id === id);
          return {
            id,
            name: doc?.name ?? "",
            status: doc?.status,
            ddqStatus: doc?.ddqStatus,
            tags: doc?.tags,
            funds: doc?.funds,
          };
        }),
    [rowSelectionModel, documents],
  );

  const handleClassify = () => {
    setProcessLoading(true);

    classifyDocuments.mutate(
      { ids: selectedDocuments.map((doc) => doc.id.toString()) },
      {
        onSuccess: () => {
          setProcessLoading(false);
        },
        onError: (error) => {
          toast.error("Documents classifying failed", {
            id: "classifying-documents",
          });
          console.error(error);
          setProcessLoading(false);
        },
      },
    );
  };

  const handleProcess = ({
    skipReady,
    onlyDDQs,
  }: {
    skipReady?: boolean;
    onlyDDQs?: boolean;
  } = {}) => {
    setProcessLoading(true);

    console.log("documents", documents);
    console.log("rowSelectionModel", rowSelectionModel);

    const processingDocuments = selectedDocuments.filter((doc) => {
      console.log("doc", doc);

      if (skipReady) {
        return doc.status !== DocumentStatus.READY;
      }

      if (onlyDDQs) {
        return doc.ddqStatus !== null;
      }

      return true;
    });

    toast.info(`Processing selected documents`, {
      id: "processing-documents",
    });

    console.log("selectedDocuments", processingDocuments);

    vectorizeDocuments.mutate(
      processingDocuments.map((doc) => ({
        id: doc.id.toString(),
        name: doc.name,
      })),
      {
        onSuccess: async (data) => {
          setProcessLoading(false);
          await utils.tag.getAllTags.invalidate();
          await utils.fund.getAllFunds.invalidate();
        },

        onError: (error) => {
          toast.error("Documents processing failed", {
            id: "processing-documents",
          });
          console.error(error);
          setProcessLoading(false);
        },
      },
    );
  };

  const handleGenerateAnswers = useCallback(
    ({
      onlyErrored,
      onlyEmpty,
    }: {
      onlyErrored?: boolean;
      onlyEmpty?: boolean;
    } = {}) => {
      setProcessLoading(true);

      console.log("rowSelectionModel", rowSelectionModel);

      const selectedDocuments = rowSelectionModel
        .filter((row) => !(row as string).includes("auto-generated-row"))
        .map((id) => ({
          id,
          name: documents?.find((doc) => doc.id === id)?.name ?? "",
        }));

      toast.info(`Generating answers for selected documents`, {
        id: "generating-answers",
      });

      console.log("selectedDocuments", selectedDocuments);

      generateAnswers.mutate(
        {
          documents: selectedDocuments.map((doc) => ({
            id: doc.id.toString(),
            name: doc.name,
          })),
          onlyErroredResponses: onlyErrored,
          onlyEmptyAnswers: onlyEmpty,
        },
        {
          onSuccess: async (data) => {
            setProcessLoading(false);
          },

          onError: (error) => {
            toast.error("Documents generating answers failed", {
              id: "generating-answers",
            });
            console.error(error);
            setProcessLoading(false);
          },
        },
      );
    },
    [rowSelectionModel],
  );

  const renderFilters = (
    <div className="flex flex-col flex-1 md:flex-row items-start md:items-center gap-2">
      <FileManagerFilters
        filters={filters}
        dateError={false}
        onResetPage={() => {}}
        openDateRange={openDateRange.value}
        onOpenDateRange={openDateRange.onTrue}
        onCloseDateRange={openDateRange.onFalse}
        options={{
          types: FILE_TYPE_OPTIONS,
          tags:
            tags.data?.map((tag) => ({
              name: tag.name,
              id: tag.id,
              color: tag.color,
            })) ?? [],
          funds:
            funds.data?.map((fund) => ({
              name: fund.name,
              id: fund.id,
              color: "#000000",
            })) ?? [],
          categories:
            categories.data?.map((category) => ({
              name: category.name,
              id: category.id,
            })) ?? [],
        }}
      />

      <FundsAndTagsFilterPopup
        tags={tags.data}
        funds={funds.data}
        categories={categories.data}
        filters={filters}
        buttonText="Filters"
      />

      {currentTab === "sharepoint" && (
        <Protect permission="org:integrations:manage" fallback={<></>}>
          <Button
            variant="contained"
            onClick={() => handleSharepointRefresh()}
            loading={sharepointRefreshLoading}
            disabled={!org.azureAccessTokenId}
          >
            Reindex
          </Button>
          <Button
            variant="contained"
            onClick={() => handleSyncSharepoint(false)}
            loading={sharepointSyncLoading}
            disabled={!org.azureAccessTokenId}
          >
            Sync All
          </Button>
          <Button
            variant="contained"
            onClick={() => handleSyncSharepoint(true)}
            loading={sharepointSyncLoading}
            disabled={!org.azureAccessTokenId}
          >
            Sync Latest
          </Button>
        </Protect>
      )}

      {currentTab === "local" && (
        <Button
          variant="contained"
          color="error"
          onClick={() => handleDeleteItems()}
          loading={processLoading}
          disabled={rowSelectionModel.length === 0}
        >
          Delete
        </Button>
      )}
    </div>
  );

  const orgNameToSlug = (name: string) => {
    return name.toLowerCase().replace(/ /g, "-");
  };
  const upload = useBoolean();

  const [openCheckList, setOpenCheckList] = useState(false);

  const progressColor = (value: number) => {
    if (value < 99) {
      return "bg-warning";
    }
    return "bg-success";
  };

  console.log("currentPath", currentPath);

  const nestedFolders = rowSelectionModel
    .filter((row) => row.toString().includes("auto-generated-row-null"))
    .map((row) => {
      const pathName =
        currentPath + "/" + row.toString().split("/").slice(1).join("/");
      return pathName;
    });

  const { data: documentsIds } = api.document.getAllIdsForFolder.useQuery(
    {
      folders: nestedFolders,
      documentSource: documentSourceView,
    },
    {
      enabled: nestedFolders.length > 0, // Only run query when there are folders to query
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    },
  );

  const nestedFileIdsMap = useRef<Map<string, GridRowSelectionModel>>(
    new Map(),
  );

  // Function to clean up nested file IDs when folders are deselected
  const cleanupNestedFileIds = useCallback(
    (
      deselectedFolderIds: GridRowSelectionModel,
      newSelection: GridRowSelectionModel,
    ) => {
      console.log("cleanupNestedFileIds called with:", {
        deselectedFolderIds,
        newSelection,
      });
      let cleanedSelection = [...newSelection];

      deselectedFolderIds.forEach((folderId) => {
        if (folderId.toString().includes("auto-generated-row-null")) {
          const folderPath = folderId
            .toString()
            .replace("auto-generated-row-null", "");
          const fullPath = currentPath + folderPath;

          console.log("Processing folder:", { folderPath, fullPath });

          // Get the nested file IDs for this specific folder from the map before deleting
          const nestedFileIdsForFolder = nestedFileIdsMap.current.get(fullPath);
          console.log("Found nested file IDs:", nestedFileIdsForFolder);

          // Remove from nestedFileIdsMap
          nestedFileIdsMap.current.delete(fullPath);

          if (nestedFileIdsForFolder && nestedFileIdsForFolder.length > 0) {
            const nestedFileIds = nestedFileIdsForFolder.map((id) =>
              id.toString(),
            );

            console.log("Filtering out nested file IDs:", nestedFileIds);
            cleanedSelection = cleanedSelection.filter(
              (id) => !nestedFileIds.includes(id.toString()),
            );
            console.log("Selection after filtering:", cleanedSelection);
          }
        }
      });

      return cleanedSelection;
    },
    [currentPath],
  );

  useEffect(() => {
    console.log("rowSelectionModel", rowSelectionModel);

    if (
      rowSelectionModel.some((row) =>
        row.toString().includes("auto-generated-row-null"),
      )
    ) {
      const allIds = [...rowSelectionModel, ...(documentsIds ?? [])];
      const filteredIds = allIds.filter(
        (id, index, self) => self.indexOf(id) === index,
      );
      setRowSelectionModel(filteredIds);

      // Store nested file IDs for each selected folder
      rowSelectionModel.forEach((row) => {
        if (row.toString().includes("auto-generated-row-null")) {
          const folderPath = row
            .toString()
            .replace("auto-generated-row-null", "");
          const fullPath = currentPath + folderPath;
          nestedFileIdsMap.current.set(fullPath, documentsIds ?? []);
          console.log(
            "Stored nested file IDs for folder:",
            fullPath,
            documentsIds,
          );
        }
      });
    } else if (rowSelectionModel.length === 0) {
      setRowSelectionModel([]);
    } else {
      nestedFileIdsMap.current.set(
        currentPath,
        rowSelectionModel.map((row) => row.toString()),
      );
    }
  }, [
    documentsIds,
    rowSelectionModel.some((row) =>
      row.toString().includes("auto-generated-row-null"),
    ),
    currentPath,
  ]);

  // Clean up nestedFileIdsMap when currentPath changes
  useEffect(() => {
    // Remove entries for paths that are no longer relevant
    const currentPathParts = currentPath.split("/").filter(Boolean);
    for (const [path, _] of nestedFileIdsMap.current.entries()) {
      const pathParts = path.split("/").filter(Boolean);
      // If the stored path is not a subpath of current path, remove it
      if (
        !pathParts.every(
          (part: string, index: number) => currentPathParts[index] === part,
        )
      ) {
        nestedFileIdsMap.current.delete(path);
      }
    }
  }, [currentPath]);

  return (
    <>
      <DashboardHeader
        title="Data Room"
        tabs={TABS}
        actions={
          <>
            {env.NEXT_PUBLIC_ENABLE_DILIGENT_CHECKLIST === "true" && (
              <>
                <Button
                  variant="soft"
                  className="flex items-center gap-1"
                  onClick={() => setOpenCheckList(true)}
                >
                  <div
                    className={cn(
                      "w-1.5 h-1.5 rounded-full",
                      progressColor(Math.round(checklistProgress)),
                    )}
                  ></div>
                  {Math.round(checklistProgress) === 100
                    ? "Diligence Progress: Complete"
                    : "Diligence Progress: Incomplete"}
                </Button>
              </>
            )}

            <Button
              // className="!text-[10px]"
              color="primary"
              variant="contained"
              onClick={upload.onTrue}
            >
              Upload File
            </Button>
          </>
        }
      >
        <div className="flex w-full items-center gap-2 pt-4">
          {renderFilters}
        </div>
      </DashboardHeader>
      <DashboardContent>
        <BreadCrumbs
          currentPath={currentPath}
          setCurrentPath={setCurrentPath}
        />

        <FileExplorer
          documents={documents}
          count={documents?.length}
          loading={isLoading}
          source={documentSourceView}
          rowSelectionModel={rowSelectionModel}
          setRowSelectionModel={async (newRowSelectionModel) => {
            console.log("=== DESELECT DEBUG ===");
            console.log("Current selection:", rowSelectionModel);
            console.log("New selection:", newRowSelectionModel);

            // Find deselected folder IDs (auto-generated-row-null)
            const deselectedFolders = rowSelectionModel.filter(
              (id) =>
                id.toString().includes("auto-generated-row-null") &&
                !newRowSelectionModel.includes(id),
            );

            console.log("Deselected folders:", deselectedFolders);
            console.log(
              "nestedFileIdsMap contents:",
              Array.from(nestedFileIdsMap.current.entries()),
            );

            // Clean up nested file IDs for deselected folders and get cleaned selection
            let finalSelection = newRowSelectionModel;
            if (deselectedFolders.length > 0) {
              console.log("Calling cleanupNestedFileIds...");
              finalSelection = cleanupNestedFileIds(
                deselectedFolders,
                newRowSelectionModel,
              );
              console.log("Final selection after cleanup:", finalSelection);
            }

            setRowSelectionModel(finalSelection);
            await utils.document.getAllIdsForFolder.invalidate();
          }}
          currentPath={currentPath}
          setCurrentPath={setCurrentPath}
        />

        <ConfirmDialog
          open={confirm.value}
          onClose={confirm.onFalse}
          title="Delete"
          content={<>Are you sure want to delete </>}
          action={
            <Button
              variant="contained"
              color="error"
              onClick={() => {
                confirm.onFalse();
              }}
            >
              Delete
            </Button>
          }
        />
      </DashboardContent>

      <FileManagerNewFolderDialog
        open={upload.value}
        onClose={upload.onFalse}
        orgId={org.id}
        orgSlug={orgNameToSlug(org.name)}
      />

      {/* Bottom bar for process actions */}
      {selectedDocuments.length > 0 && (
        <div className="bg-white border-t border-gray-200 p-3 flex items-center gap-2 shadow-[0_-2px_8px_rgba(0,0,0,0.3)]">
          <Typography variant="h5" className="flex-1">
            {selectedDocuments.length} item
            {selectedDocuments.length > 1 ? "s" : ""} selected
          </Typography>
          <ApplyTagsModal
            selectedDocuments={selectedDocuments.map((doc) => ({
              id: doc.id.toString(),
              name: doc.name,
              tags: doc.tags ?? [],
            }))}
            onClose={() => {
              // Refresh the documents list after applying tags
              utils.document.getAll.invalidate();
            }}
          />
          <ApplyFundsModal
            selectedDocuments={selectedDocuments.map((doc) => ({
              id: doc.id.toString(),
              name: doc.name,
              funds: doc.funds ?? [],
            }))}
            onClose={() => {
              // Refresh the documents list after applying funds
              utils.document.getAll.invalidate();
            }}
          />
          <ProcessActionButtons
            processLoading={processLoading}
            onProcess={handleProcess}
            onGenerateAnswers={handleGenerateAnswers}
            onClassify={handleClassify}
          />
        </div>
      )}

      <ChecklistDialog
        open={openCheckList}
        setOpen={setOpenCheckList}
        org={org}
      />
    </>
  );
}
