import dayjs from "dayjs";
import { debounce } from "lodash";
import { type FC, useEffect, useMemo, useState } from "react";
import Loading from "~/app/dashboard/loading";
import { DashboardContent, DashboardHeader } from "~/layouts/Dashboard";
import { api } from "~/trpc/react";
import { downloadExcelFile } from "~/utils/excelExport";
import { Input } from "~/v2/components";
import { Button } from "~/v2/components/ui/Button";
import { Typography } from "~/v2/components/ui/Typography";
import AddCategoryDialog from "./AddCategoryDialog";
import CategorySection from "./CategorySection";

const DiligenceChecklistView: FC = () => {
  // const filterModeOptions = [
  //   {
  //     label: "All",
  //     value: "ALL",
  //   },
  //   {
  //     label: "Missing only",
  //     value: "MISSING_ONLY",
  //   },
  //   {
  //     label: "Active only",
  //     value: "ACTIVE_ONLY",
  //   },
  // ];
  const [filterMode, setFilterMode] = useState<
    "MISSING_ONLY" | "ALL" | "ACTIVE_ONLY"
  >("ALL");
  const [searchTerm, setSearchTerm] = useState<string>("");

  const {
    data: categoryResponse,
    isLoading,
    error,
    refetch,
  } = api.category.getCategoryTreeWithDocuments.useQuery({
    filterMode: filterMode,
  });

  const createCategory = api.category.createCategory.useMutation();
  const exportCategories = api.category.exportCategories.useMutation();

  const [categories, setCategories] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [creating, setCreating] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  useEffect(() => {
    if (categoryResponse) {
      setCategories(categoryResponse);
    }
  }, [categoryResponse]);

  // Create debounced search handler using lodash
  const debouncedSetSearchTerm = useMemo(
    () => debounce((value: string) => setSearchTerm(value), 300),
    [],
  );

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSetSearchTerm.cancel();
    };
  }, [debouncedSetSearchTerm]);

  // Filter categories based on search term
  const filteredCategories = useMemo(() => {
    if (!searchTerm.trim()) return categories;

    return categories
      .map((category: any) => {
        const filteredChildren =
          category.children?.filter((child: any) =>
            child.name.toLowerCase().includes(searchTerm.toLowerCase()),
          ) || [];

        return {
          ...category,
          children: filteredChildren,
        };
      })
      .filter((category: any) => {
        if (category.children && category.children.length > 0) {
          return true;
        }

        return category.name.toLowerCase().includes(searchTerm.toLowerCase());
      });
  }, [categories, searchTerm]);

  const handleAddCategory = (form: {
    name: string;
    description?: string;
    staleAfterDays?: number;
    parentId?: string;
  }) => {
    setFormError(null);
    setCreating(true);
    void createCategory.mutate(form, {
      onSuccess: () => {
        setOpen(false);
        refetch();
      },
      onError: (err) => {
        setFormError(err.message || "Error creating category");
      },
      onSettled: () => {
        setCreating(false);
      },
    });
  };

  const handleExport = () => {
    void exportCategories.mutate(undefined, {
      onSuccess: (data) => {
        downloadExcelFile(data, `diligence_checklist_${dayjs().unix()}`);
      },
    });
  };

  return (
    <>
      <DashboardHeader
        title="Diligence"
        actions={
          <>
            <AddCategoryDialog
              open={open}
              setOpen={setOpen}
              onSubmit={handleAddCategory}
              loading={creating}
              error={formError}
            />
            <div className="flex flex-col gap-2">
              <div className="flex flex-row gap-2">
                <Button variant="outline" size="sm" onClick={handleExport}>
                  Export
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setOpen(true)}
                >
                  Add category
                </Button>
              </div>
            </div>
          </>
        }
      >
        <Input
          placeholder="Search categories..."
          onChange={(e) => {
            const value = e.target.value;
            // setInputValue(value);
            debouncedSetSearchTerm(value);
          }}
        />
      </DashboardHeader>
      <DashboardContent>
        {isLoading && <Loading />}
        {error && (
          <Typography variant="body" className="text-red-500">
            Error loading categories
          </Typography>
        )}
        {!isLoading && !categories?.length && (
          <div className="flex flex-col gap-2 text-center">
            <Typography variant="body">No categories found.</Typography>
          </div>
        )}

        {!isLoading && searchTerm.trim() && !filteredCategories?.length && (
          <div className="flex flex-col gap-2 text-center">
            <Typography variant="body">
              No categories or checklist items match your search &quot;
              {searchTerm}&quot;.
            </Typography>
          </div>
        )}

        {filteredCategories?.map((category: any) => (
          <article
            key={category.id}
            className="mb-2 pb-2 border-b border-gray-200"
          >
            <CategorySection
              category={category}
              allCategories={categories}
              onRefetch={refetch}
              filterMode={filterMode}
            />
          </article>
        ))}
      </DashboardContent>
    </>
  );
};

export default DiligenceChecklistView;
