import {
  Card,
  CardContent,
  CardFooter,
} from "~/v2/components/ui/Card";
import { Badge } from "~/v2/components/ui/Badge";
import { Typography } from "~/v2/components/ui/Typography";
import Divider from "@mui/material/Divider";
import Tooltip from "@mui/material/Tooltip";
import { QuestionCategory } from "@prisma/client";
import Highlighter from "react-highlight-words";
import ResponseStatusLabel from "~/components/virgil/Response/ResponseStatusLabel";
import { type ResponseContentType } from "~/lib/types";
import { type QuestionWithSearchType } from "~/server/api/routers/question.common";
import { type GetTagEntityConnectionsType } from "~/server/api/routers/tag";
import { fDateTime } from "~/utils/format-time";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useTheme } from "@mui/material/styles";
import { useResponseLibraryContext } from "./context/ResponseLibraryContext";
import { cn, getFilename } from "~/v2/lib/utils";
import { Clock, File } from "lucide-react";
import { useMemo } from "react";
import { Tooltip as TooltipV2, TooltipContent, TooltipTrigger } from "~/v2/components/ui/Tooltip";

type Props = {
  question: QuestionWithSearchType;
  tagEntityConnections: GetTagEntityConnectionsType[];
  search?: string | undefined;
  accordion?: boolean;
  index?: number;
  expanded?: string | null;
  setExpanded?: (expanded: string | null) => void;
  similarQuestionCount?: number;
  actions?: React.ReactNode;
  allowEdit: boolean;
};

export function getContrastColor(hexColor: string): "white" | "black" {
  // Remove the hash at the start if it's there
  const color = hexColor.replace(/^#/, "");

  // Convert 3-digit hex to 6-digit hex if necessary
  const hex =
    color.length === 3
      ? color
        .split("")
        .map((char) => char + char)
        .join("")
      : color;

  // Parse the r, g, b values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Calculate the perceived brightness (ranges from 0 to 255)
  // Using the formula: brightness = (red * 299 + green * 587 + blue * 114) / 1000
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;

  // If the background is bright, use black text. Otherwise, use white text.
  return brightness > 200 ? "black" : "white";
}

export const formatQuestionCategory = (category: QuestionCategory) => {
  switch (category) {
    case QuestionCategory.DATA_ASSURANCE:
      return "Data Assurance";
    case QuestionCategory.STANDARDS_AND_FRAMEWORKS:
      return "Standards & Frameworks";
    case QuestionCategory.INVESTMENT_PROCESS:
      return "Investment Process";
    default:
      return "Other";
  }
};

export function QuestionDetails({
  question,
  tagEntityConnections,
  search,
  accordion = false,
  index,
  expanded,
  setExpanded,
  similarQuestionCount,
  actions,
  allowEdit,
}: Props) {
  const theme = useTheme();

  const questionContent = question?.questionContents[0]
    ?.content as ResponseContentType;

  const responseContent = question?.response?.responseContents[0]
    ?.content as ResponseContentType;

  const matchScore = useMemo(() => {
    if (typeof question.searchResult?.question_distance != "number" || typeof question.searchResult?.relevance_score != "number") {
      return null;
    }

    const question_distance = 1 - (question.searchResult?.question_distance ?? 1);
    const relevance_score = question.searchResult?.relevance_score ?? 0;

    const questionDistanceScore = Number((question_distance * 100).toFixed(2));
    const relevanceScore = Number((relevance_score * 100).toFixed(2));

    return {
      highestScore: Math.max(questionDistanceScore, relevanceScore),
      scoreType: question_distance > relevance_score ? "question_distance" : "relevance_score",
    }

  }, []);


  const ddqNames =
    question?.response?.documents?.map((doc: any) => getFilename(doc.document.name)).filter(Boolean) ??
    [];

  const ddqCount = question?.response?.documents?.length ?? 0;

  const usedInDDQs = ddqCount > 0 ? (
    <TooltipV2>
      <TooltipTrigger>
        Used in {ddqCount} DDQ{ddqCount === 1 ? "" : "s"}
      </TooltipTrigger>
      <TooltipContent>
        {ddqNames.map((name: string, index: number) => (
          <div key={index}>{name}</div>
        ))}
      </TooltipContent>
    </TooltipV2>
  ) : (
    <Typography variant="body" className="text-gray-500 text-sm">Not used</Typography>
  );

  const renderResponseContents = (
    <>
      <div
        data-testid="question-details-header-content"
        className={`flex flex-col items-start ${accordion ? "w-full" : "w-4/5"}`}
      >
        {questionContent.text && (
          <Typography
            data-testid="question-details-header-title"
            variant="h6"
            className="text-sm"
          >
            {questionContent.text}
          </Typography>
        )}
      </div>
      <div className="flex flex-row items-center justify-end mt-0.5">
        {setExpanded && accordion && (similarQuestionCount ?? 0) > 0 && (
          <Badge
            data-testid="view-similar-questions-label"
            variant="secondary"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              setExpanded?.(expanded === question.id ? null : question.id);
            }}
          >
            {expanded === question.id ? "Hide" : "View"} {similarQuestionCount}{" "}
            Similar Questions
          </Badge>
        )}
      </div>
    </>
  );

  const renderResponseSnippet = (
    <Typography
      variant="body"
      className="text-sm"
    >
      {accordion ? (
        <div className="markdown-body">
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {responseContent?.text}
          </ReactMarkdown>
        </div>
      ) : (
        <Highlighter
          highlightStyle={{
            backgroundColor: "yellow",
            fontWeight: "bold",
          }}
          searchWords={[search ?? ""]}
          autoEscape={true}
          textToHighlight={responseContent?.text ?? ""}
        />
      )}
    </Typography>
  );

  const renderResponseDetails = (
    <div className="flex flex-row items-center justify-between w-full">
      <div className="flex flex-row items-center">
        {question?.tags.map((t, i) => (
          <Tooltip
            key={i}
            title={
              tagEntityConnections.find((c) => c.tagId === t.id)?.connectionReason
            }
          >
            <Badge
              key={t.id}
              variant="outline"
              style={{
                backgroundColor: t.color,
                color: getContrastColor(t.color),
              }}
            >
              {t.name}
            </Badge>
          </Tooltip>
        ))}
        {question?.tags.length === 0 && (
          <Badge variant="secondary">
            No tags
          </Badge>
        )}
        {question?.response?.status && (
          <ResponseStatusLabel responseStatus={question?.response?.status} />
        )}
        <div className="text-black/15 select-none">|</div>
        <div className="flex flex-row items-center gap-1">
          <File size={16} />
          {usedInDDQs}
        </div>
        <div className="text-black/15 select-none">|</div>
        <div className="flex flex-row items-center gap-1 text-gray-500 text-sm">
          <Clock size={16} />
          <Typography variant="body">{`Last updated ${fDateTime(question?.response?.updatedAt)}`}</Typography>
        </div>
        <div className="text-black/15 select-none">|</div>
        {
          matchScore && (
            <Tooltip title={`The ${matchScore?.scoreType === "question_distance" ? "question" : "response"} most closely matches the search query.`}>
              <Badge variant="default" className="bg-lime-400 text-black font-semibold">
                {matchScore?.highestScore}%
              </Badge>
            </Tooltip>
          )
        }
      </div>
      <div className="flex-shrink-0">
        {actions}
      </div>
    </div>
  );

  return (
    <>
      {accordion ? (
        <Accordion
          sx={{
            "& .MuiButtonBase-root.Mui-expanded": {
              minHeight: 0,
            },
            "& .MuiButtonBase-root.Mui-expanded .only-collapsed": {
              display: "none"
            },
            border: "0.5px solid grey.200",
            borderLeft:
              expanded === question.id
                ? `2px solid ${theme.palette.primary.main}`
                : "inherit",
          }}
          defaultExpanded={index === 0}
        >
          <AccordionSummary
            id={question.id}
            sx={{
              cursor: "pointer",
              backgroundColor: "grey.50",
              "& .MuiAccordionSummary-content.Mui-expanded": {
                paddingTop: "10px",
                margin: "0",
              },
            }}
          >
            <div className="flex flex-col">
              <Typography>{renderResponseContents}</Typography>
              <div className="only-collapsed line-clamp-3 markdown-body">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {responseContent?.text}
                </ReactMarkdown>
              </div>
            </div>
          </AccordionSummary>
          <AccordionDetails>
            {renderResponseSnippet}
            <Divider sx={{ borderStyle: "dashed" }} />
            {renderResponseDetails}
          </AccordionDetails>
        </Accordion>
      ) : (
        <ResponseCard
          question={question}
          renderResponseContents={renderResponseContents}
          renderResponseSnippet={renderResponseSnippet}
          renderResponseDetails={renderResponseDetails}
          allowEdit={allowEdit}
        />
      )}
    </>
  );
}

const ResponseCard = ({
  question,
  renderResponseContents,
  renderResponseSnippet,
  renderResponseDetails,
  allowEdit,
}: {
  question: QuestionWithSearchType;
  renderResponseContents: React.ReactNode;
  renderResponseSnippet: React.ReactNode;
  renderResponseDetails: React.ReactNode;
  allowEdit: boolean;
}) => {
  const filters = useResponseLibraryContext();
  const isSelected = filters.state.selectedQuestionId === question.id;

  return (
    <Card
      onClick={() => {
        if (isSelected) {
          filters.setState({ selectedQuestionId: null, allowEdit: false });
        } else {
          filters.setState({ selectedQuestionId: question.id, allowEdit });
        }
      }}
      className={cn("py-0 gap-0 cursor-pointer", isSelected && "border-l-5 border-l-primary")}
    >
      <CardContent className={cn("px-2 py-3 m-0", isSelected && "bg-primary/5")}>
        {renderResponseContents}
        {renderResponseSnippet}
      </CardContent>
      <CardFooter className={cn("px-2 py-3 border-t-3 border-t-transparent", isSelected ? "bg-primary/10" : "border-t-black/10")}>
        {renderResponseDetails}
      </CardFooter>
    </Card>
  );
};
