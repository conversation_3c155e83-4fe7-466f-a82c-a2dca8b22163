import { useMemo } from "react";
import { api } from "~/trpc/react";
import { FilterPopup } from "./FilterPopup";
import { useResponseLibraryContext } from "../context/ResponseLibraryContext";
import { SearchField } from "./SearchField";

export const Filters = () => {

    const ddqs = api.document.getAllDDQs.useQuery({});
    const tags = api.tag.getAllTags.useQuery(undefined);
    const funds = api.fund.getAllFunds.useQuery(undefined);

    const DDQ_FILTERS = useMemo(
        () =>
            ddqs.data?.map((ddq) => ({
                name: ddq.name,
                id: ddq.id,
            })) ?? [],
        [ddqs.data],
    );

    return (
        <div className="flex flex-col md:flex-row items-end md:items-center gap-1 w-full mt-4">
            <SearchField />

            <FilterPopup
                tags={tags.data}
                funds={funds.data}
                ddqs={DDQ_FILTERS}
            />
        </div>
    );
};