import { FilterIcon } from "lucide-react";
import { useEffect, useState } from "react";

import { type Fund, type Tag, ResponseStatus } from "@prisma/client";

import {
  MultiSelect,
  MultiSelectClear,
  MultiSelectCommand,
  MultiSelectCommandItem,
  MultiSelectCommandList,
  MultiSelectContent,
  MultiSelectItem,
  MultiSelectList,
  MultiSelectTrigger,
  MultiSelectValues,
} from "~/v2/components/composit/MultiSelect/MultiSelect";
import { Badge } from "~/v2/components/ui/Badge";
import { Button } from "~/v2/components/ui/Button";
import { CardContent } from "~/v2/components/ui/Card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/v2/components/ui/Popover";
import { Typography } from "~/v2/components/ui/Typography";
import { useResponseLibraryContext } from "../context/ResponseLibraryContext";

export const FilterPopup = ({
  tags,
  funds,
  ddqs,
}: {
  tags: Tag[] | undefined;
  funds: Fund[] | undefined;
  ddqs: { name: string; id: string }[] | undefined;
}) => {
  const filters = useResponseLibraryContext();
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedFunds, setSelectedFunds] = useState<string[]>([]);
  const [selectedDDQs, setSelectedDDQs] = useState<string[]>([]);
  const [selectedResponseStatuses, setSelectedResponseStatuses] = useState<
    string[]
  >([]);
  const [open, setOpen] = useState(false);

  // Initialize state from current filters
  useEffect(() => {
    setSelectedTags(filters.state.tags.map((tag) => tag.id));
    setSelectedFunds(filters.state.funds.map((fund) => fund.id));
    setSelectedDDQs(filters.state.ddq.map((ddq) => ddq.id));
    setSelectedResponseStatuses(filters.state.responseStatus);
  }, [
    filters.state.tags,
    filters.state.funds,
    filters.state.ddq,
    filters.state.responseStatus,
  ]);

  const handleTagsChange = (selectedTagIds: string[]) => {
    setSelectedTags(selectedTagIds);
    filters.setState({
      tags: selectedTagIds
        .map((id) => {
          const tag = tags?.find((tag) => tag.id === id);
          return tag ? { name: tag.name, id: tag.id, color: tag.color } : null;
        })
        .filter((tag) => tag !== null),
    });
  };

  const handleDDQsChange = (selectedDDQIds: string[]) => {
    setSelectedDDQs(selectedDDQIds);
    filters.setState({
      ddq: selectedDDQIds
        .map((id) => {
          const ddq = ddqs?.find((ddq) => ddq.id === id);
          return ddq ? { name: ddq.name, id: ddq.id } : null;
        })
        .filter((ddq) => ddq !== null),
    });
  };

  const handleFundsChange = (selectedFundIds: string[]) => {
    setSelectedFunds(selectedFundIds);
    filters.setState({
      funds: selectedFundIds
        .map((id) => {
          const fund = funds?.find((fund) => fund.id === id);
          return fund
            ? { name: fund.name, id: fund.id, color: "#000000" }
            : null;
        })
        .filter((fund) => fund !== null),
    });
  };

  const handleResponseStatusChange = (selectedStatusIds: string[]) => {
    setSelectedResponseStatuses(selectedStatusIds);
    filters.setState({
      responseStatus: selectedStatusIds as ResponseStatus[],
    });
  };

  const handleClear = () => {
    setSelectedTags([]);
    setSelectedFunds([]);
    setSelectedDDQs([]);
    setSelectedResponseStatuses([]);
    filters.setState({
      tags: [],
      funds: [],
      ddq: [],
      responseStatus: [],
    });
  };

  const tagOptions =
    tags?.map((tag) => ({ value: tag.id, label: tag.name })) ?? [];

  const fundOptions =
    funds?.map((fund) => ({ value: fund.id, label: fund.name })) ?? [];

  const ddqOptions =
    ddqs?.map((ddq) => ({ value: ddq.id, label: ddq.name })) ?? [];

  const responseStatusOptions = [
    { value: ResponseStatus.DRAFT, label: "Draft" },
    { value: ResponseStatus.PENDING_APPROVAL, label: "Pending Approval" },
    { value: ResponseStatus.APPROVED, label: "Approved" },
    { value: ResponseStatus.REJECTED, label: "Rejected" },
  ];

  const totalSelected =
    selectedTags.length +
    selectedFunds.length +
    selectedDDQs.length +
    selectedResponseStatuses.length;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="default"
          className="flex items-center gap-2"
        >
          <FilterIcon className="h-4 w-4" />
          Filters
          {totalSelected > 0 && (
            <Badge variant="secondary" className="ml-1">
              {totalSelected}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0 bg-white border-ra" align="start">
        <CardContent className="p-4 space-y-4">
          <div className="space-y-2">
            <Typography variant="h6" className="font-semibold">
              Response Status
            </Typography>
            <MultiSelect
              options={responseStatusOptions}
              selected={selectedResponseStatuses}
              onClose={handleResponseStatusChange}
              onChange={handleResponseStatusChange}
            >
              <MultiSelectTrigger>
                <MultiSelectValues placeholder="Select response statuses" />
              </MultiSelectTrigger>
              <MultiSelectContent>
                <MultiSelectList>
                  {responseStatusOptions.map((option) => (
                    <MultiSelectItem key={option.value} value={option.value}>
                      {option.label}
                    </MultiSelectItem>
                  ))}
                  <MultiSelectClear />
                </MultiSelectList>
              </MultiSelectContent>
            </MultiSelect>
          </div>

          <div className="space-y-2">
            <Typography variant="h6" className="font-semibold">
              DDQs
            </Typography>
            <MultiSelect
              options={ddqOptions}
              selected={selectedDDQs}
              onClose={handleDDQsChange}
            >
              <MultiSelectTrigger>
                <MultiSelectValues placeholder="Select DDQs" />
              </MultiSelectTrigger>
              <MultiSelectContent>
                <MultiSelectCommand>
                  <MultiSelectCommandList>
                    {ddqOptions.map((option) => (
                      <MultiSelectCommandItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </MultiSelectCommandItem>
                    ))}
                  </MultiSelectCommandList>
                  <MultiSelectClear />
                </MultiSelectCommand>
              </MultiSelectContent>
            </MultiSelect>
          </div>

          <div className="space-y-2">
            <Typography variant="h6" className="font-semibold">
              Funds
            </Typography>
            <MultiSelect
              options={fundOptions}
              selected={selectedFunds}
              onClose={handleFundsChange}
            >
              <MultiSelectTrigger>
                <MultiSelectValues placeholder="Select funds" />
              </MultiSelectTrigger>
              <MultiSelectContent>
                <MultiSelectCommand>
                  <MultiSelectCommandList>
                    {fundOptions.map((option) => (
                      <MultiSelectCommandItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </MultiSelectCommandItem>
                    ))}
                  </MultiSelectCommandList>
                  <MultiSelectClear />
                </MultiSelectCommand>
              </MultiSelectContent>
            </MultiSelect>
          </div>

          <div className="space-y-2">
            <Typography variant="h6" className="font-semibold">
              Tags
            </Typography>
            <MultiSelect
              options={tagOptions}
              selected={selectedTags}
              onClose={handleTagsChange}
            >
              <MultiSelectTrigger>
                <MultiSelectValues placeholder="Select tags" />
              </MultiSelectTrigger>
              <MultiSelectContent>
                <MultiSelectCommand>
                  <MultiSelectCommandList>
                    {tagOptions.map((option) => (
                      <MultiSelectCommandItem
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </MultiSelectCommandItem>
                    ))}
                  </MultiSelectCommandList>
                  <MultiSelectClear />
                </MultiSelectCommand>
              </MultiSelectContent>
            </MultiSelect>
          </div>

          <div className="flex gap-2 pt-2">
            <Button variant="outline" onClick={handleClear} className="flex-1">
              Clear
            </Button>
          </div>
        </CardContent>
      </PopoverContent>
    </Popover>
  );
};
