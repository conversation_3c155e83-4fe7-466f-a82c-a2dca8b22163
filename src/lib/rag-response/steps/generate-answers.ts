import { type JsonObject } from "@prisma/client/runtime/library";

import { generateAnswersByTableIds } from "~/lib/ddq";
import {
  GeneratedTableResponse,
  type GeneratedResponse,
} from "~/lib/rag-response";
import {
  markDocumentStatusGeneratingAnswers,
  markDocumentStatusReady,
} from "~/lib/vectorizer";
import { type PrismaClientType } from "~/server/db";
import { type ModelParameters } from "~/lib/types";
import { tracing, TracingContext } from "~/server/api/managers/tracingManager";
import {
  ExistingAnswerType,
  GetDocumentWithEmptyAnswersInclude,
  GetDocumentWithEmptyAnswersType,
  GetDocumentWithEmptyAnswersTypeForTables,
  GetDocumentWithEmptyAnswersIncludeForTables,
} from "~/server/api/routers/document";
import {
  overwriteExistingResponseContent,
  overwriteExistingTableResponseContent,
} from "~/server/api/routers/document";
import { DocumentType, WorkSheetTableStatus } from "@prisma/client";
import { addExistingAnswersToResponseContent } from "../utils/generate-answers-for-document";

export async function* processTablesInSpreadsheet(
  input: {
    db: PrismaClientType;
    orgId: string;
    userId: string;
    onlyEmptyAnswers: boolean;
    onlyErroredResponses: boolean;
    responseLibraryMatching: boolean;
    modelParameters: ModelParameters;
  },
  doc: { id: string; name: string },
  tracingContext?: TracingContext,
  secret?: Record<string, string>,
) {
  console.time(
    `processTablesInSpreadsheet: Generating tables for document ${doc.name}`,
  );

  if (!input.onlyErroredResponses) {
    await markDocumentStatusGeneratingAnswers({
      db: input.db,
      documentId: doc.id,
      orgId: input.orgId,
    });
  }

  const documentWithTablesAndEmptyAnswers: GetDocumentWithEmptyAnswersTypeForTables =
    await input.db.document.findFirstOrThrow({
      where: {
        id: doc.id,
        orgId: input.orgId,
      },
      include: GetDocumentWithEmptyAnswersIncludeForTables,
    });

  console.log(
    "documentWithTablesAndEmptyAnswers",
    documentWithTablesAndEmptyAnswers.worksheets.map((r) =>
      r.tables.map((t) => t.cells.map((c) => c.prompt)).flat(),
    ),
  );

  console.log("input.onlyEmptyAnswers", input.onlyEmptyAnswers);
  console.log("input.onlyErroredResponses", input.onlyErroredResponses);

  const tableIds = documentWithTablesAndEmptyAnswers.worksheets
    .map((worksheet) => worksheet.tables.map((table) => table.id))
    .flat();

  console.log(
    "tableIds",
    tableIds,
    tableIds.length,
    documentWithTablesAndEmptyAnswers.worksheets
      .filter((worksheet) => tableIds.includes(worksheet.id))
      .map((worksheet) => worksheet.tables.map((table) => table.id)),
  );

  const existingAnswers: ExistingAnswerType[] = [];

  const totalDocumentTables = tableIds.length;
  const totalExistingAnswers = existingAnswers.filter(
    (answer) => answer !== null,
  ).length;

  console.log(
    "totalDocumentTables",
    totalDocumentTables,
    "totalExistingAnswers",
    totalExistingAnswers,
  );

  // -----
  const batchSize =
    parseInt(
      secret?.NUMBER_OF_QUESTIONS_TO_PROCESS ??
        process.env.NUMBER_OF_QUESTIONS_TO_PROCESS ??
        "4",
    ) ?? 4;

  const tableIdsWithCustomPrompt = tableIds.map((q) => ({
    tableId: q,
    customPrompt: "",
  }));

  const batches = tableIdsWithCustomPrompt.reduce(
    (acc, curr, index) => {
      const batchIndex = Math.floor(index / batchSize);
      if (!acc[batchIndex]) {
        acc[batchIndex] = [];
      }
      acc[batchIndex].push(curr);
      return acc;
    },
    [] as { tableId: string; customPrompt: string }[][],
  );

  for (const batch of batches) {
    console.log(
      `processTablesInSpreadsheet: Generating answers for tables ${batch.map((b) => b.tableId)}`,
    );

    await input.db.workSheetTable.updateMany({
      where: {
        id: { in: batch.map((b) => b.tableId) },
      },
      data: { status: WorkSheetTableStatus.GENERATING_ANSWERS },
    });

    const answersIterator = generateAnswersByTableIds({
      db: input.db,
      orgId: input.orgId,
      documentId: doc.id,
      tableIdsWithCustomPrompt: batch,
      userId: input.userId,
      tracingContext,
      modelParameters: input.modelParameters,
      secret,
    });

    const answers: GeneratedTableResponse[] = [];
    for await (const {
      status,
      totalResponses,
      totalResponsesGenerated,
      responses,
    } of answersIterator) {
      console.log(
        "processTablesInSpreadsheet: Generated new answers",
        status,
        totalResponses,
        totalResponsesGenerated,
        responses?.length,
      );
      if (status === "error") {
        yield {
          status: "error",
          documentId: doc.id,
          totalResponses: totalResponses ?? 0,
        };
      }

      if (status === "generating_answers") {
        yield {
          status: "generating_answers",
          documentId: doc.id,
          totalResponses: totalResponses ?? 0,
          totalResponsesGenerated: totalResponsesGenerated ?? 0,
          responses: responses ?? [],
        };

        const answersAndResponses = responses.map((response) => ({
          answer: response?.answer ?? "",
          reason: response?.reason ?? "",
          citations: response?.citations ?? [],
          tableId: response?.tableId ?? "",
          response: documentWithTablesAndEmptyAnswers.worksheets.find((r) =>
            r.tables.find((t) => t.id === response?.tableId),
          ),
          cells: response?.cells ?? [],
        }));

        if (answersAndResponses.length > 0) {
          console.log(
            "processTablesInSpreadsheet: Overwriting existing response contents",
            answersAndResponses,
          );

          answersAndResponses.map((a) => {
            console.log(
              `Cells for response ${a.tableId}: ${JSON.stringify(
                a.cells.map((c) => ({
                  cellAddress: c.cellAddress,
                  cellValue: c.cellValue,
                })),
              )}`,
            );
          });

          console.time(
            `processTablesInSpreadsheet: Overwriting existing response contents for ${doc.name}. Total responses generated: ${totalResponsesGenerated}`,
          );
          const updatedResponseContents =
            await overwriteExistingTableResponseContent(
              input.db,
              answersAndResponses.map((r) => ({
                tableId: r.tableId,
                answer: r.answer,
                reason: r.reason,
                citations: r.citations,
                cells: r.cells,
              })),
              input.userId,
              input.orgId,
            );

          // console.log("updatedResponseContents", updatedResponseContents);
          console.timeEnd(
            `processTablesInSpreadsheet: Overwriting existing response contents for ${doc.name}. Total responses generated: ${totalResponsesGenerated}`,
          );
        }
      }

      if (status === "completed") {
        const validResponses = responses.filter(
          (r): r is GeneratedTableResponse => r !== null,
        );
        yield {
          status: "completed",
          documentId: doc.id,
          totalResponses: totalResponses ?? 0,
          totalResponsesGenerated: totalResponsesGenerated ?? 0,
          responses: validResponses,
        };
        answers.push(...validResponses);
      }
    }
  }

  // ----

  console.timeEnd(
    `processTablesInSpreadsheet: Generating tables for document ${doc.name}`,
  );
}
