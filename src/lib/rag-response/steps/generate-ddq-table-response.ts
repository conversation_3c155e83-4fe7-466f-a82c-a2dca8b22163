import type {
  DocumentChunkType,
  SingleDDQTableResponse,
} from "~/lib/rag-response/steps/types";
import type { ModelParameters } from "~/views/chat/ChatModelParametersSelector";
import {
  tracing,
  type TracingContext,
} from "~/server/api/managers/tracingManager";
import { env } from "~/env";
import {
  InvokeCommand,
  type InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import {
  AnswerDataMode,
  getAnswerGenDataMode,
} from "~/utils/answer_gen_data_mode";
import { getPyrpcApiUrl } from "~/utils/url";
import { backoffDelay } from "~/server/utils/backoff";

export async function generateDDQTableResponses(
  tablesInput: {
    tableId: string;
    instructions: string;
    tableTemplate: string;
    customPrompt?: string;
    existingTable?: string;
    funds: string[];
  }[],
  chunks: DocumentChunkType[],
  context: { institutionName: string },
  modelParameters: ModelParameters,
  secret?: Record<string, string>,
  tracingContext?: TracingContext,
): Promise<SingleDDQTableResponse[]> {
  const span = tracing?.trace({
    name: "generateDDQTableResponses",
    tracingContext,
    input: {
      tables: tablesInput.length,
      chunks: chunks.length,
    },
  });

  const nestedTracingContext = {
    ...tracingContext,
    parentSpanId: span?.id,
  };

  const tablePayload = await Promise.all(
    tablesInput.map(async (t) => ({
      table_id: t.tableId,
      table_template: t.tableTemplate,
      instructions: t.instructions,
      context,
      custom_prompt: t.customPrompt,
      existing_answer: t.existingTable ?? "",
      funds: t.funds.join(","),
    })),
  );

  console.time(`generateDDQTableResponses-${tablesInput.length}`);

  const MAX_RETRIES = 7;
  let retries = 0;

  while (retries < MAX_RETRIES) {
    try {
      let responseJson: SingleDDQTableResponse[] = [];

      if (process.env.NODE_ENV === "production") {
        const lambdaParams = {
          region: env.AWS_LAMBDA_REGION,
          credentials: process.env.AWS_LAMBDA_ACCESS_KEY
            ? {
                accessKeyId: env.AWS_LAMBDA_ACCESS_KEY,
                secretAccessKey: env.AWS_LAMBDA_SECRET_ACCESS_KEY,
              }
            : undefined,
        };

        // Execute Python Lambda
        const lambdaClient = new LambdaClient(lambdaParams);

        console.log("Invoking generateDDQTableResponses Lambda");

        const invokeParams: InvokeCommandInput = {
          FunctionName: `generateDDQTableResponses-${env.LAMBDA_ENV}`,
          InvocationType: "RequestResponse",
          Payload: JSON.stringify({
            table_array: JSON.stringify(tablePayload),
            jumbo_chunks: JSON.stringify(chunks),
            data_mode: AnswerDataMode.JUMBO_CHUNKS,
            context,
            modelParameters,
            tracingContext: nestedTracingContext,
            secret,
          }),
        };

        try {
          const command = new InvokeCommand(invokeParams);
          const response = await lambdaClient.send(command);

          console.log("generateDDQTableResponses Lambda response", response);

          const jsonResponse = JSON.parse(
            Buffer.from(response.Payload ?? "").toString(),
          );

          if (jsonResponse.statusCode !== 200) {
            throw new Error(`HTTP error! status: ${jsonResponse.statusCode}`);
          }

          responseJson = jsonResponse.body.map(
            (r: {
              table_id: string;
              generated_table: string;
              reason: string;
              cells: {
                cellAddress: string;
                cellValue: string;
                reason: string;
              };
            }) => ({
              tableId: r.table_id,
              generatedTable: r.generated_table,
              reason: r.reason,
              cells: r.cells,
            }),
          );
        } catch (error) {
          console.error(
            `Error invoking generateDDQTableResponses-${env.LAMBDA_ENV} Lambda`,
            error,
          );
          throw error;
        }
      } else {
        console.log("Calling /pyrpc/generate-answer/generate_table_response");

        const response = await fetch(
          `${getPyrpcApiUrl()}/pyrpc/generate-answer/generate_table_response?data_mode=${getAnswerGenDataMode()}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${env.CHUNKING_API_SECRET ?? secret?.CHUNKING_API_SECRET}`,
            },
            body: JSON.stringify({
              table_array: JSON.stringify(tablePayload),
              jumbo_chunks: JSON.stringify(chunks),
              data_mode: AnswerDataMode.JUMBO_CHUNKS,
              context,
              modelParameters,
              tracingContext: nestedTracingContext,
            }),
          },
        );

        if (!response.ok || response.status !== 200 || !response.body) {
          if (response.status === 422) {
            console.log(
              "422 error, check validation error.",
              await response.json(),
            );

            return tablesInput.map((t) => ({
              tableId: t.tableId,
              generatedTable: "Virgil is unable to generate this table",
              reason: "Validation error",
              cells: [],
            }));
          }
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        responseJson = (await response.json()).map(
          (r: {
            table_id: string;
            generated_table: string;
            reason: string;
            cells: {
              cellAddress: string;
              cellValue: string;
              reason: string;
            };
          }) => ({
            tableId: r.table_id,
            generatedTable: r.generated_table,
            reason: r.reason,
            cells: r.cells,
          }),
        );
      }

      console.timeEnd(`generateDDQTableResponses-${tablesInput.length}`);

      span?.update({ output: responseJson });

      return responseJson.map((r) => ({
        ...r,
      }));
    } catch (error) {
      span?.update({ level: "ERROR", statusMessage: (error as Error).message });
      console.error(`Error generating DDQ Table Responses`, error);

      retries++;
      await backoffDelay(retries);
    } finally {
      if (retries >= MAX_RETRIES) {
        tracing?.end();
      }
    }
  }

  tracing?.end();

  console.error(
    `tableGenerator: Unable to generate DDQ table responses after ${MAX_RETRIES} retries`,
  );

  return tablesInput.map((t) => ({
    tableId: t.tableId,
    generatedTable: "Virgil is unable to generate this table",
    reason: "Unable to generate DDQ table responses",
    cells: [],
  }));
}
