// Filter out similar chunks using cosine similarity
// TODO: Check to see if sanitizing strings and doing a simple string comparison would be faster
import { VoyageAIEmbedding } from "~/lib/rag-response/utils/voyageai-embedding-openai-compat";
import {
  tracing,
  type TracingContext,
} from "~/server/api/managers/tracingManager";
import { type DocumentChunkType } from "~/lib/rag-response/steps/types";

export async function filterSimilarChunks(
  chunks: DocumentChunkType[],
  secret?: Record<string, string>,
  tracingContext?: TracingContext,
) {
  try {
    const filterSimilarSpan = tracing.trace({
      name: "filterSimilarChunks",
      input: {
        chunks,
      },
      tracingContext: tracingContext,
    });

    const embedder = new VoyageAIEmbedding(secret);

    // Get embeddings for all chunks
    const chunkTexts = chunks.map((chunk) => chunk.pageContent);
    const embeddings = await embedder.embedDocuments(chunkTexts);

    // Calculate cosine similarity between all pairs of chunks
    const similarityThreshold = 0.99; // Chunks with similarity above this will be considered duplicates
    const uniqueChunks: DocumentChunkType[] = [];
    const processedIndices = new Set<number>();

    for (let i = 0; i < chunks.length; i++) {
      if (processedIndices.has(i)) continue;

      const currentChunk = chunks[i];
      if (currentChunk) {
        uniqueChunks.push(currentChunk);
      }
      processedIndices.add(i);

      // Compare with remaining chunks
      for (let j = i + 1; j < chunks.length; j++) {
        if (processedIndices.has(j)) continue;

        const similarity = cosineSimilarity(embeddings[i]!, embeddings[j]!);
        if (similarity > similarityThreshold) {
          processedIndices.add(j);
        }
      }
    }

    filterSimilarSpan?.update({
      output: uniqueChunks,
    });

    return uniqueChunks;
  } catch (error) {
    console.error("filterSimilarChunks: Error filtering similar chunks", error);
    throw error;
  }
}

// Helper function to calculate cosine similarity between two vectors
export function cosineSimilarity(vecA: number[], vecB: number[]): number {
  const dotProduct = vecA.reduce((acc, val, i) => acc + val * vecB[i]!, 0);
  const normA = Math.sqrt(vecA.reduce((acc, val) => acc + val * val, 0));
  const normB = Math.sqrt(vecB.reduce((acc, val) => acc + val * val, 0));
  return dotProduct / (normA * normB);
}
