import {
  Question,
  QuestionContent,
  ResponseContent,
  AnswerGenerationType,
  ResponseStatus,
} from "@prisma/client";
import { VoyageAIEmbedding } from "./voyageai-embedding-openai-compat";
import { PrismaClientType } from "~/server/db";
import { QuestionContentType } from "~/lib/types";
import * as pgvector from "pgvector";
import { Prisma } from "@prisma/client";

export type QuestionWithContent = Question & {
  questionContents: QuestionContent[];
  responseContents: (ResponseContent & {
    status: ResponseStatus;
  })[]; // this is a hack - we need standardize types better
  distance?: number;
  originalQuestionId?: string;
  similarity?: number;
  documentName?: string;
};

export async function findSimilarQuestions(
  db: PrismaClientType,
  orgId: string,
  questions: QuestionWithContent[],
  secret?: Record<string, string>,
  excludeDocumentId?: string,
): Promise<QuestionWithContent[]> {
  try {
    const embedder = new VoyageAIEmbedding(secret);

    // Batch process all questions at once for better performance
    const questionTexts = questions
      .map(
        (question) =>
          (question.questionContents[0]?.content as QuestionContentType)
            ?.text ?? "",
      )
      .filter((text) => text.length > 0);

    if (questionTexts.length === 0) {
      return [];
    }

    // Generate embeddings for all questions in parallel
    const embeddings = await Promise.all(
      questionTexts.map((text) => embedder.embedQuery(text)),
    );

    // Convert embeddings to SQL format
    const sqlEmbeddings = embeddings.map(
      (embedding) => pgvector.toSql(embedding) as string,
    );

    // Build optimized query with proper indexing hints
    const similarityThreshold = Number(
      process.env.QUESTION_MATCHING_SIMILARITY_THRESHOLD ?? 0.3,
    );

    // Use a simpler approach with individual queries for each embedding
    const similarQuestionsPromises = sqlEmbeddings.map(
      async (embedding, index) => {
        const query = excludeDocumentId
          ? await db.$queryRaw<QuestionWithContent[]>`
            SELECT 
              q.id as question_id,
              qc.id as content_id,
              qc.content,
              q.type as question_type,
              q.category as question_category,
              q.index as question_index,
              q.group as question_group,
              q."answerTemplate",
              q.status as question_status,
              q."createdAt",
              q."updatedAt",
              q."responseId" as response_id,
              ${index} as embedding_index,
              (qc.vector <=> ${embedding}::vector) as distance,
              rc.id as response_content_id,
              rc.content as response_content,
              rc."createdAt" as response_content_created_at,
              rc."updatedAt" as response_content_updated_at,
              rc."answerGenerationType" as response_answer_generation_type,
              r.status as response_status,
              d.name as document_name
            FROM "Question" q
            INNER JOIN "QuestionContent" qc ON q.id = qc."questionId"
            INNER JOIN "Response" r ON q."responseId" = r.id
            LEFT JOIN "DocumentResponses" dr ON r.id = dr."responseId"
            LEFT JOIN "Document" d ON dr."documentId" = d.id
            LEFT JOIN "ResponseContent" rc ON r.id = rc."responseId"
            WHERE qc.vector IS NOT NULL
              AND q."orgId" = ${orgId}
              AND (qc.vector <=> ${embedding}::vector) < ${similarityThreshold}
              AND (dr."documentId" IS NULL OR dr."documentId" != ${excludeDocumentId})
              AND r.status = 'APPROVED'
            ORDER BY distance ASC
            LIMIT 3
          `
          : await db.$queryRaw<QuestionWithContent[]>`
            SELECT 
              q.id as question_id,
              qc.id as content_id,
              qc.content,
              q.type as question_type,
              q.category as question_category,
              q.index as question_index,
              q.group as question_group,
              q."answerTemplate",
              q.status as question_status,
              q."createdAt",
              q."updatedAt",
              q."responseId" as response_id,
              ${index} as embedding_index,
              (qc.vector <=> ${embedding}::vector) as distance,
              rc.id as response_content_id,
              rc.content as response_content,
              rc."createdAt" as response_content_created_at,
              rc."updatedAt" as response_content_updated_at,
              rc."answerGenerationType" as response_answer_generation_type,
              r.status as response_status,
              d.name as document_name
            FROM "Question" q
            INNER JOIN "QuestionContent" qc ON q.id = qc."questionId"
            INNER JOIN "Response" r ON q."responseId" = r.id
            LEFT JOIN "DocumentResponses" dr ON r.id = dr."responseId"
            LEFT JOIN "Document" d ON dr."documentId" = d.id
            LEFT JOIN "ResponseContent" rc ON r.id = rc."responseId"
            WHERE qc.vector IS NOT NULL
              AND q."orgId" = ${orgId}
              AND (qc.vector <=> ${embedding}::vector) < ${similarityThreshold}
              AND r.status = 'APPROVED'
            ORDER BY distance ASC
            LIMIT 3
          `;

        return query;
      },
    );

    const similarQuestionsResults = await Promise.all(similarQuestionsPromises);
    const similarQuestions = similarQuestionsResults.flat();

    // Map results back to expected format
    return similarQuestions.map((result: any) => ({
      id: result.question_id,
      type: result.question_type,
      category: result.question_category,
      index: result.question_index,
      group: result.question_group,
      answerTemplate: result.answerTemplate,
      status: result.question_status,
      orgId: orgId,
      createdById: "",
      responseId: result.response_id ?? "",
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      questionContents: [
        {
          id: result.content_id,
          content: result.content,
          vector: null,
          orgId: orgId,
          createdAt: result.createdAt,
          updatedAt: result.updatedAt,
          createdById: "",
          questionId: result.question_id,
        },
      ],
      responseContents: result.response_content_id
        ? [
            {
              id: result.response_content_id,
              content: result.response_content,
              orgId: orgId,
              createdAt: result.response_content_created_at || new Date(),
              updatedAt: result.response_content_updated_at || new Date(),
              responseId: result.response_id || "",
              createdById: result.createdById || "",
              status: result.response_status as ResponseStatus,
              answerGenerationType:
                result.response_answer_generation_type ||
                AnswerGenerationType.EXTRACTED,
            },
          ]
        : [],
      distance: result.distance,
      originalQuestionId: questions[result.embedding_index]?.id || "",
      documentName: result.document_name,
    }));
  } catch (error) {
    console.error(
      "findSimilarQuestions: Error finding similar questions",
      error,
    );
    throw error;
  }
}
