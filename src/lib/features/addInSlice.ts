import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export const WORD_HOST_TYPE = "word";
export const EXCEL_HOST_TYPE = "excel";
export type SupportedOfficeHostType =
  | typeof WORD_HOST_TYPE
  | typeof EXCEL_HOST_TYPE;

export type AddInNavigationPath =
  | "document-details"
  | "settings"
  | "collaboration"
  | "alerts"
  | "ddq"
  | "detailed-answer"
  | "document-status"
  | "document-feedback-status"
  | "initializing"
  | "excel-debug";

export type CellEdit = Excel.ChangedEventDetail & {
  range: string;
};

export interface AddInState {
  documentId: string;
  documentUrl: string;
  hostType: SupportedOfficeHostType | null;
  addInNavigationPath: AddInNavigationPath;
  backRoute: AddInNavigationPath | null;
  mode: "office" | "web";
  // Excel
  worksheets: string[];
  activeSheet: string;
  activeRangeAddress: string;
  cellChangedEventDetail: CellEdit | null;
  selectedCellValue: string;
  showStandardResponses: boolean;
}

const initialState: AddInState = {
  documentId: "",
  documentUrl: "",
  hostType: null,
  addInNavigationPath: "initializing",
  backRoute: null,
  mode: "office",
  // Excel
  worksheets: [],
  activeSheet: "",
  activeRangeAddress: "",
  cellChangedEventDetail: null,
  selectedCellValue: "",
  showStandardResponses: false,
};

const addInSlice = createSlice({
  name: "addIn",
  initialState,
  reducers: {
    // Common
    setDocumentId(state, action: PayloadAction<string>) {
      state.documentId = action.payload;
    },
    setDocumentUrl(state, action: PayloadAction<string>) {
      state.documentUrl = action.payload;
    },
    setAddInNavigationPath(state, action: PayloadAction<AddInNavigationPath>) {
      state.addInNavigationPath = action.payload;
    },
    setDisplayDetailedAnswer(state) {
      state.backRoute = state.addInNavigationPath;
      state.addInNavigationPath = "detailed-answer";
    },
    setDisplayIndexView(state) {
      state.backRoute = state.addInNavigationPath;
      state.addInNavigationPath = "ddq";
    },
    setBackRoute(state, action: PayloadAction<AddInNavigationPath>) {
      state.backRoute = action.payload;
    },
    navigateBack(state) {
      if (state.backRoute) {
        state.addInNavigationPath = state.backRoute;
        state.backRoute = null;
      }
    },
    setMode(state, action: PayloadAction<"office" | "web">) {
      state.mode = action.payload;
    },
    setHostType(state, action: PayloadAction<SupportedOfficeHostType | null>) {
      state.hostType = action.payload;
    },
    // Excel
    setWorksheets(state, action: PayloadAction<string[]>) {
      state.worksheets = action.payload;
    },
    setActiveSheet(state, action: PayloadAction<string>) {
      state.activeSheet = action.payload;
    },
    setActiveRangeAddress(state, action: PayloadAction<string>) {
      state.activeRangeAddress = action.payload;
    },
    setCellChangedEventDetail(state, action: PayloadAction<CellEdit | null>) {
      state.cellChangedEventDetail = action.payload;
    },
    setSelectedCellValue(state, action: PayloadAction<string>) {
      state.selectedCellValue = action.payload;
    },
    setShowStandardResponses(state, action: PayloadAction<boolean>) {
      state.showStandardResponses = action.payload;
    },
  },
});

export const {
  setDocumentId,
  setDocumentUrl,
  setAddInNavigationPath,
  setDisplayDetailedAnswer,
  setDisplayIndexView,
  navigateBack,
  setMode,
  setHostType,
  // Word
  setShowStandardResponses,
  // Excel
  setWorksheets,
  setActiveSheet,
  setActiveRangeAddress,
  setCellChangedEventDetail,
  setSelectedCellValue,
} = addInSlice.actions;

export default addInSlice.reducer;
