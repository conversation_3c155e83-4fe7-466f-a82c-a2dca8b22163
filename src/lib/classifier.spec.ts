import { Command } from "commander";
import { TracingContext, tracing } from "~/server/api/managers/tracingManager";
import { db } from "~/server/db";
import { documentCategoryClassifier } from "./classifier";

async function getUserOrg(email: string) {
  return await db.userOrg.findFirstOrThrow(
    true
      ? {
          where: {
            user: {
              email: email,
            },
          },
        }
      : void 0,
  );
}

async function main() {
  const program = new Command();

  program
    .name("classifier")
    .description("Process classifier")
    .option(
      "-u, --user-email <address>",
      "user email to imitate, if not provided, will use the first user in the database",
    )
    .version("1.0.0");

  program.parse();

  const options = program.opts();

  const { userId, orgId } = await getUserOrg(
    options.userEmail ?? "<EMAIL>",
  );

  const document = await db.document.findFirstOrThrow({
    where: {
      orgId: orgId,
    },
    select: {
      id: true,
      name: true,
    },
  });

  // Generate session and trace IDs
  const sessionId = tracing.generateSessionId();

  // Create a root trace for the classification
  const rootSpan = tracing.trace({
    name: "documentClassification",
    input: {
      orgId: orgId,
      userId: userId,
      documentId: document.id,
      documentName: document.name,
      userEmail: options.userEmail,
    },
    tracingContext: {
      sessionId,
    },
    userId: userId,
  });

  const tracingContext: TracingContext = {
    traceId: rootSpan?.id,
    sessionId,
  };

  try {
    const classificationResult = await documentCategoryClassifier({
      db,
      orgId: orgId,
      documentId: document.id,
      tracingContext,
    });

    console.log(`Document Request >>>`, {
      documentId: document.id,
      name: document.name,
    });

    console.log(`Analyze Response <<<`, classificationResult);

    // Update the root span with final results
    rootSpan?.update({
      output: {
        documentId: document.id,
        documentName: document.name,
        categories: classificationResult,
      },
    });

    console.log(`Successfully classified: ${document.name}`);
  } catch (error) {
    console.error(`Failed to classify ${document.name}:`, error);

    // Update the root span with error information
    rootSpan?.update({
      level: "ERROR",
      statusMessage: error instanceof Error ? error.message : String(error),
    });

    throw error;
  } finally {
    // Ensure tracing is properly flushed
    tracing.end();
  }
}

// Execute if run directly (not imported as module)
// if (require.main === module) {
main().catch(console.error);
// }
