name: Deploy <PERSON> (Manual)

on:
  workflow_dispatch:
  workflow_call:
  schedule:
    - cron: "15 3 * * *"

jobs:
  Deploy-Docker-Image:
    runs-on: ubuntu-latest
    env:
      ECR_HOST: ${{ secrets.HL_ECR_HOST }}
      ECR_PATH: ${{ secrets.HL_ECR_PATH }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@62f4f872db3836360b72999f4b87f1ff13310f3a

      - name: Deploy Docker Image
        id: build-docker-image
        run: |
          docker buildx build --platform=linux/amd64 -f infra/containers/pyrpc/Dockerfile -t ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}:latest infra/containers/pyrpc
          docker tag ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}:latest ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}:${{ github.sha }}
          docker push ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}:latest

      - name: Update Docker Lambda URI
        id: update-lambda-uri
        run: |
          IMAGE_DIGEST=$(aws ecr describe-images --repository-name ${{ env.ECR_PATH }} --query 'sort_by(imageDetails,& imagePushedAt)[*].imageDigest' --output yaml | tail -n 1 | awk -F'- ' '{print $2}')
          aws lambda update-function-code --function-name pyrpc-hl --image-uri ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}@${IMAGE_DIGEST}
          while ! aws lambda get-function --function-name pyrpc-hl | grep -q '"LastUpdateStatus": "Successful"'; do
            echo "Waiting for image to be ready..."
            sleep 5
          done
          VERSION_ID=$(aws lambda publish-version --function-name pyrpc-hl --query Version --output text)
          aws lambda update-alias --function-name pyrpc-hl --name latest-ver --function-version $VERSION_ID --no-cli-pager  --routing-config AdditionalVersionWeights={}

  Deploy-Hamilton-Lane:
    runs-on: ubuntu-latest
    env:
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.HL_VERCEL_PROJECT_ID }}
      DATABASE_URL: ${{ secrets.HL_DATABASE_URL }}
      NODE_OPTIONS: --max-old-space-size=16384
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
      - name: Install Vercel CLI
        run: npm install --global vercel@latest --legacy-peer-deps
      - name: Apply all pending migrations to the database
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.HL_DATABASE_URL }}
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }} --archive=tgz
