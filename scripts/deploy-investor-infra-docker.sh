#!/bin/sh

set -xe

# ECR_HOST=183295412412.dkr.ecr.us-east-1.amazonaws.com
# ECR_PATH=investor-infra/investor-infra-api

ECR_HOST=$1
ECR_PATH=$2

GIT_HASH=$(git log --pretty=format:'%h' -n 1)

aws ecr get-login-password --region us-east-1 |
    docker login --username AWS --password-stdin $ECR_HOST

docker buildx build --platform=linux/amd64 --provenance=false -f infra/containers/investor-infra/Dockerfile -t $ECR_HOST/$ECR_PATH:latest infra/containers/investor-infra
docker tag $ECR_HOST/$ECR_PATH:latest $ECR_HOST/$ECR_PATH:$GIT_HASH
docker push $ECR_HOST/$ECR_PATH:latest

IMAGE_DIGEST=$(aws ecr describe-images --repository-name $ECR_PATH --query 'sort_by(imageDetails,& imagePushedAt)[*].imageDigest' --output yaml | tail -n 1 | awk -F'- ' '{print $2}')
aws lambda update-function-code --function-name investor-infra-dev --image-uri $ECR_HOST/$ECR_PATH@$IMAGE_DIGEST --no-cli-pager

# wait until the image is ready
while ! aws lambda get-function --function-name investor-infra-dev | grep -q '"LastUpdateStatus": "Successful"'; do
  echo "Waiting for image to be ready..."
  sleep 5
done

VERSION_ID=$(aws lambda publish-version --function-name investor-infra-dev --query Version --output text)

# Update alias to point to the new version, set latest version weight to 100
aws lambda update-alias --function-name investor-infra-dev --name latest-ver --function-version $VERSION_ID --no-cli-pager  --routing-config AdditionalVersionWeights={}

# # Set reserved concurrency to 2 using the alias
# aws lambda put-provisioned-concurrency-config --function-name investor-infra-dev --qualifier dev-${VERSION_ID} --provisioned-concurrent-executions 2 --no-cli-pager

echo "Deployed to $ECR_HOST/$ECR_PATH:$GIT_HASH"
echo "Version: $VERSION_ID"
