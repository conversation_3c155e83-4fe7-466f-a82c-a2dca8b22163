#!/usr/bin/env node

// Simple DDQ generation script
const { faker } = require('@faker-js/faker');
const { Document, Packer, Paragraph, TextRun } = require('docx');
const fs = require('fs');
const path = require('path');

console.log('Starting DDQ generation...');

// Configuration
const TOTAL_FILES = 5000;
const QUESTIONS_PER_DDQ = 50;
const BASE_DIR = './fund-ddq-documents';

// Fund names (predefined for consistency)
const FUND_NAMES = [
    'Global Private Equity Partners', 'Strategic Venture Capital', 'Premium Hedge Fund Management',
    'Elite Real Estate Holdings', 'Alpha Infrastructure Group', 'Prime Growth Capital',
    'Meridian Distressed Debt Fund', 'Apex Credit Associates', 'Summit Energy Ventures',
    'Quantum Healthcare Investment', 'Pinnacle Technology Fund', 'Nexus Emerging Markets',
    'Vanguard Multi-Strategy', 'Legacy Buyout Partners', 'Capital Mezzanine Group',
    'Strategic Infrastructure Capital', 'Premium Technology Ventures', 'Elite Growth Partners',
    'Global Credit Management', 'Alpha Real Estate Fund', 'Prime Healthcare Capital',
    'Meridian Energy Investment', 'Apex Venture Partners', 'Summit Private Equity',
    'Quantum Infrastructure Fund'
];

const DDQ_CATEGORIES = ['Investment_Strategy', 'Operational_Due_Diligence', 'Legal_Compliance', 'Financial_Reporting'];

// Create directory structure
function createDirectoryStructure() {
    if (!fs.existsSync(BASE_DIR)) {
        fs.mkdirSync(BASE_DIR, { recursive: true });
    }

    const folders = [];
    
    FUND_NAMES.forEach((fundName, index) => {
        const fundFolder = path.join(BASE_DIR, `${(index + 1).toString().padStart(2, '0')}_${fundName.replace(/\s+/g, '_')}`);
        if (!fs.existsSync(fundFolder)) {
            fs.mkdirSync(fundFolder, { recursive: true });
        }
        
        DDQ_CATEGORIES.forEach(category => {
            const categoryFolder = path.join(fundFolder, category);
            if (!fs.existsSync(categoryFolder)) {
                fs.mkdirSync(categoryFolder, { recursive: true });
            }
            folders.push({
                path: categoryFolder,
                fund: fundName,
                category: category
            });
        });
    });
    
    return folders;
}

// Generate DDQ questions and answers
function generateDDQQuestions(count, category, fundName) {
    const questionsAndAnswers = [];
    
    const baseQuestions = {
        'Investment_Strategy': [
            `What is ${fundName}'s primary investment thesis?`,
            `How does ${fundName} identify potential investments?`,
            `What is the typical investment size range for ${fundName}?`,
            `What geographic markets does ${fundName} focus on?`,
            `How does ${fundName} approach sector diversification?`,
            `What is the expected holding period for investments?`,
            `How does ${fundName} create value in portfolio companies?`,
            `What are the key performance metrics used?`,
            `How does ${fundName} approach ESG considerations?`,
            `What is the approach to co-investment opportunities?`
        ],
        'Operational_Due_Diligence': [
            `Describe ${fundName}'s investment committee structure.`,
            `What is the background of the senior investment team?`,
            `How does ${fundName} manage conflicts of interest?`,
            `What operational infrastructure is in place?`,
            `How does ${fundName} approach risk management?`,
            `What is the approach to talent retention?`,
            `How does ${fundName} handle succession planning?`,
            `What technology systems are used?`,
            `How does ${fundName} ensure operational scalability?`,
            `What third-party service providers does ${fundName} work with?`
        ],
        'Legal_Compliance': [
            `What regulatory registrations does ${fundName} maintain?`,
            `How does ${fundName} ensure securities law compliance?`,
            `What is the approach to AML compliance?`,
            `How are investor onboarding and KYC handled?`,
            `What legal structure is used for funds?`,
            `How are regulatory reporting requirements managed?`,
            `What insurance coverage does ${fundName} maintain?`,
            `How is data privacy and cybersecurity handled?`,
            `What is the approach to managing regulatory changes?`,
            `How is compliance ensured across jurisdictions?`
        ],
        'Financial_Reporting': [
            `What is the approach to fund valuation procedures?`,
            `How frequently is performance reporting provided?`,
            `What accounting standards are followed?`,
            `Who serves as independent auditor and administrator?`,
            `How are management fees and carried interest calculated?`,
            `What is the approach to expense allocation?`,
            `How is foreign exchange risk handled?`,
            `What key performance indicators are tracked?`,
            `How is transparency ensured in reporting?`,
            `What is the approach to benchmarking?`
        ]
    };

    const questions = baseQuestions[category] || baseQuestions['Investment_Strategy'];
    
    for (let i = 0; i < count; i++) {
        const question = questions[i % questions.length];
        const answer = `${fundName} employs industry-leading practices in this area. Our approach focuses on ${faker.company.buzzPhrase().toLowerCase()} while maintaining ${faker.company.buzzNoun().toLowerCase()} standards. We have achieved ${faker.number.int({min: 80, max: 99})}% success rates and maintain ${faker.number.int({min: 90, max: 100})}% compliance across all relevant metrics. Our team of ${faker.number.int({min: 5, max: 25})} professionals ensures comprehensive coverage of all requirements.`;
        
        questionsAndAnswers.push({
            number: i + 1,
            question: question,
            answer: answer
        });
    }
    
    return questionsAndAnswers;
}

// Create DDQ document
async function createDDQDocument(questionsAndAnswers, filePath, fundName, category) {
    const categoryName = category.replace('_', ' ');
    
    const doc = new Document({
        sections: [{
            properties: {},
            children: [
                new Paragraph({
                    children: [
                        new TextRun({
                            text: "DILIGENCE QUESTIONNAIRE",
                            bold: true,
                            size: 36,
                        }),
                    ],
                }),
                new Paragraph({
                    children: [
                        new TextRun({
                            text: `Fund: ${fundName}`,
                            bold: true,
                            size: 28,
                        }),
                    ],
                }),
                new Paragraph({
                    children: [
                        new TextRun({
                            text: `Category: ${categoryName}`,
                            bold: true,
                            size: 24,
                        }),
                    ],
                }),
                new Paragraph({
                    children: [
                        new TextRun({
                            text: `Generated: ${new Date().toLocaleString()}`,
                            italics: true,
                            size: 20,
                        }),
                    ],
                }),
                new Paragraph({
                    children: [
                        new TextRun({
                            text: "",
                        }),
                    ],
                }),
                ...questionsAndAnswers.flatMap(qa => [
                    new Paragraph({
                        children: [
                            new TextRun({
                                text: `${qa.number}. ${qa.question}`,
                                bold: true,
                                size: 24,
                            }),
                        ],
                    }),
                    new Paragraph({
                        children: [
                            new TextRun({
                                text: `Answer: ${qa.answer}`,
                                size: 22,
                            }),
                        ],
                    }),
                    new Paragraph({
                        children: [
                            new TextRun({
                                text: "",
                            }),
                        ],
                    })
                ]),
            ],
        }],
    });

    const buffer = await Packer.toBuffer(doc);
    fs.writeFileSync(filePath, buffer);
}

// Main generation function
async function generateAllDDQFiles() {
    try {
        console.log('Creating directory structure...');
        const folders = createDirectoryStructure();
        
        console.log(`Generating ${TOTAL_FILES} DDQ files across ${folders.length} folders...`);
        
        const filesPerFolder = Math.ceil(TOTAL_FILES / folders.length);
        let fileCount = 0;
        
        for (let folderIndex = 0; folderIndex < folders.length && fileCount < TOTAL_FILES; folderIndex++) {
            const folderInfo = folders[folderIndex];
            const remainingFiles = TOTAL_FILES - fileCount;
            const filesToCreate = Math.min(filesPerFolder, remainingFiles);
            
            console.log(`Processing ${folderInfo.fund} - ${folderInfo.category} (${folderIndex + 1}/${folders.length})`);
            
            for (let i = 0; i < filesToCreate; i++) {
                const fileName = `DDQ_${folderInfo.category}_${(fileCount + 1).toString().padStart(4, '0')}.docx`;
                const filePath = path.join(folderInfo.path, fileName);
                
                const questionsAndAnswers = generateDDQQuestions(QUESTIONS_PER_DDQ, folderInfo.category, folderInfo.fund);
                await createDDQDocument(questionsAndAnswers, filePath, folderInfo.fund, folderInfo.category);
                
                fileCount++;
                
                if (fileCount % 100 === 0) {
                    console.log(`Generated ${fileCount}/${TOTAL_FILES} DDQ files...`);
                }
            }
        }
        
        console.log(`\n✅ Successfully generated ${fileCount} DDQ files!`);
        console.log(`📁 Files organized in: ${BASE_DIR}`);
        console.log(`🏢 Structure: ${FUND_NAMES.length} funds × ${DDQ_CATEGORIES.length} categories`);
        console.log(`📝 Each DDQ contains ${QUESTIONS_PER_DDQ} questions with answers`);
        
    } catch (error) {
        console.error('Error generating DDQ files:', error);
    }
}

// Run the generation
generateAllDDQFiles();