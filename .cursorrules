# Cursor Rules for Virgil AI App

## Project Overview

This is a Next.js application with TypeScript, tRPC, Prisma, and Tailwind CSS. The project includes:

- Frontend: Next.js 14+ with App Router
- Backend: tRPC API routes
- Database: Prisma ORM
- Styling: Tailwind CSS
- UI Components: Custom component library

## Code Style & Standards

### TypeScript

- Use strict TypeScript configuration
- Prefer explicit types over `any`
- Use proper interfaces and types from Prisma/Zod schemas
- Follow naming conventions: PascalCase for components, camelCase for functions/variables

### React/Next.js

- Use functional components with hooks
- Prefer Server Components when possible
- Use proper file structure: page.tsx for pages, layout.tsx for layouts
- Follow Next.js App Router conventions

### File Organization

- Components go in `src/components/`
- Pages go in `src/app/`
- Utilities go in `src/lib/` or `src/utils/`
- Types go in `src/types/`
- Views go in `src/views/`

### Import Rules

- Use absolute imports with `@/` prefix
- Group imports: external libraries first, then internal modules
- Use named imports when possible

### API & Database

- Use tRPC for API layer
- Use Prisma for database operations
- Follow established patterns in `src/server/`
- Use Zod schemas for validation (available in `prisma/zod/`)

### Styling

- Use Tailwind CSS classes
- Follow responsive design principles
- Use consistent spacing and color schemes
- Leverage existing theme configuration

## Best Practices

### Performance

- Optimize images and assets
- Use proper loading states
- Implement proper error boundaries
- Use React.memo() for expensive components when needed

### Security

- Validate all inputs using Zod schemas
- Use proper authentication checks
- Sanitize user inputs
- Follow security best practices for file uploads

## Project-Specific Guidelines

### DDQ (Diligence Questionnaire) Features

- Follow patterns established in existing DDQ components
- Use proper status enums and types
- Implement proper approval workflows

### Chat Features

- Use established chat patterns and components
- Implement proper real-time updates
- Follow conversation management patterns

### Document Management

- Use proper file upload/download patterns
- Implement proper categorization
- Follow document processing workflows

## Common Patterns

### Error Handling

- Use proper error boundaries
- Implement user-friendly error messages
- Log errors appropriately

### Loading States

- Use consistent loading indicators
- Implement skeleton screens where appropriate
- Handle async operations properly

### Forms

- Use React Hook Form with Zod validation
- Implement proper form state management
- Use consistent form styling

## Avoid

- Direct database queries in components
- Inline styles (use Tailwind classes)
- Hardcoded strings (use proper constants or i18n)
- Unused imports or variables
- console.log statements in production code

## When Making Changes

- Follow existing patterns and conventions
- Update types/schemas when modifying data structures
- Consider migration needs for database changes

## Icon Import

- Try to use Icon from 'lucide-react' library.

## Component Import

- Try to import "~/v2/components"
