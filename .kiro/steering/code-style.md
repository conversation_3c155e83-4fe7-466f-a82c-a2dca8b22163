<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:
------------------------------------------------------------------------------------->

Code styling:

- While implement new feature. Reference other places to understand coding style then following those styles. Don't create new styles if not needed.

- The folder to reference coding style is ./infra/containers/pyrpc

- Dont modify current code if not needed

- Clean/Remove test files if it will not be used

- Prefer place all files code in /investor_intelligence_agents
